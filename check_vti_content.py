import vtk
from vtk.util.numpy_support import vtk_to_numpy
import numpy as np
import os

# Fix numpy compatibility
if not hasattr(np, 'bool'):
    np.bool = bool

# Check what arrays are in the existing VTI files
slice_dir = 'mrxcat-2.0-main/outputData/test_male169/Tissue_properties_initial_frame/slice_730/'

vti_files = ['PD.vti', 'T1.vti', 'T2.vti', 'T2s.vti']

print("Checking VTI file contents:")
print("=" * 50)

for filename in vti_files:
    filepath = os.path.join(slice_dir, filename)
    if os.path.exists(filepath):
        print(f"\n📁 File: {filename}")
        print(f"   Size: {os.path.getsize(filepath)} bytes")
        
        try:
            reader = vtk.vtkXMLImageDataReader()
            reader.SetFileName(filepath)
            reader.Update()
            
            imageData = reader.GetOutput()
            
            # Check point data arrays
            pointData = imageData.GetPointData()
            print(f"   Point Data Arrays: {pointData.GetNumberOfArrays()}")
            
            for i in range(pointData.GetNumberOfArrays()):
                array = pointData.GetArray(i)
                array_name = array.GetName() if array.GetName() else f"Array_{i}"
                print(f"     [{i}] Name: '{array_name}', Size: {array.GetNumberOfTuples()}")
            
            # Check cell data arrays
            cellData = imageData.GetCellData()
            print(f"   Cell Data Arrays: {cellData.GetNumberOfArrays()}")
            
            for i in range(cellData.GetNumberOfArrays()):
                array = cellData.GetArray(i)
                array_name = array.GetName() if array.GetName() else f"Array_{i}"
                print(f"     [{i}] Name: '{array_name}', Size: {array.GetNumberOfTuples()}")
                
        except Exception as e:
            print(f"   ❌ Error reading file: {e}")
    else:
        print(f"❌ File not found: {filepath}")

print("\n" + "=" * 50)
print("Checking for Model.vti file...")
model_path = os.path.join(slice_dir, 'Model.vti')
if os.path.exists(model_path):
    print("✅ Model.vti exists!")
else:
    print("❌ Model.vti is missing!")
    print("   This is likely why the conversion is failing.") 