import vtk
from vtk.util.numpy_support import vtk_to_numpy
import numpy as np
import os

# Fix numpy compatibility
if not hasattr(np, 'bool'):
    np.bool = bool

def test_read_vti(filepath):
    print(f"\n🔍 Testing: {os.path.basename(filepath)}")
    print(f"   File size: {os.path.getsize(filepath)} bytes")
    
    try:
        # Method 1: Basic reader
        reader = vtk.vtkXMLImageDataReader()
        reader.SetFileName(filepath)
        reader.Update()
        
        imageData = reader.GetOutput()
        
        if imageData is None:
            print("   ❌ Failed to read imageData")
            return
            
        print(f"   ✅ Successfully read VTI file!")
        print(f"   Dimensions: {imageData.GetDimensions()}")
        print(f"   Spacing: {imageData.GetSpacing()}")
        print(f"   Origin: {imageData.GetOrigin()}")
        
        # Check point data
        pointData = imageData.GetPointData()
        print(f"   Point Data Arrays: {pointData.GetNumberOfArrays()}")
        
        for i in range(pointData.GetNumberOfArrays()):
            array = pointData.GetArray(i)
            if array:
                array_name = array.GetName() if array.GetName() else f"Array_{i}"
                print(f"     [{i}] '{array_name}': {array.GetNumberOfTuples()} elements")
                
                # Try to convert to numpy
                try:
                    np_array = vtk_to_numpy(array)
                    print(f"         Range: {np_array.min():.3f} to {np_array.max():.3f}")
                    print(f"         Unique values (first 10): {np.unique(np_array)[:10]}")
                except Exception as e:
                    print(f"         ❌ Failed to convert to numpy: {e}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error reading file: {e}")
        return False

# Test files
test_files = [
    "mrxcat-2.0-main/outputData/test_male169/slice_574/Model.vti",
    "mrxcat-2.0-main/outputData/test_male169/Tissue_properties_initial_frame/slice_574/PD.vti"
]

print("Testing VTI file reading:")
print("=" * 50)

for filepath in test_files:
    if os.path.exists(filepath):
        success = test_read_vti(filepath)
    else:
        print(f"\n❌ File not found: {filepath}") 