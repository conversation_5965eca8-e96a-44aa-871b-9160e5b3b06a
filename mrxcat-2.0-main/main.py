#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
主脚本

此脚本整合了所有模块，执行完整的MRI重建流程。
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.ndimage import gaussian_filter

# 导入自定义模块
from data_loader import load_cardiac_data, extract_axial_slice, resize_to_square, preprocess_image
from volume_transform import extract_oblique_slice, transform_modality_to_oblique_view, process_transformed_slice, reorganize_volume
from spiral_trajectory import generate_spiral_trajectory
from mri_reconstruction import (simulate_spiral_acquisition, apply_density_compensation,
                               create_sparse_kspace, enhance_kspace, balance_kspace_quadrants,
                               reconstruct_image, postprocess_image)
from visualization import (normalize_image, evaluate_reconstruction, plot_reconstruction_comparison,
                          plot_reconstruction_methods, plot_quadrant_analysis, plot_kspace_comparison,
                          plot_coverage_map, print_summary)

def main():
    # ===== 数据加载 =====
    # 心脏数据的路径
    cardiac_data_path = "/Users/<USER>/Documents/important document backup/Local python code/MRXCAT2/mrxcat-2.0-main/female50/cardiac_region_new/cardiac_volume.npz"

    # 加载所有参数图
    data = load_cardiac_data(cardiac_data_path)

    # 获取中间切片索引用于可视化
    z_slice = data['Labels'].shape[2] // 2
    print(f"Displaying slice {z_slice} of {data['Labels'].shape[2]}")

    # ===== 提取轴向切片 =====
    # 从T2数据中获取中间切片
    param_data_input = data['T2']
    best_slice = param_data_input.shape[0] // 2

    # 提取中间切片供以后使用
    middle_slice = extract_axial_slice(param_data_input, best_slice)
    print(f"Extracted middle slice (index {best_slice}) with shape: {middle_slice.shape}")

    # ===== 斜切面变换 =====
    # 从3D体积中提取斜切片
    oblique_slice = extract_oblique_slice(param_data_input, best_slice, offset=0, Lu=150, Lv=150, theta=-45)
    print(f"Extracted oblique slice with shape: {oblique_slice.shape}")

    # 将所有模态转换为斜视图
    print("Transforming all modalities to oblique view...")
    transformed_data = {}
    for modality in data.keys():
        print(f"  Transforming {modality}...", end='')
        transformed_data[modality] = transform_modality_to_oblique_view(data[modality])

    # 从ref_volume中提取斜切片
    ref_volume = transformed_data['T2']
    ref_slice_index = ref_volume.shape[0] // 2
    print("Oblique 3D volume stored in ref_volume with shape:", ref_volume.shape)
    print("Central oblique slice index:", ref_slice_index)

    # 从ref_volume中提取斜切片
    oblique_slice_ref = extract_oblique_slice(ref_volume, 51, offset=0, Lu=150, Lv=150, theta=-18)
    print(f"Extracted oblique slice from ref_volume with shape: {oblique_slice_ref.shape}")

    # 处理转换后的数据
    print("Processing transformed data...")
    processed_data = {}
    for modality in transformed_data.keys():
        processed_slice = process_transformed_slice(transformed_data, modality)
        processed_data[modality] = processed_slice
        print(f"Processed {modality} slice with shape: {processed_slice.shape}")

    # 重新组织所有体积
    print("Reorganizing all volumes...")
    reorganized_data = {}
    for key, volume in transformed_data.items():
        print(f"  Reorganizing {key}...", end='')
        reorganized_data[key] = reorganize_volume(volume)

    print("\nReorganization complete!")

    # 示例：显示原始vs重新组织的T2体积形状
    print(f"\nOriginal T2 shape: {transformed_data['T2'].shape}")
    print(f"Reorganized T2 shape: {reorganized_data['T2'].shape}")

    # ===== 螺旋重建 =====
    # 准备用于螺旋重建的图像
    image_size = 90  # 调整为更好地匹配GRE图像的可能大小
    # 使用原始转换后的数据，而不是重新组织后的数据
    # 这更接近原始代码的实现
    z_slice = transformed_data['T2star'].shape[0] // 2  # 使用中间切片
    print(f"Using slice {z_slice} from transformed data")
    print(f"Transformed T2star shape: {transformed_data['T2star'].shape}")

    # 使用process_transformed_slice函数处理切片
    # 这个函数会转置并翻转切片，与原始代码一致
    gre_t2star_image = process_transformed_slice(transformed_data, 'T2star', slice_idx=z_slice)
    print(f"After transformation: shape: {gre_t2star_image.shape}, min: {gre_t2star_image.min():.4f}, max: {gre_t2star_image.max():.4f}")

    # 裁剪图像以匹配原始代码中的处理
    # 在原始代码中，图像被裁剪为90x90的区域
    print("Cropping image to match original code...")
    h, w = gre_t2star_image.shape

    # 找到非零区域的中心
    non_zero_mask = gre_t2star_image > 0
    if np.any(non_zero_mask):
        y_indices, x_indices = np.where(non_zero_mask)
        center_y = int(np.mean(y_indices))
        center_x = int(np.mean(x_indices))
    else:
        center_y, center_x = h // 2, w // 2

    # 计算90x90区域的裁剪边界
    crop_size = 90
    half_size = crop_size // 2

    # 计算边界，确保它们在图像尺寸内
    row_min = max(0, center_y - half_size)
    row_max = min(h - 1, center_y + half_size - 1)
    col_min = max(0, center_x - half_size)
    col_max = min(w - 1, center_x + half_size - 1)

    # 调整如果裁剪区域小于期望大小
    if row_max - row_min + 1 < crop_size:
        if row_min == 0:
            row_max = min(h - 1, crop_size - 1)
        elif row_max == h - 1:
            row_min = max(0, h - crop_size)

    if col_max - col_min + 1 < crop_size:
        if col_min == 0:
            col_max = min(w - 1, crop_size - 1)
        elif col_max == w - 1:
            col_min = max(0, w - crop_size)

    # 裁剪图像
    gre_t2star_image_cropped = gre_t2star_image[row_min:row_min+crop_size, col_min:col_min+crop_size]
    print(f"Cropped image shape: {gre_t2star_image_cropped.shape}")
    print(f"Cropping bounds: rows [{row_min}:{row_min+crop_size}], cols [{col_min}:{col_min+crop_size}]")

    # 使用裁剪后的图像作为输入
    image_size = crop_size  # 设置图像大小为裁剪大小
    image_data = gre_t2star_image_cropped
    print(f"Final image shape: {image_data.shape}, min: {image_data.min():.4f}, max: {image_data.max():.4f}")

    # 应用预处理以提高图像质量
    image_data = preprocess_image(image_data, sigma=0.5, gamma=0.8)

    # ===== 螺旋轨迹生成 =====
    # 螺旋轨迹生成参数
    smax = 20000       # 最大转换率 (G/cm/s) 200T/m/s
    gmax = 6           # 最大梯度幅度 (G/cm) 40 mt/m
    T = 2e-6           # 采样周期 (s)
    N = 48             # 螺旋臂数量（增加到48以获得更高的覆盖率）
    Fcoeff = [192, -168]   # FOV = 18 cm（常数）或等效地 [19.2, -16.8]（cm单位）
    sf = 1
    rmax = sf * 1/(2*0.2)   # 2mm分辨率的最大k空间半径

    # 打印螺旋轨迹参数
    print(f"Spiral trajectory parameters:")
    print(f"  Number of interleaves: {N}")
    print(f"  Maximum slew rate: {smax} G/cm/s")
    print(f"  Maximum gradient: {gmax} G/cm")
    print(f"  Sampling period: {T*1e6} μs")
    print(f"  Maximum k-space radius: {rmax}")

    # 生成螺旋轨迹
    print("Variable Density Spiral Generation")
    # 使用原始采样周期以确保每个螺旋臂有足够的点
    T_original = 2e-6  # 原始采样周期
    kx_spiral, ky_spiral, g, s, time, r, theta = generate_spiral_trajectory(smax, gmax, T_original, N, Fcoeff, rmax)

    # ===== 模拟螺旋采集 =====
    # 模拟螺旋轨迹的MRI信号采集
    spiral_kspace, kx_pixel_float, ky_pixel_float, kx_pixel_int, ky_pixel_int = simulate_spiral_acquisition(
        image_data, kx_spiral, ky_spiral, image_size)

    # ===== 密度补偿和稀疏K空间创建 =====
    # 应用密度补偿以考虑非均匀采样
    spiral_kspace, density_comp = apply_density_compensation(
        kx_pixel_float, ky_pixel_float, spiral_kspace, image_size, N)

    # 创建用于重建的稀疏k空间表示
    sparse_kspace, coverage_mask = create_sparse_kspace(
        spiral_kspace, kx_pixel_int, ky_pixel_int, image_size)

    # ===== 增强K空间 =====
    # 增强k空间数据
    sparse_kspace, central_mask, central_coverage_before, central_coverage_after, dist_from_center = enhance_kspace(
        sparse_kspace, image_data, image_size)

    # 创建网格用于k空间滤波
    y_grid, x_grid = np.ogrid[:image_size, :image_size]
    center_y, center_x = image_size // 2, image_size // 2

    # 应用小模糊使螺旋模式在可视化中更加可见
    # （这仅用于可视化目的，不用于重建）
    blurred_viz = gaussian_filter(np.abs(sparse_kspace), sigma=1.5)

    # ===== 平衡K空间象限 =====
    # 平衡k空间四个象限的能量
    balanced_kspace = balance_kspace_quadrants(sparse_kspace)

    # ===== 图像重建 =====
    # 从处理后的k空间数据重建图像
    reconstructed_image, direct_quadrants, standard_quadrants = reconstruct_image(
        balanced_kspace, image_size)

    # 计算象限方差作为平衡度量
    direct_variance = np.var(direct_quadrants)
    standard_variance = np.var(standard_quadrants)

    # 获取直接和标准方法的幅度
    direct_magnitude = np.abs(np.fft.ifft2(balanced_kspace))
    standard_magnitude = np.abs(np.fft.fftshift(np.fft.ifft2(np.fft.ifftshift(balanced_kspace))))

    # ===== 图像后处理 =====
    # 对重建图像应用后处理以提高质量
    reconstructed_magnitude = postprocess_image(reconstructed_image)

    # ===== 质量评估 =====
    # 归一化图像以进行公平比较
    image_data_normalized = normalize_image(image_data)

    # 计算边缘图用于误差分析
    edge_x = np.abs(np.diff(reconstructed_magnitude, axis=0, prepend=0))
    edge_y = np.abs(np.diff(reconstructed_magnitude, axis=1, prepend=0))
    edge_magnitude = np.sqrt(edge_x**2 + edge_y**2)
    edge_magnitude = edge_magnitude / (edge_magnitude.max() + 1e-10)

    # 评估重建质量
    metrics, error_map = evaluate_reconstruction(
        image_data, reconstructed_magnitude, edge_magnitude)

    # ===== 结果可视化 =====
    # 绘制原始图像、重建图像和误差图的比较
    plot_reconstruction_comparison(
        image_data_normalized, reconstructed_magnitude, error_map, metrics)

    # 绘制不同重建方法的比较
    plot_reconstruction_methods(
        direct_magnitude, standard_magnitude, reconstructed_magnitude,
        image_data_normalized, direct_variance, standard_variance, metrics)

    # 绘制重建图像的象限分析
    plot_quadrant_analysis(reconstructed_magnitude)

    # 绘制k空间比较
    central_kspace = np.fft.fftshift(np.fft.fft2(image_data))
    k_filter = np.exp(-(dist_from_center / (image_size/2))**2 / 0.5)
    plot_kspace_comparison(central_kspace, sparse_kspace, k_filter)

    # 绘制k空间覆盖图
    coverage_percentage = plot_coverage_map(coverage_mask)

    # ===== 摘要 =====
    # 计算滤波效果
    filter_effect = 0.8  # 假设值，实际值应从enhance_kspace函数中获取

    # 打印重建摘要
    print_summary(
        image_size, N, kx_spiral, g, s, time, coverage_percentage,
        k_center_radius=image_size//8, central_coverage_after=central_coverage_after,
        metrics=metrics, filter_effect=filter_effect)

if __name__ == "__main__":
    main()
