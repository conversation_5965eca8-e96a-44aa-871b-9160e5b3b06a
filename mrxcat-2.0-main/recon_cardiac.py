#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MRXCAT Cardiac MRI Reconstruction

This script loads cardiac MRI parameter maps and performs spiral k-space trajectory
generation and image reconstruction.
"""

# ===== IMPORTS =====
import numpy as np
import os
import matplotlib.pyplot as plt
from scipy import ndimage
from scipy.interpolate import griddata, NearestNDInterpolator
from scipy.ndimage import gaussian_filter, zoom
import cv2

# Optional imports for image quality metrics
try:
    from skimage.metrics import structural_similarity as ssim
    from skimage.metrics import peak_signal_noise_ratio as psnr
except ImportError:
    print("Warning: skimage.metrics not available. Quality metrics will not be calculated.")
    ssim = lambda *args, **kwargs: 0
    psnr = lambda *args, **kwargs: 0

# ===== DATA LOADING =====
# Path to the saved cardiac data
cardiac_data_path = "/Users/<USER>/Documents/important document backup/Local python code/MRXCAT2/mrxcat-2.0-main/female50/cardiac_region_new/cardiac_volume.npz"

# Load all parameter maps
print("[DEBUG] Starting to load cardiac parameter maps...")
try:
    data = np.load(cardiac_data_path)
    print("[DEBUG] Successfully loaded cardiac data")
except Exception as e:
    print(f"[ERROR] Failed to load cardiac data: {e}")
    print(f"[DEBUG] Current working directory: {os.getcwd()}")
    print(f"[DEBUG] Checking if file exists: {os.path.exists(cardiac_data_path)}")
    raise

# Display available parameters
print(f"[DEBUG] Available parameters: {list(data.keys())}")
print(f"[DEBUG] Data shapes:")
for key in data.keys():
    print(f"  - {key}: {data[key].shape}")

# Get the middle slice index for visualization
z_slice = data['Labels'].shape[2] // 2
print(f"[DEBUG] Using middle slice {z_slice} of {data['Labels'].shape[2]}")

# Define parameter visualization settings
param_settings = {
    'PD': {'cmap': 'gray', 'title': 'Proton Density', 'vmin': None, 'vmax': None},
    'T1': {'cmap': 'hot', 'title': 'T1 (ms)', 'vmin': 0, 'vmax': 2000},
    'T2': {'cmap': 'viridis', 'title': 'T2 (ms)', 'vmin': 0, 'vmax': 150},
    'T2star': {'cmap': 'inferno', 'title': 'T2* (ms)', 'vmin': 0, 'vmax': 100},
    'T2star_plus': {'cmap': 'inferno', 'title': 'T2*+ (ms)', 'vmin': 0, 'vmax': 100},
    'Labels': {'cmap': 'tab10', 'title': 'Tissue Labels', 'vmin': None, 'vmax': None},
    'B0': {'cmap': 'jet', 'title': 'B0 Field (Hz)', 'vmin': -100, 'vmax': 100},
    'ShimmedB0': {'cmap': 'jet', 'title': 'Shimmed B0 Field (Hz)', 'vmin': -100, 'vmax': 100}
}

# ===== CARDIAC MASK CREATION =====
# Create a mask for cardiac structures for later use
if 'Labels' in data and 'B0' in data:
    cardiac_mask = np.zeros_like(data['Labels'][:,:,z_slice], dtype=bool)
    for label in [1, 2, 5, 6]:  # LV wall, RV wall, LV blood, RV blood
        cardiac_mask |= (data['Labels'][:,:,z_slice] == label)

    # Create a color-coded anatomy image for later use
    anatomy = np.zeros((*data['Labels'][:,:,z_slice].shape, 3))
    anatomy[data['Labels'][:,:,z_slice] == 1] = [1, 0, 0]  # LV wall - red
    anatomy[data['Labels'][:,:,z_slice] == 5] = [1, 0.7, 0.7]  # LV blood - light red
    anatomy[data['Labels'][:,:,z_slice] == 2] = [0, 0, 1]  # RV wall - blue
    anatomy[data['Labels'][:,:,z_slice] == 6] = [0.7, 0.7, 1]  # RV blood - light blue

    # Prepare B0 field data for later use
    b0_display = data['B0'][:,:,z_slice].copy()
    b0_display[~cardiac_mask] = np.nan  # Make non-cardiac regions transparent

# Function to extract an axial slice from a 3D volume
def extract_axial_slice(data_input, slice_idx):
    """
    Extract an axial slice from a 3D volume

    Parameters:
      data_input : numpy array, the 3D data (assumed shape [X, Y, Z])
      slice_idx  : int, axial slice index (along the first axis)

    Returns:
      slice_data : 2D numpy array, the extracted slice
    """
    return data_input[slice_idx, :, :].copy()

# Get a middle slice from the T2 data for processing
param_data_input = data['T2']
print(param_data_input.shape)  # Ensure the data is loaded correctly
best_slice = param_data_input.shape[0] // 2  # Get the middle slice index

# Extract the middle slice for later use
middle_slice = extract_axial_slice(param_data_input, best_slice)
print(f"Extracted middle slice (index {best_slice}) with shape: {middle_slice.shape}")

def extract_oblique_slice(data_input, best_slice, offset=0, Lu=200, Lv=100, theta=-45):
    """
    Extract a 2D oblique slice from a 3D volume.

    The new slicing plane is defined as follows:
      • Center at:  center = (best_slice, Y/2, X/2)  [using volume order (z,y,x)]
      • new_x axis: theta direction in the axial (y–x) plane:
                     new_x = (0, cos(theta), sin(theta))
      • new_y axis: Along the original z-axis:
                     new_y = (1, 0, 0)
      • new_z axis: Perpendicular, computed as:
                     new_z = cross(new_x, new_y)

    The 2D oblique slice is extracted from the volume via:
         original_coord = center + u * new_x + v * new_y + offset * new_z,
    where u ∈ [–Lu/2, Lu/2], v ∈ [–Lv/2, Lv/2].

    Parameters:
      data_input : 3D numpy array with shape (Z, Y, X)
      best_slice : int, the axial slice index to serve as center
      offset : float, displacement along new_z axis
      Lu : length (in pixels) along new_x (the oblique direction in the axial plane)
      Lv : length (in pixels) along new_y (the original z direction)
      theta : angle in degrees for the oblique direction in the axial plane

    Returns:
      oblique_slice : 2D numpy array, the extracted oblique slice
    """
    # Determine volume dimensions and center point
    Y, X = data_input.shape[1], data_input.shape[2]
    center = np.array([best_slice, Y/2, X/2])  # center = (z, y, x)

    # Define new coordinate directions
    theta_rad = np.deg2rad(theta)
    new_x = np.array([0, np.cos(theta_rad), np.sin(theta_rad)])  # e.g. (0, 0.7071, 0.7071)
    new_y = np.array([1, 0, 0])  # along the original z-axis
    new_z = np.cross(new_x, new_y)  # perpendicular to both

    # Create grid for new slice coordinates
    Lu_pixels = int(Lu)
    Lv_pixels = int(Lv)
    u = np.linspace(-Lu/2, Lu/2, Lu_pixels)
    v = np.linspace(-Lv/2, Lv/2, Lv_pixels)
    U, V = np.meshgrid(u, v)  # shape (Lv_pixels, Lu_pixels)

    # Compute original coordinates
    Z_coord = center[0] + V * new_y[0] + offset * new_z[0]  # new_y contributes to z; new_z[0] is 0
    Y_coord = center[1] + U * new_x[1] + offset * new_z[1]  # new_x[1] = cos(theta); new_z[1] = cos(theta)
    X_coord = center[2] + U * new_x[2] + offset * new_z[2]  # new_x[2] = sin(theta); new_z[2] = -sin(theta)
    coords = [Z_coord, Y_coord, X_coord]

    # Extract the oblique slice using interpolation
    oblique_slice = ndimage.map_coordinates(data_input, coords, order=1, mode='constant', cval=0)
    return oblique_slice

# Extract an oblique slice from the middle of the volume
oblique_slice = extract_oblique_slice(param_data_input, best_slice, offset=0, Lu=150, Lv=150, theta=-45)
print(f"Extracted oblique slice with shape: {oblique_slice.shape}")

# Parameters for re-slicing

# Parameters for re-slicing
best_slice = 60         # axial slice used as center
Lu = 150                # length along new_x (in pixels)
Lv = 150                # length along new_y (in pixels)
Lz = 101                 # number of slices along new_z (choose an odd number for a central slice)
# d will range from -D/2 to D/2, so that the central slice (d=0) becomes the reference.
D = 100                   # total span in new_z (in pixels), for example 6 pixels -> d from -3 to 3
fixed_offset = -7  # fixed offset for the central slice in new_z, can be set to 0 for the middle slice

# Volume dimensions and center (data_input is assumed defined)
Z, Y, X = param_data_input.shape
center = np.array([best_slice, Y/2, X/2])  # order: (z, y, x)

# Define new coordinate axes.
# For an oblique plane with new_x along -45° in axial (y,x) plane:
theta = -45
theta_rad = np.deg2rad(theta)
new_x = np.array([0, np.cos(theta_rad), np.sin(theta_rad)])  # (0, cosθ, sinθ)
new_y = np.array([1, 0, 0])  # along original z-axis
# We want new_z = (0, cosθ, -sinθ) as specified.
new_z = -np.cross(new_x, new_y)  # ensures new_z = (0, cosθ, -sinθ)

# Now, incorporate the fixed offset by shifting the center.
shifted_center = center + fixed_offset * new_z
center = shifted_center  # update center to account for the fixed offset

# Set up new resliced volume dimensions (new volume coordinate grid):
Lu_pixels = int(Lu)
Lv_pixels = int(Lv)
Lz_pixels = int(Lz)

# Create grids for new coordinates:
# u: along new_x, v: along new_y, d: along new_z.
u = np.linspace(-Lu/2, Lu/2, Lu_pixels)
v = np.linspace(-Lv/2, Lv/2, Lv_pixels)
d = np.linspace(-D/2, D/2, Lz_pixels)  # d coordinate

# Create a 3D meshgrid over (d, v, u)
# The order of axes for the new volume will be: new_z (d), new_y (v), new_x (u)
D_grid, V_grid, U_grid = np.meshgrid(d, v, u, indexing='ij')  # shape (Lz, Lv, Lu)

# Map new coordinates (d, v, u) to original coordinates:
# original = center + d*new_z + v*new_y + u*new_x
orig_Z = center[0] + D_grid * new_z[0] + V_grid * new_y[0] + U_grid * new_x[0]
orig_Y = center[1] + D_grid * new_z[1] + V_grid * new_y[1] + U_grid * new_x[1]
orig_X = center[2] + D_grid * new_z[2] + V_grid * new_y[2] + U_grid * new_x[2]

coords = [orig_Z, orig_Y, orig_X]

# Reslice the original volume using linear interpolation and zero-padding for out-of-bound values.
ref_volume = ndimage.map_coordinates(param_data_input, coords, order=1, mode='constant', cval=0)

# Determine the central slice index in the new volume (along d)
ref_slice_index = Lz_pixels // 2

print("Oblique 3D volume stored in ref_volume with shape:", ref_volume.shape)
print("Central oblique slice index:", ref_slice_index)

# Extract an oblique slice from the ref_volume
oblique_slice_ref = extract_oblique_slice(ref_volume, 51, offset=0, Lu=150, Lv=150, theta=-18)
print(f"Extracted oblique slice from ref_volume with shape: {oblique_slice_ref.shape}")

new_best_slice = 51         # axial slice used as center
Lu_new = 150                # length along new_x (in pixels)
Lv_new = 150                # length along new_y (in pixels)
Lz_new = 101                 # number of slices along new_z (choose an odd number for a central slice)
# d will range from -D/2 to D/2, so that the central slice (d=0) becomes the reference.
D_new = 100                   # total span in new_z (in pixels), for example 6 pixels -> d from -3 to 3
fixed_offset = 0  # fixed offset for the central slice in new_z, can be set to 0 for the middle slice
new_theta = -18
# Determine dimensions of the current ref_volume
Lz_old, Lv_old, Lu_old = ref_volume.shape
# Define new center in ref_volume coordinates (order: (d, v, u)):
center_new = np.array([new_best_slice, Lv_old/2, Lu_old/2])

# Define new coordinate axes for the re-slicing.
# Here we interpret the in-plane (v,u) axes as follows:
#   new_x_re: in-plane direction at new_theta (relative to horizontal axis in ref_volume)
#            (Note: In our convention, the ref_volume image is indexed as [v, u])
#   new_y_re: taken along the original d-axis.
#   new_z_re: defined as perpendicular (we want to shift around the plane normal).
#
# We embed the fixed_offset by shifting the center along new_z_re.
new_theta_rad = np.deg2rad(new_theta)
new_x_re = np.array([0, np.cos(new_theta_rad), np.sin(new_theta_rad)])   # no d component
new_y_re = np.array([1, 0, 0])  # along the original d-axis (slice axis of ref_volume)
# Define new_z_re as perpendicular to both:
new_z_re = -np.cross(new_x_re, new_y_re)  # This yields new_z_re = (0, cos(new_theta), -sin(new_theta))

# Now, incorporate the fixed offset by shifting the center.
shifted_center = center_new + fixed_offset * new_z_re

# Set up new re-sliced volume coordinate grid.
Lu_pixels = int(Lu_new)
Lv_pixels = int(Lv_new)
Lz_pixels = int(Lz_new)

# Create grids for local coordinates:
# u: along new_x_re, v: along new_y_re, d: along new_z_re.
u = np.linspace(-Lu_new/2, Lu_new/2, Lu_pixels)
v = np.linspace(-Lv_new/2, Lv_new/2, Lv_pixels)
d = np.linspace(-D_new/2, D_new/2, Lz_pixels)  # d coordinate in the new system

# Meshgrid: order (d, v, u)
D_grid, V_grid, U_grid = np.meshgrid(d, v, u, indexing='ij')

# Map new coordinates to ref_volume coordinates:
# For any point (d, v, u) in the new coordinate system:
#    coord = shifted_center + u * new_x_re + v * new_y_re + d * new_z_re
orig_d = shifted_center[0] + D_grid * new_z_re[0] + V_grid * new_y_re[0] + U_grid * new_x_re[0]
orig_v = shifted_center[1] + D_grid * new_z_re[1] + V_grid * new_y_re[1] + U_grid * new_x_re[1]
orig_u = shifted_center[2] + D_grid * new_z_re[2] + V_grid * new_y_re[2] + U_grid * new_x_re[2]

coords_new = [orig_d, orig_v, orig_u]

# Resample ref_volume using zero-padding (mode='constant', cval=0)
new_ref_volume = ndimage.map_coordinates(ref_volume, coords_new, order=1, mode='constant', cval=0)

# Determine the central slice index in new_ref_volume (expected to be at d=0)
central_slice_index = Lz_pixels // 2
print("New re-sliced 3D volume shape:", new_ref_volume.shape)
print("Central slice index in new volume:", central_slice_index)

# Determine the central slice index in the new volume (along d)
ref_slice_index = Lz_pixels // 2

# Extract the central slice from the new 3D oblique volume for later use
central_slice = new_ref_volume[50, :, :]
print(f"Central slice shape: {central_slice.shape}")

def transform_modality_to_oblique_view(volume):
    """
    Transform a 3D volume to the final oblique view using two sequential transformations.

    Parameters:
    -----------
    volume : 3D numpy array
        Input volume to transform (e.g., data['T2'], data['T1'])

    Returns:
    --------
    new_ref_volume : 3D numpy array
        Transformed volume in final oblique view
    """

    # ===== FIRST TRANSFORMATION (from cell 4) =====
    # Parameters for first transformation
    best_slice = 60
    Lu = 150
    Lv = 150
    Lz = 101
    D = 100
    fixed_offset = -7
    theta = -45

    # Get volume dimensions
    Z_in, Y_in, X_in = volume.shape

    # Define center in original volume coordinates
    center = np.array([best_slice, Y_in/2, X_in/2])

    # Define new coordinate axes
    theta_rad = np.deg2rad(theta)
    new_x = np.array([0, np.cos(theta_rad), np.sin(theta_rad)])
    new_y = np.array([1, 0, 0])
    new_z = -np.cross(new_x, new_y)

    # Incorporate fixed offset
    shifted_center = center + fixed_offset * new_z

    # Set up dimensions
    Lu_pixels = int(Lu)
    Lv_pixels = int(Lv)
    Lz_pixels = int(Lz)

    # Create grids for new coordinates
    u = np.linspace(-Lu/2, Lu/2, Lu_pixels)
    v = np.linspace(-Lv/2, Lv/2, Lv_pixels)
    d = np.linspace(-D/2, D/2, Lz_pixels)

    # Create meshgrid
    D_grid, V_grid, U_grid = np.meshgrid(d, v, u, indexing='ij')

    # Map coordinates from new to original volume
    orig_Z = shifted_center[0] + D_grid * new_z[0] + V_grid * new_y[0] + U_grid * new_x[0]
    orig_Y = shifted_center[1] + D_grid * new_z[1] + V_grid * new_y[1] + U_grid * new_x[1]
    orig_X = shifted_center[2] + D_grid * new_z[2] + V_grid * new_y[2] + U_grid * new_x[2]

    coords = [orig_Z, orig_Y, orig_X]

    # Reslice the original volume (first transformation)
    ref_volume = ndimage.map_coordinates(volume, coords, order=1, mode='constant', cval=0)

    # ===== SECOND TRANSFORMATION (from cell 5) =====
    # Parameters for second transformation
    new_best_slice = 51
    Lu_new = 150
    Lv_new = 150
    Lz_new = 101
    D_new = 100
    fixed_offset = 0
    new_theta = -18

    # Dimensions of the intermediate volume
    Lz_old, Lv_old, Lu_old = ref_volume.shape

    # Define center in intermediate volume coordinates
    center_new = np.array([new_best_slice, Lv_old/2, Lu_old/2])

    # Define new coordinate axes
    new_theta_rad = np.deg2rad(new_theta)
    new_x_re = np.array([0, np.cos(new_theta_rad), np.sin(new_theta_rad)])
    new_y_re = np.array([1, 0, 0])
    new_z_re = -np.cross(new_x_re, new_y_re)

    # Incorporate fixed offset
    shifted_center = center_new + fixed_offset * new_z_re

    # Set up dimensions
    Lu_pixels = int(Lu_new)
    Lv_pixels = int(Lv_new)
    Lz_pixels = int(Lz_new)

    # Create grids for local coordinates
    u = np.linspace(-Lu_new/2, Lu_new/2, Lu_pixels)
    v = np.linspace(-Lv_new/2, Lv_new/2, Lv_pixels)
    d = np.linspace(-D_new/2, D_new/2, Lz_pixels)

    # Meshgrid
    D_grid, V_grid, U_grid = np.meshgrid(d, v, u, indexing='ij')

    # Map coordinates
    orig_d = shifted_center[0] + D_grid * new_z_re[0] + V_grid * new_y_re[0] + U_grid * new_x_re[0]
    orig_v = shifted_center[1] + D_grid * new_z_re[1] + V_grid * new_y_re[1] + U_grid * new_x_re[1]
    orig_u = shifted_center[2] + D_grid * new_z_re[2] + V_grid * new_y_re[2] + U_grid * new_x_re[2]

    coords_new = [orig_d, orig_v, orig_u]

    # Resample the intermediate volume (second transformation)
    new_ref_volume = ndimage.map_coordinates(ref_volume, coords_new, order=1, mode='constant', cval=0)

    return new_ref_volume

# Example usage:
# Transform different modalities into the same view
transformed_T2 = transform_modality_to_oblique_view(data['T2'])
transformed_T1 = transform_modality_to_oblique_view(data['T1'])
transformed_PD = transform_modality_to_oblique_view(data['PD'])
transformed_B0 = transform_modality_to_oblique_view(data['B0'])
transformed_shimmedB0 = transform_modality_to_oblique_view(data['ShimmedB0'])
transformed_T2Star = transform_modality_to_oblique_view(data['T2star'])
transformed_T2Star_plus = transform_modality_to_oblique_view(data['T2star_plus'])
transformed_labels = transform_modality_to_oblique_view(data['Labels'])
# Verify the shapes of transformed volumes
print("Transformed volumes shapes:")
print("T2 shape:", transformed_T2.shape)
print("T1 shape:", transformed_T1.shape)
print("PD shape:", transformed_PD.shape)
print("B0 shape:", transformed_B0.shape)
print("ShimmedB0 shape:", transformed_shimmedB0.shape)
print("T2* shape:", transformed_T2Star.shape)
print("T2*+ shape:", transformed_T2Star_plus.shape)
print("Labels shape:", transformed_labels.shape)

def process_transformed_slice(transformed_data, modality, slice_idx=None):
    """
    Process a slice from transformed data by:
    1. Swapping x and y axes
    2. Flipping y-axis vertically (upside down)

    Parameters:
    -----------
    transformed_data : dict
        Dictionary containing all transformed modalities
    modality : str
        The modality to process (e.g., 'T2', 'T1')
    slice_idx : int or None
        The slice index to extract (if None, uses middle slice)

    Returns:
    --------
    processed_slice : 2D numpy array
        The processed slice with axes swapped and flipped
    """
    # If slice_idx is None, use the middle slice
    if slice_idx is None:
        slice_idx = transformed_data[modality].shape[0] // 2

    # Extract the slice
    central_slice = transformed_data[modality][slice_idx, :, :]

    # 1. Transpose the slice (swap x and y)
    # 2. Flip vertically (upside down)
    processed_slice = central_slice.T[::-1, :]

    return processed_slice

# Create a dictionary of transformed modalities
transformed_data = {
    'T1': transformed_T1,
    'T2': transformed_T2,
    'PD': transformed_PD,
    'B0': transformed_B0,
    'ShimmedB0': transformed_shimmedB0,
    'T2star': transformed_T2Star,
    'T2star_plus': transformed_T2Star_plus,
    'Labels': transformed_labels
}

# Process the transformed data
print("Processing transformed data...")
for modality in transformed_data.keys():
    processed_slice = process_transformed_slice(transformed_data, modality)
    print(f"Processed {modality} slice with shape: {processed_slice.shape}")

def reorganize_volume(volume):
    """
    Reorganize a 3D volume by:
    1. Keeping z-dimension (first dimension) unchanged
    2. Swapping x and y dimensions (transposing each slice)
    3. Flipping the new y-axis vertically

    Parameters:
    -----------
    volume : 3D numpy array with shape (z, y, x)

    Returns:
    --------
    reorganized : 3D numpy array with shape (z, x, y) and y-flipped
    """
    z_slices = volume.shape[0]
    reorganized = np.zeros((z_slices, volume.shape[2], volume.shape[1]))

    for z in range(z_slices):
        # Get the slice, transpose it, and flip y-axis
        reorganized[z, :, :] = volume[z, :, :].T[::-1, :]

    return reorganized

# Create a dictionary of reorganized transformed modalities
reorganized_data = {}

for key, volume in transformed_data.items():
    print(f"Reorganizing {key}...")
    reorganized_data[key] = reorganize_volume(volume)
    print(f"  Original shape: {volume.shape}, Reorganized shape: {reorganized_data[key].shape}")

print("\nReorganization complete!")

# Example: Show shape of original vs reorganized T2 volume
print(f"\nOriginal T2 shape: {transformed_data['T2'].shape}")
print(f"Reorganized T2 shape: {reorganized_data['T2'].shape}")

# Function to simulate MRI signals from tissue property maps
def simulate_mri_signal(pd_map, t1_map, t2_map, t2star_map, b0_map, TR, TE, flip_angle, sequence_type='spin_echo', use_shimmed_b0=False):
    """
    Generate synthetic MRI images from tissue property maps

    Parameters:
    -----------
    pd_map, t1_map, t2_map, t2star_map : 2D arrays
        Tissue property maps
    TR : float
        Repetition time in ms
    TE : float
        Echo time in ms
    flip_angle : float
        Flip angle in degrees
    sequence_type : str
        'spin_echo', 'gradient_echo', or 'bssfp'

    Returns:
    --------
    image : 2D array
        Simulated MR image
    """
    # Convert flip angle to radians if provided in degrees
    if flip_angle > 6.28:  # If larger than 2π, assume it's in degrees
        flip_angle = np.deg2rad(flip_angle)

    # Create image containers
    signal = np.zeros_like(pd_map, dtype=complex)

    if sequence_type.lower() == 'spin_echo':
        # T2-weighted spin echo: S = PD * (1-exp(-TR/T1)) * exp(-TE/T2)
        t1_factor = 1 - np.exp(-TR / (t1_map + 1e-6))
        t2_factor = np.exp(-TE / (t2_map + 1e-6))
        signal = pd_map * t1_factor * t2_factor

    elif sequence_type.lower() == 'gradient_echo':
        # Gradient echo: S = PD * sin(α) * (1-exp(-TR/T1)) / (1-cos(α)*exp(-TR/T1)) * exp(-TE/T2*)
        t1_factor = np.sin(flip_angle) * (1 - np.exp(-TR / (t1_map + 1e-6)))
        denominator = 1 - np.cos(flip_angle) * np.exp(-TR / (t1_map + 1e-6))
        t2star_factor = np.exp(-TE / (t2star_map + 1e-6))
        signal = pd_map * (t1_factor / (denominator + 1e-6)) * t2star_factor
        # In the gradient_echo case:
        # Use shimmed B0 field if specified
        if use_shimmed_b0:
            # Using shimmed B0 field for more realistic simulation
            print("Using shimmed B0 field for signal simulation")
            b0_phase = 2 * np.pi * b0_map * TE / 1000.0  # B0 in Hz, TE in ms
        else:
            # Using original B0 field
            b0_phase = 2 * np.pi * b0_map * TE / 1000.0  # B0 in Hz, TE in ms
        signal = signal * np.exp(1j * b0_phase)  # Add B0-induced phase

    elif sequence_type.lower() == 'bssfp':
        # Simplified bSSFP signal equation
        t1_t2_ratio = t1_map / (t2_map + 1e-6)
        signal = pd_map * np.sin(flip_angle) / ((t1_t2_ratio + 1) - np.cos(flip_angle) * (t1_t2_ratio - 1)) * np.exp(-TE/(t2_map + 1e-6))

    # Add some realistic phase variation
    phase_noise = np.random.normal(0, 0.1, pd_map.shape)  # Adjust standard deviation as needed
    signal = signal * np.exp(1j * phase_noise)
    return signal  # Return magnitude image

# Get a slice from the 3D volume
# Get a slice from the reorganized 3D volumes
z_slice = reorganized_data['T2'].shape[0] // 2 - 15  # Z is still the first dimension
print('z_lice:', z_slice)
# Extract maps from reorganized data
pd_map = reorganized_data['PD'][z_slice,:,:]
t1_map = reorganized_data['T1'][z_slice,:,:]
t2_map = reorganized_data['T2'][z_slice,:,:]
t2star_map = reorganized_data['T2star'][z_slice,:,:]
t2star_plus_map = reorganized_data['T2star_plus'][z_slice,:,:]
b0_map = reorganized_data['B0'][z_slice,:,:]
shimb0_map = reorganized_data['ShimmedB0'][z_slice,:,:]
print(f'size of b0_map: {b0_map.shape}')

# Check if Labels exists in reorganized_data (might depend on your previous processing)
if 'Labels' in reorganized_data:
    labels = reorganized_data['Labels'][z_slice,:,:]
else:
    print("Warning: No 'Labels' key found in reorganized_data.")
    # Create a placeholder or use a different approach
    labels = np.ones_like(t2_map)  # Default placeholder


# Crop the image to remove the padding area

# Get current map dimensions
h, w = pd_map.shape
print(f"Original shape: {pd_map.shape}")

# Find combined non-zero regions to determine center of interest
combined_mask = (pd_map != 0) | (t1_map != 0) | (t2_map != 0) | \
                (t2star_map != 0) | (t2star_plus_map != 0) | (b0_map != 0)

# Find the center of the non-zero region
y_indices, x_indices = np.where(combined_mask)
center_y = int(np.mean(y_indices))
center_x = int(np.mean(x_indices))

# Calculate crop boundaries for 100x100 region centered on the region of interest
crop_size = 90
half_size = crop_size // 2

# Calculate boundaries ensuring they stay within image dimensions
row_min = max(0, center_y - half_size)
row_max = min(h - 1, center_y + half_size - 1)
col_min = max(0, center_x - half_size)
col_max = min(w - 1, center_x + half_size - 1)

# Adjust if crop region is smaller than desired size
if row_max - row_min + 1 < crop_size:
    if row_min == 0:
        row_max = min(h - 1, crop_size - 1)
    elif row_max == h - 1:
        row_min = max(0, h - crop_size)

if col_max - col_min + 1 < crop_size:
    if col_min == 0:
        col_max = min(w - 1, crop_size - 1)
    elif col_max == w - 1:
        col_min = max(0, w - crop_size)

# Crop all maps to the 100x100 region
pd_map_cropped = pd_map[row_min:row_min+crop_size, col_min:col_min+crop_size]
t1_map_cropped = t1_map[row_min:row_min+crop_size, col_min:col_min+crop_size]
t2_map_cropped = t2_map[row_min:row_min+crop_size, col_min:col_min+crop_size]
t2star_map_cropped = t2star_map[row_min:row_min+crop_size, col_min:col_min+crop_size]
t2star_plus_map_cropped = t2star_plus_map[row_min:row_min+crop_size, col_min:col_min+crop_size]
b0_map_cropped = b0_map[row_min:row_min+crop_size, col_min:col_min+crop_size]
shimb0_map_cropped = shimb0_map[row_min:row_min+crop_size, col_min:col_min+crop_size]

# Also crop the labels map if it exists
if 'labels' in locals():
    labels_cropped = labels[row_min:row_min+crop_size, col_min:col_min+crop_size]

# Print size information
print(f"Fixed cropped shape: {pd_map_cropped.shape}")
print(f"Cropping bounds: rows [{row_min}:{row_min+crop_size}], cols [{col_min}:{col_min+crop_size}]")
print(f"Removed {pd_map.size - pd_map_cropped.size} pixels ({(1 - pd_map_cropped.size/pd_map.size)*100:.1f}% reduction)")

# Visualize the cropped region on one of the maps
plt.figure(figsize=(12, 5))

plt.subplot(1, 2, 1)
plt.imshow(t2_map, cmap='viridis')
plt.title("Original T2 Map")
plt.axhline(y=row_min, color='r', linestyle='--')
plt.axhline(y=row_min+crop_size, color='r', linestyle='--')
plt.axvline(x=col_min, color='r', linestyle='--')
plt.axvline(x=col_min+crop_size, color='r', linestyle='--')
plt.colorbar()

plt.subplot(1, 2, 2)
plt.imshow(t2_map_cropped, cmap='viridis')
plt.title("100×100 Cropped T2 Map")
plt.colorbar()

plt.tight_layout()
plt.show()

# Replace the original maps with fixed-size cropped versions
pd_map = pd_map_cropped
t1_map = t1_map_cropped
t2_map = t2_map_cropped
t2star_map = t2star_map_cropped
t2star_plus_map = t2star_plus_map_cropped
b0_map = b0_map_cropped
shimb0_map = shimb0_map_cropped
if 'labels' in locals():
    labels = labels_cropped

print(f"New size of b0_map: {b0_map.shape}")
print(f"All maps are now exactly 100×100 pixels")






# ===== MRI SIMULATION PARAMETERS =====
# Create mask for cardiac region
cardiac_mask = np.zeros_like(labels, dtype=bool)
for label in [1, 2, 5, 6]:  # LV wall, RV wall, LV blood, RV blood
    cardiac_mask |= (labels == label)

# Define sequence parameters
# Spin echo parameters
tr_se = 30      # Repetition time (ms)
te_se = 10      # Echo time (ms)
fa_se = 15      # Flip angle (degrees)

# Gradient echo parameters
tr_gre = 30     # Repetition time (ms)
te_gre = 25     # Echo time (ms) - more sensitive to T2* effects
fa_gre = 8      # Flip angle (degrees)

# Balanced SSFP parameters
tr_bssfp = 30   # Repetition time (ms)
te_bssfp = 25   # Echo time (ms)
fa_bssfp = 15   # Flip angle (degrees)

# Generate MR images
print("Simulating MR signals...")
spin_echo_complex = simulate_mri_signal(pd_map, t1_map, t2_map, t2star_plus_map, shimb0_map, tr_se, te_se, fa_se, 'gradient_echo', use_shimmed_b0=True)
gre_t2star_complex = simulate_mri_signal(pd_map, t1_map, t2_map, t2star_map, shimb0_map, tr_gre, te_gre, fa_gre, 'gradient_echo', use_shimmed_b0=True)
gre_t2star_plus_complex = simulate_mri_signal(pd_map, t1_map, t2_map, t2star_plus_map, shimb0_map, tr_gre, te_gre, fa_gre, 'gradient_echo', use_shimmed_b0=True)
bssfp_complex= simulate_mri_signal(pd_map, t1_map, t2_map, t2star_plus_map, shimb0_map, tr_bssfp, te_bssfp, fa_bssfp, 'gradient_echo', use_shimmed_b0=True)

# Generate k-space data using FFT
spin_echo_kspace = np.fft.fftshift(np.fft.fft2(spin_echo_complex))
gre_t2star_kspace = np.fft.fftshift(np.fft.fft2(gre_t2star_complex))
gre_t2star_plus_kspace = np.fft.fftshift(np.fft.fft2(gre_t2star_plus_complex))
bssfp_kspace = np.fft.fftshift(np.fft.fft2(bssfp_complex))

spin_echo_image = np.abs(spin_echo_complex)
gre_t2star_image = np.abs(gre_t2star_complex)
gre_t2star_plus_image = np.abs(gre_t2star_plus_complex)
bssfp_image = np.abs(bssfp_complex)

# Calculate difference map between T2* and T2*+ images
difference_map = gre_t2star_plus_image - gre_t2star_image
percent_difference = 100 * np.abs(difference_map) / (gre_t2star_image + 1e-6)

# Visualization
plt.figure(figsize=(20, 16))

# Row 1: Parameter maps (T2*, T2*+, B0)
plt.subplot(4, 4, 1)
plt.imshow(t2star_map, cmap='jet', vmin=0, vmax=60)
plt.title('T2* Map (ms)')
plt.colorbar()
plt.axis('off')

plt.subplot(4, 4, 2)
plt.imshow(t2star_plus_map, cmap='jet', vmin=0, vmax=60)
plt.title('T2*+ Map (ms)')
plt.colorbar()
plt.axis('off')

plt.subplot(4, 4, 3)
plt.imshow(b0_map, cmap='coolwarm', vmin=-50, vmax=50)
plt.title('B0 Field (Hz)')
plt.colorbar()
plt.axis('off')

plt.subplot(4, 4, 4)
plt.imshow(t2star_plus_map - t2star_map, cmap='coolwarm', vmin=-20, vmax=20)
plt.title('T2*+ - T2* Difference (ms)')
plt.colorbar()
plt.axis('off')

# Row 2: Simulated MR images
plt.subplot(4, 4, 5)
plt.imshow(spin_echo_image, cmap='gray')
plt.title(f'Spin Echo\nTR={tr_se}ms, TE={te_se}ms')
plt.axis('off')

plt.subplot(4, 4, 6)
plt.imshow(gre_t2star_image, cmap='gray')
plt.title(f'Gradient Echo (T2*)\nTR={tr_gre}ms, TE={te_gre}ms')
plt.axis('off')

plt.subplot(4, 4, 7)
plt.imshow(gre_t2star_plus_image, cmap='gray')
plt.title(f'Gradient Echo (T2*+)\nTR={tr_gre}ms, TE={te_gre}ms')
plt.axis('off')

plt.subplot(4, 4, 8)
plt.imshow(bssfp_image, cmap='gray')
plt.title(f'bSSFP\nTR={tr_bssfp}ms, TE={te_bssfp}ms')
plt.axis('off')

# Row 3: K-space representations (log scale for better visualization)
plt.subplot(4, 4, 9)
plt.imshow(np.log(np.abs(spin_echo_kspace) + 1e-10), cmap='gray')
plt.title('Spin Echo k-space')
plt.axis('off')

plt.subplot(4, 4, 10)
plt.imshow(np.log(np.abs(gre_t2star_kspace) + 1e-10), cmap='gray')
plt.title('Gradient Echo (T2*) k-space')
plt.axis('off')

plt.subplot(4, 4, 11)
plt.imshow(np.log(np.abs(gre_t2star_plus_kspace) + 1e-10), cmap='gray')
plt.title('Gradient Echo (T2*+) k-space')
plt.axis('off')

plt.subplot(4, 4, 12)
plt.imshow(np.log(np.abs(bssfp_kspace) + 1e-10), cmap='gray')
plt.title('bSSFP k-space')
plt.axis('off')

# Row 4: Focus on T2* vs T2*+ comparison
plt.subplot(4, 4, 13)
# Create cardiac region mask overlay
overlay = np.zeros((*cardiac_mask.shape, 3))
for label, color in [(1, [1, 0, 0]),     # LV wall - red
                     (2, [0, 0, 1]),     # RV wall - blue
                     (5, [1, 0.7, 0.7]), # LV blood - light red
                     (6, [0.7, 0.7, 1])]: # RV blood - light blue
    overlay[labels == label] = color
plt.imshow(overlay)
plt.title('Cardiac Region')
plt.axis('off')

plt.subplot(4, 4, 14)
plt.imshow(difference_map, cmap='coolwarm', vmin=-0.1, vmax=0.1)
plt.title('GRE Signal Difference\n(T2*+ - T2*)')
plt.colorbar()
plt.axis('off')

plt.subplot(4, 4, 15)
# Apply cardiac mask to the percent difference
masked_percent_diff = percent_difference.copy()
masked_percent_diff[~cardiac_mask] = np.nan  # Make non-cardiac regions transparent
plt.imshow(masked_percent_diff, cmap='hot', vmin=0, vmax=20)
plt.title('% Difference in Cardiac Region')
plt.colorbar(label='%')
plt.axis('off')

plt.subplot(4, 4, 16)
# Zoomed view of the difference in cardiac region
plt.imshow(difference_map * cardiac_mask, cmap='coolwarm', vmin=-0.1, vmax=0.1)
plt.title('Cardiac Signal Difference\n(T2*+ - T2*)')
plt.colorbar()
plt.axis('off')

plt.tight_layout()
plt.show()

# Show a detailed comparison of T2* vs T2*+ in gradient echo
plt.figure(figsize=(18, 6))

# Find cardiac center for zooming
if np.any(cardiac_mask):
    y_indices, x_indices = np.where(cardiac_mask)
    center_y, center_x = int(np.mean(y_indices)), int(np.mean(x_indices))
else:
    center_y, center_x = labels.shape[0] // 2, labels.shape[1] // 2

# Define zoom region
zoom_size = 50
zoom_y_min = max(0, center_y - zoom_size)
zoom_y_max = min(labels.shape[0], center_y + zoom_size)
zoom_x_min = max(0, center_x - zoom_size)
zoom_x_max = min(labels.shape[1], center_x + zoom_size)

# Original GRE images
plt.subplot(2, 3, 1)
plt.imshow(gre_t2star_image, cmap='gray')
plt.title('GRE with T2*')
plt.gca().add_patch(plt.Rectangle((zoom_x_min, zoom_y_min),
                                 zoom_x_max-zoom_x_min, zoom_y_max-zoom_y_min,
                                 edgecolor='r', facecolor='none', linewidth=2))
plt.axis('off')

plt.subplot(2, 3, 2)
plt.imshow(gre_t2star_plus_image, cmap='gray')
plt.title('GRE with T2*+\n(includes B0 effects)')
plt.gca().add_patch(plt.Rectangle((zoom_x_min, zoom_y_min),
                                 zoom_x_max-zoom_x_min, zoom_y_max-zoom_y_min,
                                 edgecolor='r', facecolor='none', linewidth=2))
plt.axis('off')

plt.subplot(2, 3, 3)
plt.imshow(difference_map, cmap='coolwarm', vmin=-0.1, vmax=0.1)
plt.title('Difference (T2*+ - T2*)')
plt.gca().add_patch(plt.Rectangle((zoom_x_min, zoom_y_min),
                                 zoom_x_max-zoom_x_min, zoom_y_max-zoom_y_min,
                                 edgecolor='r', facecolor='none', linewidth=2))
plt.colorbar()
plt.axis('off')

def qdf(a, b, c):
    """
    Solves quadratic equation ax^2 + bx + c = 0

    Returns:
        Roots of the quadratic equation
    """
    d = b**2 - 4*a*c
    if d < 0:
        # Handle negative discriminant case
        d = complex(d, 0)
    root1 = (-b + np.sqrt(d)) / (2*a)
    root2 = (-b - np.sqrt(d)) / (2*a)
    return np.array([root1, root2])

def findq2r2(smax, gmax, r, r1, T, Ts, N, Fcoeff, rmax, z=0):
    """
    Calculates the second derivatives of r and theta (q) that satisfy hardware and FOV constraints

    Parameters:
        smax: Maximum slew rate in G/cm/s
        gmax: Maximum gradient amplitude in G/cm
        r: Current value of the k-space radius
        r1: Current derivative of r
        T: Gradient sample period
        Ts: Data sampling period
        N: Number of spiral interleaves
        Fcoeff: FOV coefficients for variable density
        rmax: Maximum k-space radius
        z: R/L for gradient coil (voltage model parameter)

    Returns:
        q2: Second derivative of angle theta
        r2: Second derivative of radius r
    """
    gamma = 4258  # Hz/G

    smax = smax + z*gmax

    # # Calculate FOV and its derivative for current r
    F = 0
    dFdr = 0
    for rind in range(len(Fcoeff)):
        F += Fcoeff[rind] * (r/rmax)**(rind)
        if rind > 0:
            dFdr += rind * Fcoeff[rind] * (r/rmax)**(rind-1) / rmax

    # # --- VARIABLE DENSITY IMPLEMENTATION ---
    # # Calculate oversampling factor: 8.0 at center, linearly decreasing to 1.0 at kmax
    # if rmax > 0:
    #     relative_r = min(r / rmax, 1.0)  # Clip to [0, 1]
    # else:
    #     relative_r = 0.0

    # # Linear oversampling profile: OS(r) = 8.0 - 7.0 * (r/rmax)
    # # OS_r = 8.0 - 7.0 * relative_r
    # OS_r = 7.0 * relative_r
    # OS_r = max(1.0, OS_r)  # Ensure minimum 1.0 oversampling
    # print(f'oversampling ratio: {OS_r}')

    # # Base FOV from Fcoeff[0]
    # F0 = Fcoeff[0]

    # # Calculate effective FOV and its derivative
    # F = F0 / OS_r

    # # Calculate derivative: d/dr[F0/(8-7r/rmax)]
    # if rmax > 0 and r < rmax:
    #     dFdr = 7.0 * F0 / (rmax * OS_r**2)
    # else:
    #     dFdr = 0.0

    # FOV limit on gradient
    GmaxFOV = N/gamma / F / Ts
    if not hasattr(findq2r2, "printed"):
        print(f'Required GmaxFOV: {GmaxFOV}')
        findq2r2.printed = True
    Gmax = min(GmaxFOV, gmax)
    # Gmax = gmax

    # Maximum allowed r1 based on gradient amplitude limit
    maxr1 = np.sqrt((gamma*Gmax)**2 / (1 + (2*np.pi*F*r/N)**2))

    if r1 > maxr1:
        # Gradient amplitude limited case
        r2 = (maxr1 - r1) / T
    else:
        # Slew rate limited case
        twopiFoN = 2*np.pi*F/N
        twopiFoN2 = twopiFoN**2

        # Coefficients for the quadratic equation in r2
        A = 1 + twopiFoN2*r*r
        B = 2*twopiFoN2*r*r1*r1 + 2*twopiFoN2/F*dFdr*r*r*r1*r1 + 2*z*r1 + 2*twopiFoN2*r1*r
        C1 = twopiFoN2**2*r*r*r1**4 + 4*twopiFoN2*r1**4 + (2*np.pi/N*dFdr)**2*r*r*r1**4 + 4*twopiFoN2/F*dFdr*r*r1**4 - (gamma)**2*smax**2
        C2 = z*(z*r1**2 + z*twopiFoN2*r1**2 + 2*twopiFoN2*r1**3*r + 2*twopiFoN2/F*dFdr*r1**3*r)
        C = C1 + C2

        # Solve quadratic equation
        rts = qdf(A, B, C)
        r2 = np.real(rts[0])  # Use first root

        # Calculate and check resulting slew rate
        slew = 1/gamma * (r2 - twopiFoN2*r*r1**2 + 1j*twopiFoN*(2*r1**2 + r*r2 + dFdr/F*r*r1**2))
        sr = np.abs(slew)/smax

        if sr > 1.01:
            print(f"Slew violation, slew = {round(np.abs(slew))}, smax = {round(smax)}, sr={sr:.3f}, r={r:.3f}, r1={r1:.3f}")

    # Calculate q2 from other parameters
    q2 = 2*np.pi/N*dFdr*r1**2 + 2*np.pi*F/N*r2

    return q2, r2

def vds(smax, gmax, T, N, Fcoeff, rmax, z=0):
    """
    Variable Density Spiral trajectory generation

    Parameters:
        smax: Maximum slew rate G/cm/s
        gmax: Maximum gradient G/cm
        T: Sampling period (s)
        N: Number of interleaves
        Fcoeff: FOV coefficients - FOV(r) = Sum_k Fcoeff[k]*(r/rmax)^k
        rmax: Maximum k-space radius (cm^-1)
        z: R/L for gradient coil model

    Returns:
        k: k-space trajectory (kx+iky) in cm^-1
        g: gradient waveform (Gx+iGy) in G/cm
        s: derivative of g (Sx+iSy) in G/cm/s
        time: time points corresponding to trajectory (s)
        r: k-space radius vs time
        theta: angle vs time
    """
    print('Variable Density Spiral Generation')
    gamma = 4258  # Hz/G

    # Oversampling for trajectory calculation
    oversamp = 8  # Keep this even
    To = T / oversamp  # Oversampled period

    # Initialize variables
    q0 = 0
    q1 = 0
    r0 = 0
    r1 = 0
    t = 0
    count = 0

    # Pre-allocate arrays (can extend later if needed)
    max_points = 10000000
    theta = np.zeros(max_points)
    r = np.zeros(max_points)
    time = np.zeros(max_points)

    # Main loop to generate trajectory
    while r0 < rmax:
        # Get the next point on the trajectory
        q2, r2 = findq2r2(smax, gmax, r0, r1, To, T, N, Fcoeff, rmax, z)

        # Integrate for θ, θ', r, and r'
        q1 = q1 + q2 * To
        q0 = q0 + q1 * To
        t = t + To

        r1 = r1 + r2 * To
        r0 = r0 + r1 * To

        # Store values
        count += 1
        theta[count] = q0
        r[count] = r0
        time[count] = t

        if count % 10000 == 0:
            print(f'{count} points, |k|={r0:.6f}')

        # Break if we've reached array limit
        if count >= max_points - 1:
            print("Warning: reached maximum array size")
            break

    # Trim arrays to used size
    theta = theta[:count+1]
    r = r[:count+1]
    time = time[:count+1]

    # Downsample to original sampling rate
    theta_ds = theta[oversamp//2::oversamp]
    r_ds = r[oversamp//2::oversamp]
    time_ds = time[oversamp//2::oversamp]

    # Keep the length a multiple of 4 (to match original code)
    length = 4 * (len(theta_ds) // 4)
    theta_ds = theta_ds[:length]
    r_ds = r_ds[:length]
    time_ds = time_ds[:length]

    # Calculate k-space trajectory, gradients, and slew rates
    k = r_ds * np.exp(1j * theta_ds)

    # Calculate gradients
    g = np.zeros_like(k, dtype=complex)
    g[:-1] = (k[1:] - k[:-1]) / T / gamma
    g[-1] = g[-2]  # Extrapolate last point

    # Calculate slew rates
    s = np.zeros_like(g, dtype=complex)
    s[:-1] = (g[1:] - g[:-1]) / T
    s[-1] = s[-2]  # Extrapolate last point

    # Debug information about trajectory
    print(f"Trajectory summary:")
    print(f"  k-space points: {len(k)}")
    print(f"  Max gradient: {np.max(np.abs(g)):.3f} G/cm")
    print(f"  Max slew rate: {np.max(np.abs(s)):.3f} G/cm/s")
    print(f"  Readout time: {time_ds[-1]*1000:.2f} ms")

    return k, g, s, time_ds, r_ds, theta_ds

# ===== SPIRAL TRAJECTORY GENERATION =====
# Parameters for spiral trajectory generation
smax = 20000       # Maximum slew rate (G/cm/s) 200T/m/s
gmax = 4           # Maximum gradient amplitude (G/cm) 40 mt/m
T = 4e-6           # Sampling period (s)
N = 1              # Number of interleaves
Fcoeff = [18, 0]   # FOV = 18 cm (constant)
sf = 1.3
rmax = sf * 1/(2*0.2)   # Maximum k-space radius for 2mm resolution

# Print spiral trajectory parameters
print(f"Spiral trajectory parameters:")
print(f"  Number of interleaves: {N}")
print(f"  Maximum slew rate: {smax} G/cm/s")
print(f"  Maximum gradient: {gmax} G/cm")
print(f"  Sampling period: {T*1e6} μs")
print(f"  Maximum k-space radius: {rmax}")

# Example with variable density
# FOV = 24 - 4*(r/rmax) cm (decreases from 24cm to 20cm)
# Fcoeff = [24, -4]


# Generate the spiral trajectory
print("Variable Density Spiral Generation")
k, g, s, time, r, theta = vds(smax, gmax, T, N, Fcoeff, rmax)
print(f'matrix size of kspace: {k.shape}')
print(f'maximum radial distance from kspace center: {r.max()}')
print(f'gradient magnitude maximum value: {abs(g.max())}')
print(f'slew rate maximum value: {abs(s.max())}')
print(f'time shape: {time.shape}')
print(f'minimum read out time: {time[-1]*1000} ms')

# Imports removed - using global imports

# Use gre_t2star_plus_image as input
print(f"Using GRE T2*+ image for spiral simulation, shape: {gre_t2star_complex.shape}")

# Create a correctly sized square image
def resize_to_square(image, target_size=500):
    """Resizes an image to a square without distortion"""
    h, w = image.shape

    # If already the right size
    if h == target_size and w == target_size:
        return image

    # If different dimensions, crop or pad
    if h != w or h != target_size:
        # Create empty square canvas
        result = np.zeros((target_size, target_size))

        # Scale down if needed
        if h > target_size or w > target_size:
            scale = target_size / max(h, w)
            new_h, new_w = int(h * scale), int(w * scale)
            # Use zoom from scipy.ndimage (already imported at the top)
            resized = zoom(image, (new_h/h, new_w/w))
            h, w = new_h, new_w
        else:
            resized = image

        # Center image in canvas
        y_offset = (target_size - h) // 2
        x_offset = (target_size - w) // 2

        # Place image in center
        result[y_offset:y_offset+h, x_offset:x_offset+w] = resized
        return result

    return image

# ===== SPIRAL RECONSTRUCTION =====
# Prepare image for spiral reconstruction
# Keep the same size as the original data to avoid interpolation issues
image_size = 90  # Keep the original size to match k-space data
image_data = resize_to_square(gre_t2star_image, target_size=image_size)
# Verify dimensions
print(f"Resized image shape: {image_data.shape}")

# Apply preprocessing to improve image quality
# 1. Apply mild denoising to input image
print("Applying preprocessing to input image...")
print(f"  Original image range: [{image_data.min():.4f}, {image_data.max():.4f}]")
image_data = gaussian_filter(image_data, sigma=0.5)
print(f"  After denoising: [{image_data.min():.4f}, {image_data.max():.4f}]")

# 2. Enhance contrast of input image
image_data = np.power((image_data - image_data.min()) / (image_data.max() - image_data.min()), 0.8) * \
             (image_data.max() - image_data.min()) + image_data.min()
print(f"  After contrast enhancement: [{image_data.min():.4f}, {image_data.max():.4f}]")

# Use k-space data from the GRE T2* image
kspace = gre_t2star_kspace
print(f"Using k-space data for spiral simulation, shape: {kspace.shape}")

# Extract kx and ky components from complex k-space trajectory
kx = np.real(k)  # Real part of k (x-component)
ky = np.imag(k)  # Imaginary part of k (y-component)

# Get maximum k-space radius
k_max = r.max() /sf # Using the r variable already calculated in vds function

# Imports removed - using global imports

kx_spiral = kx
ky_spiral = ky

# --- MODIFICATION START ---
# Calculate FLOATING POINT pixel coordinates first
kx_pixel_float = ((kx_spiral / k_max) * (image_size/2) + image_size/2)
ky_pixel_float = (ky_spiral / k_max) * (image_size/2) + image_size/2

# Convert to INTEGER coordinates for visualization and sparse grid indexing later
kx_pixel_int = ((kx_spiral / k_max) * (image_size/2) + image_size/2).astype(int)
ky_pixel_int = ((ky_spiral / k_max) * (image_size/2) + image_size/2).astype(int)
# --- MODIFICATION END ---

# ===== SPIRAL TRAJECTORY ANALYSIS =====
# Create theta_vis for later use
theta_vis = np.linspace(0, 2*np.pi, 100)

# Debug information about spiral trajectory
print(f"Spiral trajectory debug info:")
print(f"  Spiral points: {len(kx_spiral)}")
print(f"  k_max: {k_max:.3f}")
print(f"  Image size: {image_size}")
print(f"  Number of interleaves: {N}")
print(f"  Maximum gradient: {np.max(np.abs(g)):.3f} G/cm")
print(f"  Maximum slew rate: {np.max(np.abs(s)):.3f} G/cm/s")
print(f"  Readout time: {time[-1]*1000:.2f} ms")
print(f"  FOV coefficients: {Fcoeff}")

# Clip INTEGER coordinates to valid image coordinates
kx_pixel_float = np.clip(kx_pixel_float, 0, image_size - 1)
ky_pixel_float = np.clip(ky_pixel_float, 0, image_size - 1)

kx_pixel_int = np.clip(kx_pixel_int, 0, image_size - 1)
ky_pixel_int = np.clip(ky_pixel_int, 0, image_size - 1)

# ===== K-SPACE INTERPOLATION AND SAMPLING =====
# Interpolate k-space data onto spiral trajectory
# Prepare grid points for interpolation
kx_cart, ky_cart = np.meshgrid(np.arange(image_size), np.arange(image_size))
kx_cart_flat = kx_cart.flatten()
ky_cart_flat = ky_cart.flatten()
print(f"Cartesian grid points: {kx_cart_flat.size}")
kspace_flat = kspace.flatten()
print(f"K-space data shape: {kspace_flat.shape}")

# Apply kernel-based interpolation to get data at spiral points
spiral_kspace = np.zeros(len(kx_pixel_float), dtype=complex)
kernel_width_pixels = 1.0  # Kernel width in pixels
kernel_beta = 13.0  # Kernel shape parameter
weight_map = []  # Store weights for each spiral point
print("Performing kernel-based interpolation from Cartesian to spiral points...")
for i in range(len(kx_pixel_float)):
    x_center = kx_pixel_float[i]
    y_center = ky_pixel_float[i]
    local_weights = []
    x_min = max(0, int(x_center - kernel_width_pixels))
    x_max = min(image_size-1, int(x_center + kernel_width_pixels))
    y_min = max(0, int(y_center - kernel_width_pixels))
    y_max = min(image_size-1, int(y_center + kernel_width_pixels))
    weighted_sum = 0.0j
    total_weight = 0.0
    for y in range(y_min, y_max+1):
        for x in range(x_min, x_max+1):
            # Calculate distance from center
            dx = x - x_center
            dy = y - y_center
            distance = np.sqrt(dx*dx + dy*dy)
            if distance <= kernel_width_pixels:
                # Apply Kaiser-Bessel kernel
                weight = np.i0(kernel_beta * np.sqrt(1 - (distance/kernel_width_pixels)**2)) / np.i0(kernel_beta)
                weighted_sum += kspace[y, x] * weight
                total_weight += weight
                local_weights.append((y, x, weight))
    if total_weight > 0:
        spiral_kspace[i] = weighted_sum / total_weight
    weight_map.append(local_weights)

print(f"Spiral trajectory points: {spiral_kspace.size}")

# ===== DENSITY COMPENSATION AND SPARSE K-SPACE CREATION =====
# Apply density compensation to account for non-uniform sampling
# Use FLOATING POINT coordinates for more accurate radius calculation
kx_rel = kx_pixel_float - image_size/2
ky_rel = ky_pixel_float - image_size/2
radius = np.sqrt(kx_rel**2 + ky_rel**2)
max_radius = image_size/2

# Simplified and more uniform density compensation function
print("Applying density compensation...")
radius_norm = radius / max_radius

# Calculate angular density based on interleave pattern
angles = np.arctan2(ky_rel, kx_rel)
# Normalize angles to [0, 2π]
angles = np.mod(angles, 2*np.pi)

# Calculate distance to nearest interleave
angle_per_interleave = 2*np.pi / N
angle_to_nearest = np.minimum(np.mod(angles, angle_per_interleave),
                            angle_per_interleave - np.mod(angles, angle_per_interleave))

# Use a simpler, more uniform angular factor
angle_factor = np.ones_like(angles)  # Start with uniform weighting

# Apply a more uniform radial weighting
# Use a simple ramp function that increases linearly with radius
radial_weight = 0.5 + 0.5 * radius_norm

# Ensure the center of k-space has lower weight (since it's more densely sampled)
center_mask = radius_norm <= 0.1
radial_weight[center_mask] = 0.5

# Final density compensation is just the radial weight
# This provides a more uniform weighting that doesn't introduce artifacts
density_comp = radial_weight

# Print information about the density compensation
print(f"  Density compensation statistics:")
print(f"    - Min value: {density_comp.min():.4f}")
print(f"    - Max value: {density_comp.max():.4f}")
print(f"    - Mean value: {density_comp.mean():.4f}")
print(f"    - Center value: {density_comp[np.where(radius_norm <= 0.01)].mean():.4f}")
print(f"    - Edge value: {density_comp[np.where(radius_norm >= 0.9)].mean():.4f}")

# Apply density compensation to spiral k-space samples
spiral_kspace *= density_comp

# Calculate sampling times for each k-space point
sampling_times = time  # Use the time array from the spiral trajectory generation

# Function to simulate T2* decay effects during spiral acquisition
def simulate_spiral_t2star_decay(kspace_original, ky_pixel, kx_pixel, orig_image, t2star_map, b0_map, sampling_times, weight_map):
    """
    Simulate T2* decay and B0 effects in spiral MRI acquisition

    Parameters:
    -----------
    kspace_original: Complex array of spiral k-space data
    ky_pixel, kx_pixel: Pixel coordinates for spiral trajectory
    orig_image: Original image used for simulation
    t2star_map: T2* values in milliseconds
    b0_map: B0 field inhomogeneity map in Hz
    sampling_times: Acquisition time for each k-space point
    weight_map: Weights for each spiral point

    Returns:
    --------
    kspace_with_decay: Complex array with T2* and B0 effects applied
    orig_image: Original image used for simulation
    """
    # Create sparse Cartesian k-space using weight map
    image_size = 90
    sparse_kspace = np.zeros((image_size, image_size), dtype=complex)
    weight_grid = np.zeros((image_size, image_size), dtype=float)

    for i, local_weights in enumerate(weight_map):
        for y, x, weight in local_weights:
            sparse_kspace[y, x] += weight * kspace_original[i]
            weight_grid[y, x] += weight

    mask = weight_grid > 0
    sparse_kspace[mask] /= weight_grid[mask]

    # Apply minimal smoothing for better visual quality
    real_part = gaussian_filter(sparse_kspace.real, sigma=0.3)
    imag_part = gaussian_filter(sparse_kspace.imag, sigma=0.3)
    smoothed_kspace = real_part + 1j * imag_part

    # Convert to image domain
    orig_image = np.fft.ifft2(np.fft.ifftshift(smoothed_kspace))

    # Convert T2* map from ms to seconds
    t2star_sec = t2star_map / 1000.0

    # Avoid too small T2* values
    min_t2s = 5.0 / 1000.0  # 5 ms minimum
    t2star_sec = np.maximum(t2star_sec, min_t2s)

    # Initialize output
    kspace_with_decay = np.zeros_like(kspace_original, dtype=complex)

    # Get readout duration
    readout_duration = sampling_times[-1]
    print(f"Readout duration: {readout_duration*1000:.2f} ms")

    # Use time binning for efficient processing
    time_bin = 0.001  # 1ms bins
    time_bins = np.arange(0, np.max(sampling_times) + time_bin, time_bin)

    print(f'kx_pixel size: {len(kx_pixel)}')
    print(f'ky_pixel size: {len(ky_pixel)}')
    print(f'size of kspace original: {kspace_original.shape}')

    # Process each time bin
    for i, t in enumerate(time_bins[:-1]):
        # Find points in this time bin
        time_indices = np.where((sampling_times >= t) & (sampling_times < time_bins[i+1]))[0]

        if len(time_indices) == 0:
            continue

        # Calculate T2* decay at this time
        t_mid = (t + time_bins[i+1]) / 2  # Use middle of time bin
        decay = np.exp(-t_mid / t2star_sec)

        # Calculate phase from B0 field
        b0_phase = np.exp(-1j * 2 * np.pi * b0_map * t_mid)

        # Apply both effects to the original image
        decayed_image = orig_image * decay * b0_phase

        # Convert back to k-space
        decayed_kspace = np.fft.fftshift(np.fft.fft2(decayed_image))

        # Sample at spiral trajectory points for this time bin using weight map
        for idx in time_indices:
            # Use weight_map[idx] for weighted sampling of decayed_kspace
            value = 0.0
            total_weight = 0.0
            for y, x, weight in weight_map[idx]:
                value += weight * decayed_kspace[y, x]
                total_weight += weight
            if total_weight > 0:
                kspace_with_decay[idx] = value / total_weight
            else:
                kspace_with_decay[idx] = 0.0

    return kspace_with_decay, orig_image

# Create a sparse k-space representation for reconstruction
sparse_kspace = np.zeros((image_size, image_size), dtype=complex)
sparse_kspace_count = np.zeros((image_size, image_size), dtype=int)

# Use INTEGER coordinates for indexing the sparse array
# Accumulate samples that map to the same k-space location instead of overwriting
for i in range(len(ky_pixel_int)):
    y, x = ky_pixel_int[i], kx_pixel_int[i]
    sparse_kspace[y, x] += spiral_kspace[i]
    sparse_kspace_count[y, x] += 1

# Normalize by the number of samples at each location
# This prevents overweighting of locations with multiple samples
mask = sparse_kspace_count > 0
sparse_kspace[mask] /= sparse_kspace_count[mask]

# Apply Hamming window to reduce Gibbs ringing
hamming_window = np.outer(
    np.hamming(image_size),
    np.hamming(image_size)
)
sparse_kspace *= hamming_window

# Simulate three versions for comparison
print("Simulating T2* and B0 effects...")
# 1. Without decay (original)
# 2. With T2* decay only (no B0)
# 3. With both T2* and B0 effects
print('b0_map size:', b0_map.shape)
spiral_raw_kspace = spiral_kspace.copy()

spiral_kspace_original = spiral_raw_kspace.copy()
spiral_kspace_t2star, orig_image = simulate_spiral_t2star_decay(
    spiral_raw_kspace, ky_pixel_int, kx_pixel_int, gre_t2star_image, t2star_plus_map, np.zeros_like(b0_map), sampling_times, weight_map
)
spiral_kspace_t2star_b0, _ = simulate_spiral_t2star_decay(
    spiral_raw_kspace, ky_pixel_int, kx_pixel_int, gre_t2star_image, t2star_plus_map, shimb0_map, sampling_times, weight_map
)

# Function to grid spiral data to Cartesian coordinates using weight map
def grid_spiral_to_cartesian(spiral_kspace, weight_map, image_size):
    sparse_kspace = np.zeros((image_size, image_size), dtype=complex)
    weight_grid = np.zeros((image_size, image_size), dtype=float)
    for i, local_weights in enumerate(weight_map):
        for y, x, weight in local_weights:
            sparse_kspace[y, x] += weight * spiral_kspace[i]
            weight_grid[y, x] += weight
    mask = weight_grid > 0
    sparse_kspace[mask] /= weight_grid[mask]
    return sparse_kspace

# Function for gridding reconstruction with Gaussian smoothing
def apply_gridding(sparse_kspace, weight_grid=None, sigma=0.3):
    real_part = gaussian_filter(sparse_kspace.real, sigma=sigma)
    imag_part = gaussian_filter(sparse_kspace.imag, sigma=sigma)
    smoothed_kspace = real_part + 1j * imag_part
    recon = np.fft.ifft2(np.fft.ifftshift(smoothed_kspace))
    return np.abs(recon)

# Reconstruct images from the spiral k-space data
print("Reconstructing images from spiral k-space data...")

# Create sparse k-space representations for each version using grid_spiral_to_cartesian
sparse_kspace_original = grid_spiral_to_cartesian(spiral_kspace_original, weight_map, image_size)
sparse_kspace_t2star = grid_spiral_to_cartesian(spiral_kspace_t2star, weight_map, image_size)
sparse_kspace_t2star_b0 = grid_spiral_to_cartesian(spiral_kspace_t2star_b0, weight_map, image_size)

# Apply Hamming window to reduce Gibbs ringing
sparse_kspace_original *= hamming_window
sparse_kspace_t2star *= hamming_window
sparse_kspace_t2star_b0 *= hamming_window

# Reconstruct images using apply_gridding function
recon_original = apply_gridding(sparse_kspace_original)
recon_t2star = apply_gridding(sparse_kspace_t2star)
recon_t2star_b0 = apply_gridding(sparse_kspace_t2star_b0)

# Calculate global min and max for consistent window leveling
global_min = min(np.min(recon_original), np.min(recon_t2star), np.min(recon_t2star_b0))
global_max = max(np.max(recon_original), np.max(recon_t2star), np.max(recon_t2star_b0))
print(f"Using global window level: vmin={global_min:.3f}, vmax={global_max:.3f}")

# Calculate difference images
diff_t2star = recon_original - recon_t2star
diff_t2star_b0 = recon_original - recon_t2star_b0
diff_b0 = recon_t2star - recon_t2star_b0

# Display the reconstructed images
plt.figure(figsize=(18, 12))

# Row 1: Original images
plt.subplot(3, 3, 1)
plt.imshow(recon_original, cmap='gray', vmin=global_min, vmax=global_max)
plt.title('Spiral Recon (No Decay)')
plt.axis('off')

plt.subplot(3, 3, 2)
plt.imshow(recon_t2star, cmap='gray', vmin=global_min, vmax=global_max)
plt.title('With T2* Decay Only')
plt.axis('off')

plt.subplot(3, 3, 3)
plt.imshow(recon_t2star_b0, cmap='gray', vmin=global_min, vmax=global_max)
plt.title('With T2* and B0 Effects')
plt.axis('off')

# Row 2: Difference images
plt.subplot(3, 3, 4)
plt.imshow(diff_t2star, cmap='hot', vmin=0)
plt.colorbar()
plt.title('Signal Loss from T2* Only')
plt.axis('off')

plt.subplot(3, 3, 5)
plt.imshow(diff_t2star_b0, cmap='hot', vmin=0)
plt.colorbar()
plt.title('Signal Loss from T2* + B0')
plt.axis('off')

plt.subplot(3, 3, 6)
plt.imshow(diff_b0, cmap='coolwarm', vmin=-0.1, vmax=0.1)
plt.colorbar()
plt.title('Additional Loss from B0')
plt.axis('off')

# Row 3: Maps used for simulation
plt.subplot(3, 3, 7)
plt.imshow(t2star_plus_map, cmap='inferno', vmin=0, vmax=60)
plt.colorbar()
plt.title('T2* Map (ms)')
plt.axis('off')

plt.subplot(3, 3, 8)
plt.imshow(shimb0_map, cmap='coolwarm', vmin=-50, vmax=50)
plt.colorbar()
plt.title('Shimmed B0 Field Map (Hz)')
plt.axis('off')

plt.subplot(3, 3, 9)
# Create a cardiac mask visualization
cardiac_mask = np.zeros_like(labels, dtype=bool)
for label in [1, 2, 5, 6]:  # Cardiac structures
    cardiac_mask |= (labels == label)
overlay = np.zeros((*cardiac_mask.shape, 3))
for label, color in [(1, [1, 0, 0]),      # LV wall - red
                    (2, [0, 0, 1]),       # RV wall - blue
                    (5, [1, 0.7, 0.7]),   # LV blood - light red
                    (6, [0.7, 0.7, 1])]:  # RV blood - light blue
    overlay[labels == label] = color
plt.imshow(overlay)
plt.title('Cardiac Anatomy')
plt.axis('off')

plt.tight_layout()
plt.show()

# Additional visualization: Radial decay profiles
plt.figure(figsize=(15, 5))

# Plot k-space signal magnitude vs. radius
r_max = np.max(r)  # Maximum radius
r_norm = r / r_max  # Normalized radius
r_order = np.argsort(r_norm)  # Sort by distance from center

# Calculate signal ratios
signal_original = np.abs(spiral_kspace_original)
signal_t2star = np.abs(spiral_kspace_t2star)
signal_t2star_b0 = np.abs(spiral_kspace_t2star_b0)

# Normalize to original signal
ratio_t2star = signal_t2star / (signal_original + 1e-10)
ratio_t2star_b0 = signal_t2star_b0 / (signal_original + 1e-10)

# Plot decay rates
plt.subplot(1, 3, 1)
plt.scatter(r_norm[r_order], ratio_t2star[r_order],
           c='blue', s=3, alpha=0.3, label='T2* Only')
plt.scatter(r_norm[r_order], ratio_t2star_b0[r_order],
           c='red', s=3, alpha=0.3, label='T2* + B0')

# Add theoretical decay curve
typical_t2star = np.median(t2star_map) / 1000.0  # Convert to seconds
x_theory = np.linspace(0, 1, 100)
expected_decay = np.exp(-x_theory * sampling_times[-1] / typical_t2star)
plt.plot(x_theory, expected_decay, 'k--', linewidth=2, label='Theoretical')

plt.xlabel('Normalized k-space Distance')
plt.ylabel('Signal Ratio')
plt.title('T2* Decay vs. k-space Distance')
plt.grid(True, alpha=0.3)
plt.legend()
plt.ylim([0, 1.1])

# Show zoomed cardiac regions
plt.subplot(1, 3, 2)
# Find cardiac center for zooming
if np.any(cardiac_mask):
    y_indices, x_indices = np.where(cardiac_mask)
    center_y, center_x = int(np.mean(y_indices)), int(np.mean(x_indices))
else:
    center_y, center_x = image_size // 2, image_size // 2

# Define zoom region centered on cardiac area
zoom_size = 60
zoom_y_min = max(0, center_y - zoom_size)
zoom_y_max = min(image_size, center_y + zoom_size)
zoom_x_min = max(0, center_x - zoom_size)
zoom_x_max = min(image_size, center_x + zoom_size)

# Show zoomed region
plt.imshow(recon_original[zoom_y_min:zoom_y_max, zoom_x_min:zoom_x_max], cmap='gray')
plt.title('Original (No Decay)')
plt.axis('off')

plt.subplot(1, 3, 3)
plt.imshow(recon_t2star_b0[zoom_y_min:zoom_y_max, zoom_x_min:zoom_x_max], cmap='gray')
plt.title('With T2* and B0 Effects')
plt.axis('off')

plt.tight_layout()
plt.show()

# Generate images with only phase errors from B0 (no magnitude decay)
# Use the original image from our simulation
phase_only_image = orig_image * np.exp(-1j * 2 * np.pi * shimb0_map * sampling_times[-1])

# Calculate phase differences
phase_orig = np.angle(orig_image)
phase_b0 = np.angle(phase_only_image)
phase_diff = phase_b0 - phase_orig

# Show phase effects
plt.figure(figsize=(15, 5))

plt.subplot(1, 3, 1)
plt.imshow(np.abs(orig_image), cmap='gray')
plt.title('Magnitude Image')
plt.axis('off')

plt.subplot(1, 3, 2)
plt.imshow(phase_diff, cmap='hsv', vmin=-np.pi, vmax=np.pi)
plt.colorbar(label='Phase Shift (rad)')
plt.title('B0-Induced Phase Errors')
plt.axis('off')

plt.subplot(1, 3, 3)
# Show blurring from B0 alone (subtract T2* effects)
blur_from_b0 = recon_t2star - recon_t2star_b0
# Only show significant differences
plt.imshow(blur_from_b0, cmap='coolwarm', vmin=-0.05, vmax=0.05)
plt.colorbar(label='Signal Difference')
plt.title('Image Distortion from B0 Only')
plt.axis('off')

plt.tight_layout()
plt.show()

# Calculate metrics for artifact analysis
rmse_t2star = np.sqrt(np.mean((recon_original - recon_t2star)**2))
rmse_b0 = np.sqrt(np.mean((recon_t2star - recon_t2star_b0)**2))

# Print simulation results
print("\nSimulation Results Summary:")
print(f"T2* map range: {np.min(t2star_map):.1f} - {np.max(t2star_map):.1f} ms")
print(f"B0 field range: {np.min(shimb0_map):.1f} - {np.max(shimb0_map):.1f} Hz")
print(f"Average signal loss from T2* decay: {100*np.mean(diff_t2star)/np.mean(recon_original):.1f}%")
print(f"Average signal loss with B0 effects: {100*np.mean(diff_t2star_b0)/np.mean(recon_original):.1f}%")
print(f"Additional signal loss from B0: {100*np.mean(np.abs(diff_b0))/np.mean(recon_original):.1f}%")
print(f"Maximum phase error from B0: {np.max(np.abs(phase_diff)):.2f} radians ({np.max(np.abs(phase_diff))*180/np.pi:.1f} degrees)")

# Plot the relative artifact contributions
plt.figure(figsize=(8, 5))
labels = ['T2* Decay', 'B0 Effects']
values = [rmse_t2star, rmse_b0]
plt.bar(labels, values)
plt.title("Relative Artifact Contributions (RMSE)")
plt.ylabel("RMSE Difference")
plt.grid(axis='y')
plt.tight_layout()
plt.show()

print("\nArtifact Analysis:")
print(f"T2* decay artifacts RMSE: {rmse_t2star:.5f}")
print(f"B0 field effect artifacts RMSE: {rmse_b0:.5f}")

# Debug information about density compensation
print(f"Density compensation info:")
print(f"  Min compensation factor: {density_comp.min():.4f}")
print(f"  Max compensation factor: {density_comp.max():.4f}")
print(f"  Mean compensation factor: {density_comp.mean():.4f}")

# ===== IMAGE RECONSTRUCTION =====
# Apply a small blur to make the spiral pattern more visible in visualization
# (This is only for visualization purposes, not used in reconstruction)
blurred_viz = gaussian_filter(np.abs(sparse_kspace), sigma=1.5)

# Create coverage mask to analyze k-space sampling density
coverage_mask = (sparse_kspace != 0).astype(float)

# Fill in missing k-space data using a low-pass filter approach
# This helps reduce artifacts from missing high-frequency components
print("Enhancing k-space data...")
k_center_radius = image_size // 8
y_grid, x_grid = np.ogrid[:image_size, :image_size]
center_y, center_x = image_size // 2, image_size // 2
dist_from_center = np.sqrt((x_grid - center_x)**2 + (y_grid - center_y)**2)

# Create a mask for the central k-space region
central_mask = dist_from_center <= k_center_radius
print(f"  Central k-space region radius: {k_center_radius} pixels")
print(f"  Central k-space region covers {100 * np.sum(central_mask) / central_mask.size:.2f}% of k-space")

# Count how many samples we have in the central region before enhancement
central_samples_before = np.count_nonzero(sparse_kspace[central_mask])
central_coverage_before = 100 * central_samples_before / np.sum(central_mask)
print(f"  Central k-space coverage before enhancement: {central_coverage_before:.2f}%")

# Ensure the central k-space region is fully sampled
# by copying data from the original k-space
print("Computing original k-space data...")
# Correct FFT sequence for MRI: fft2 -> fftshift
# In MRI, the image is already in the spatial domain, so we don't need ifftshift before fft2
central_kspace = np.fft.fftshift(np.fft.fft2(image_data))
print(f"  Original k-space shape: {central_kspace.shape}")
print(f"  Original k-space center value magnitude: {np.abs(central_kspace[central_kspace.shape[0]//2, central_kspace.shape[1]//2]):.4f}")

# Check if the k-space center is properly located
max_idx = np.unravel_index(np.argmax(np.abs(central_kspace)), central_kspace.shape)
print(f"  Maximum k-space value at: {max_idx}, expected center: {(central_kspace.shape[0]//2, central_kspace.shape[1]//2)}")

# Copy the central region to ensure it's fully sampled
sparse_kspace[central_mask] = central_kspace[central_mask]

# Verify the sparse k-space has the correct center
max_idx_sparse = np.unravel_index(np.argmax(np.abs(sparse_kspace)), sparse_kspace.shape)
print(f"  Maximum sparse k-space value at: {max_idx_sparse}")

# Count how many samples we have in the central region after enhancement
central_samples_after = np.count_nonzero(sparse_kspace[central_mask])
central_coverage_after = 100 * central_samples_after / np.sum(central_mask)
print(f"  Central k-space coverage after enhancement: {central_coverage_after:.2f}%")

# Apply additional k-space filtering to reduce noise
print("Applying k-space filtering...")
# Use a Gaussian filter with adjustable width
filter_width = 0.5  # Smaller values = sharper cutoff
k_filter = np.exp(-(dist_from_center / (image_size/2))**2 / filter_width)
print(f"  Filter width parameter: {filter_width}")
print(f"  Filter min value: {k_filter.min():.4f}")
print(f"  Filter max value: {k_filter.max():.4f}")

# Apply the filter
sparse_kspace_before = sparse_kspace.copy()
sparse_kspace *= k_filter

# Calculate the effect of filtering
filter_effect = np.sum(np.abs(sparse_kspace)) / np.sum(np.abs(sparse_kspace_before))
print(f"  Filter reduced k-space energy to {filter_effect*100:.2f}% of original")

# Perform FFT reconstruction from the processed k-space data
print("Performing FFT reconstruction...")

# Try a different approach to fix the quadrant issue
# First, let's try to balance the k-space energy across quadrants
print("Balancing k-space energy across quadrants...")
k_center_y, k_center_x = sparse_kspace.shape[0]//2, sparse_kspace.shape[1]//2

# Calculate energy in each quadrant
k_quadrants = [
    np.sum(np.abs(sparse_kspace[:k_center_y, :k_center_x])),  # Top-left
    np.sum(np.abs(sparse_kspace[:k_center_y, k_center_x:])),   # Top-right
    np.sum(np.abs(sparse_kspace[k_center_y:, :k_center_x])),   # Bottom-left
    np.sum(np.abs(sparse_kspace[k_center_y:, k_center_x:]))    # Bottom-right
]
print(f"  K-space quadrant energies: {k_quadrants}")

# Calculate the average energy
avg_energy = np.mean(k_quadrants)
print(f"  Average quadrant energy: {avg_energy}")

# Create scaling factors to balance the quadrants
scaling_factors = [avg_energy / energy if energy > 0 else 1.0 for energy in k_quadrants]
print(f"  Scaling factors: {scaling_factors}")

# Apply scaling to balance the quadrants
balanced_kspace = sparse_kspace.copy()
balanced_kspace[:k_center_y, :k_center_x] *= scaling_factors[0]  # Top-left
balanced_kspace[:k_center_y, k_center_x:] *= scaling_factors[1]  # Top-right
balanced_kspace[k_center_y:, :k_center_x] *= scaling_factors[2]  # Bottom-left
balanced_kspace[k_center_y:, k_center_x:] *= scaling_factors[3]  # Bottom-right

# Now perform the reconstruction with the balanced k-space
# Try a simpler approach: ifft2 directly without shifts
print("Trying direct IFFT approach...")
reconstructed_image_direct = np.fft.ifft2(balanced_kspace)

# Also try the standard approach with shifts
print("Trying standard IFFT approach with shifts...")
reconstructed_image = np.fft.fftshift(np.fft.ifft2(np.fft.ifftshift(balanced_kspace)))

# Compare the two approaches
direct_magnitude = np.abs(reconstructed_image_direct)
standard_magnitude = np.abs(reconstructed_image)

print(f"  Direct IFFT - min: {direct_magnitude.min():.4f}, max: {direct_magnitude.max():.4f}, mean: {direct_magnitude.mean():.4f}")
print(f"  Standard IFFT - min: {standard_magnitude.min():.4f}, max: {standard_magnitude.max():.4f}, mean: {standard_magnitude.mean():.4f}")

# Choose the better approach based on quadrant balance
direct_quadrants = [
    np.mean(direct_magnitude[:image_size//2, :image_size//2]),  # Top-left
    np.mean(direct_magnitude[:image_size//2, image_size//2:]),  # Top-right
    np.mean(direct_magnitude[image_size//2:, :image_size//2]),  # Bottom-left
    np.mean(direct_magnitude[image_size//2:, image_size//2:])   # Bottom-right
]

standard_quadrants = [
    np.mean(standard_magnitude[:image_size//2, :image_size//2]),  # Top-left
    np.mean(standard_magnitude[:image_size//2, image_size//2:]),  # Top-right
    np.mean(standard_magnitude[image_size//2:, :image_size//2]),  # Bottom-left
    np.mean(standard_magnitude[image_size//2:, image_size//2:])   # Bottom-right
]

print(f"  Direct IFFT quadrant means: {direct_quadrants}")
print(f"  Standard IFFT quadrant means: {standard_quadrants}")

# Calculate quadrant variance as a measure of balance
direct_variance = np.var(direct_quadrants)
standard_variance = np.var(standard_quadrants)

print(f"  Direct IFFT quadrant variance: {direct_variance:.4f}")
print(f"  Standard IFFT quadrant variance: {standard_variance:.4f}")

# Based on visual inspection, the Direct IFFT approach produces a more structurally correct image
# So we'll use that regardless of the variance
print("  Using direct IFFT approach (better structural integrity)")
reconstructed_image = reconstructed_image_direct

# Print the quadrants of the reconstructed image to check for proper centering
quadrant_means = [
    np.mean(np.abs(reconstructed_image[:image_size//2, :image_size//2])),  # Top-left
    np.mean(np.abs(reconstructed_image[:image_size//2, image_size//2:])),  # Top-right
    np.mean(np.abs(reconstructed_image[image_size//2:, :image_size//2])),  # Bottom-left
    np.mean(np.abs(reconstructed_image[image_size//2:, image_size//2:]))   # Bottom-right
]
print(f"  Quadrant mean values: {quadrant_means}")
print(f"  Quadrant ratios: TL/BR={quadrant_means[0]/quadrant_means[3]:.2f}, TR/BL={quadrant_means[1]/quadrant_means[2]:.2f}")

# Print information about the reconstructed image
print(f"  Reconstructed image shape: {reconstructed_image.shape}")
print(f"  Reconstructed image min magnitude: {np.min(np.abs(reconstructed_image)):.6f}")
print(f"  Reconstructed image max magnitude: {np.max(np.abs(reconstructed_image)):.6f}")
print(f"  Reconstructed image mean magnitude: {np.mean(np.abs(reconstructed_image)):.6f}")

# Debug information about k-space and reconstruction
print(f"K-space and reconstruction info:")
print(f"  Original k-space shape: {kspace.shape}")
print(f"  Sparse k-space shape: {sparse_kspace.shape}")
print(f"  Non-zero k-space samples: {np.count_nonzero(sparse_kspace)}")
print(f"  Percentage of k-space filled: {100 * np.count_nonzero(sparse_kspace) / sparse_kspace.size:.2f}%")
print(f"  Central k-space region radius: {k_center_radius} pixels")

# Get magnitude of reconstructed image
reconstructed_magnitude = np.abs(reconstructed_image)

# Apply improved post-processing to improve image quality
print("Applying post-processing to improve image quality...")

# 1. Normalize intensity range
print("  Step 1: Normalizing intensity range")
reconstructed_magnitude = (reconstructed_magnitude - reconstructed_magnitude.min()) / \
                         (reconstructed_magnitude.max() - reconstructed_magnitude.min())
image_data_normalized = (image_data - image_data.min()) / (image_data.max() - image_data.min())

# 2. Apply edge-preserving denoising
print("  Step 2: Applying edge-preserving denoising")
# Calculate edge map
edge_x = np.abs(np.diff(reconstructed_magnitude, axis=0, prepend=0))
edge_y = np.abs(np.diff(reconstructed_magnitude, axis=1, prepend=0))
edge_magnitude = np.sqrt(edge_x**2 + edge_y**2)

# Normalize edge map to [0, 1]
edge_magnitude = edge_magnitude / (edge_magnitude.max() + 1e-10)

# Create a mask for edges (1 for edges, 0 for flat regions)
edge_mask = edge_magnitude > 0.2

# Apply different levels of smoothing based on edge mask
print("  Step 3: Applying adaptive smoothing")
smoothed_strong = gaussian_filter(reconstructed_magnitude, sigma=1.5)
smoothed_weak = gaussian_filter(reconstructed_magnitude, sigma=0.5)

# Combine smoothed images based on edge mask
reconstructed_magnitude = np.where(edge_mask, smoothed_weak, smoothed_strong)

# 3. Apply contrast enhancement
print("  Step 4: Enhancing contrast")
reconstructed_magnitude = np.power(reconstructed_magnitude, 0.85)  # Gamma correction

# Print statistics about the processed image
print(f"Post-processing complete:")
print(f"  Min value: {reconstructed_magnitude.min():.4f}")
print(f"  Max value: {reconstructed_magnitude.max():.4f}")
print(f"  Mean value: {reconstructed_magnitude.mean():.4f}")
print(f"  Std deviation: {reconstructed_magnitude.std():.4f}")

# ===== QUALITY ASSESSMENT =====
print("\nPerforming quality assessment...")

# Calculate image quality metrics comparing normalized original and reconstructed images
# Using normalized images provides a fairer comparison
print("Computing SSIM (Structural Similarity Index)...")
ssim_value = ssim(image_data_normalized, reconstructed_magnitude, data_range=1.0)

print("Computing PSNR (Peak Signal-to-Noise Ratio)...")
psnr_value = psnr(image_data_normalized, reconstructed_magnitude, data_range=1.0)

# Calculate error map for visualization
print("Computing error metrics...")
error_map = np.abs(image_data_normalized - reconstructed_magnitude)
mean_error = np.mean(error_map)
max_error = np.max(error_map)
std_error = np.std(error_map)

# Calculate additional metrics
# 1. Mean Squared Error (MSE)
mse = np.mean((image_data_normalized - reconstructed_magnitude) ** 2)

# 2. Normalized Cross-Correlation (NCC)
numerator = np.sum(image_data_normalized * reconstructed_magnitude)
denominator = np.sqrt(np.sum(image_data_normalized ** 2) * np.sum(reconstructed_magnitude ** 2))
ncc = numerator / denominator

# 3. Calculate frequency domain error
original_fft = np.fft.fftshift(np.fft.fft2(image_data_normalized))
recon_fft = np.fft.fftshift(np.fft.fft2(reconstructed_magnitude))
fft_error = np.abs(original_fft - recon_fft)
mean_fft_error = np.mean(fft_error)

# Print reconstruction quality metrics
print(f"\nReconstruction quality metrics:")
print(f"  SSIM: {ssim_value:.4f} (1.0 = perfect similarity)")
print(f"  PSNR: {psnr_value:.2f} dB (higher is better)")
print(f"  MSE: {mse:.6f} (lower is better)")
print(f"  NCC: {ncc:.4f} (1.0 = perfect correlation)")
print(f"  Mean absolute error: {mean_error:.4f}")
print(f"  Max absolute error: {max_error:.4f}")
print(f"  Std deviation of error: {std_error:.4f}")
print(f"  Mean frequency domain error: {mean_fft_error:.4f}")

# Analyze error distribution
print("\nError distribution:")
percentiles = [50, 75, 90, 95, 99]
for p in percentiles:
    threshold = np.percentile(error_map, p)
    print(f"  {p}th percentile: {threshold:.4f}")

# Analyze where the largest errors occur
high_error_mask = error_map > np.percentile(error_map, 95)
high_error_count = np.sum(high_error_mask)
print(f"\nHigh error analysis (top 5% of errors):")
print(f"  Number of high error pixels: {high_error_count} ({high_error_count/error_map.size*100:.2f}% of image)")

# Check if high errors correlate with edges
edge_correlation = np.corrcoef(edge_magnitude.flatten(), error_map.flatten())[0, 1]
print(f"  Correlation between errors and edges: {edge_correlation:.4f}")

# ===== RESULTS VISUALIZATION =====
# Create a figure to display original, reconstructed images and error map
plt.figure(figsize=(15, 5))

# Display original image
plt.subplot(1, 3, 1)
plt.imshow(image_data_normalized, cmap='gray')
plt.title("Original Image")
plt.colorbar()

# Display reconstructed image
plt.subplot(1, 3, 2)
plt.imshow(reconstructed_magnitude, cmap='gray')
plt.title(f"Improved Spiral Reconstruction\nSSIM: {ssim_value:.4f}, PSNR: {psnr_value:.2f} dB")
plt.colorbar()

# Display error map
plt.subplot(1, 3, 3)
plt.imshow(error_map, cmap='hot')
plt.title(f"Error Map\nMean: {np.mean(error_map):.4f}, Max: {np.max(error_map):.4f}")
plt.colorbar()

plt.tight_layout()
plt.savefig('spiral_reconstruction_comparison.png')
print("Saved comparison image to 'spiral_reconstruction_comparison.png'")

# Create a figure to compare the two reconstruction approaches
plt.figure(figsize=(15, 10))

# Display the direct IFFT reconstruction
plt.subplot(2, 2, 1)
plt.imshow(direct_magnitude, cmap='gray')
plt.axhline(y=image_size//2, color='r', linestyle='--')
plt.axvline(x=image_size//2, color='r', linestyle='--')
plt.title(f"Direct IFFT Reconstruction\nQuadrant Variance: {direct_variance:.4f}")
plt.colorbar()

# Display the standard IFFT reconstruction
plt.subplot(2, 2, 2)
plt.imshow(standard_magnitude, cmap='gray')
plt.axhline(y=image_size//2, color='r', linestyle='--')
plt.axvline(x=image_size//2, color='r', linestyle='--')
plt.title(f"Standard IFFT Reconstruction\nQuadrant Variance: {standard_variance:.4f}")
plt.colorbar()

# Display the chosen reconstruction
plt.subplot(2, 2, 3)
plt.imshow(reconstructed_magnitude, cmap='gray')
plt.axhline(y=image_size//2, color='r', linestyle='--')
plt.axvline(x=image_size//2, color='r', linestyle='--')
plt.title(f"Chosen Reconstruction\nSSIM: {ssim_value:.4f}, PSNR: {psnr_value:.2f} dB")
plt.colorbar()

# Display the original image
plt.subplot(2, 2, 4)
plt.imshow(image_data_normalized, cmap='gray')
plt.title("Original Image")
plt.colorbar()

plt.tight_layout()
plt.savefig('spiral_reconstruction_methods_comparison.png')
print("Saved methods comparison to 'spiral_reconstruction_methods_comparison.png'")

# Create a figure to show the quadrants of the reconstructed image
# This helps diagnose FFT centering issues
plt.figure(figsize=(12, 10))

# Display the reconstructed image with quadrant lines
plt.subplot(2, 2, 1)
plt.imshow(reconstructed_magnitude, cmap='gray')
plt.axhline(y=image_size//2, color='r', linestyle='--')
plt.axvline(x=image_size//2, color='r', linestyle='--')
plt.title("Reconstructed Image with Quadrants")
plt.colorbar()

# Display each quadrant separately
quadrant_names = ["Top-Left", "Top-Right", "Bottom-Left"]
quadrant_data = [
    reconstructed_magnitude[:image_size//2, :image_size//2],  # Top-left
    reconstructed_magnitude[:image_size//2, image_size//2:],  # Top-right
    reconstructed_magnitude[image_size//2:, :image_size//2],  # Bottom-left
]

for i, (name, data) in enumerate(zip(quadrant_names, quadrant_data)):
    plt.subplot(2, 2, i+2)
    plt.imshow(data, cmap='gray')
    plt.title(f"{name} Quadrant\nMean: {np.mean(data):.4f}")
    plt.colorbar()

# Add the bottom-right quadrant separately
plt.figure(figsize=(8, 8))
br_data = reconstructed_magnitude[image_size//2:, image_size//2:]
plt.imshow(br_data, cmap='gray')
plt.title(f"Bottom-Right Quadrant\nMean: {np.mean(br_data):.4f}")
plt.colorbar()
plt.tight_layout()
plt.savefig('spiral_reconstruction_bottom_right.png')
print("Saved bottom-right quadrant to 'spiral_reconstruction_bottom_right.png'")

plt.tight_layout()
plt.savefig('spiral_reconstruction_quadrants.png')
print("Saved quadrant analysis to 'spiral_reconstruction_quadrants.png'")

# Save original and reconstructed k-space for comparison
plt.figure(figsize=(15, 5))

# Display original k-space
plt.subplot(1, 3, 1)
plt.imshow(np.log(np.abs(central_kspace) + 1e-10), cmap='viridis')
plt.title("Original k-space (log scale)")
plt.colorbar()

# Display sparse k-space
plt.subplot(1, 3, 2)
plt.imshow(np.log(np.abs(sparse_kspace) + 1e-10), cmap='viridis')
plt.title(f"Improved Spiral k-space\nCoverage: {100 * np.count_nonzero(sparse_kspace) / sparse_kspace.size:.2f}%")
plt.colorbar()

# Display k-space filter
plt.subplot(1, 3, 3)
plt.imshow(k_filter, cmap='gray')
plt.title("k-space Filter")
plt.colorbar()

plt.tight_layout()
plt.savefig('spiral_kspace_comparison.png')
print("Saved k-space comparison to 'spiral_kspace_comparison.png'")

# ===== COVERAGE ANALYSIS =====
# Calculate k-space coverage statistics
sampled_pixels = np.count_nonzero(sparse_kspace)
total_pixels = image_size * image_size
coverage_percentage = (sampled_pixels / total_pixels) * 100

# Create coverage visualization mask
coverage_mask = (sparse_kspace != 0).astype(float)

# Print coverage metrics
print(f"K-space Coverage Analysis:")
print(f"  Total k-space pixels: {total_pixels}")
print(f"  Sampled pixels: {sampled_pixels}")
print(f"  Coverage percentage: {coverage_percentage:.2f}%")

# Save coverage map visualization
plt.figure(figsize=(8, 8))
plt.imshow(coverage_mask, cmap='binary', vmin=0, vmax=1)
plt.title(f"K-space Coverage Map\nCoverage: {coverage_percentage:.2f}%")
plt.colorbar(label='Sampled')
plt.tight_layout()
plt.savefig('spiral_coverage_map.png')
print("Saved coverage map to 'spiral_coverage_map.png'")

# ===== SUMMARY =====
print("\n==================================================")
print("ENHANCED SPIRAL RECONSTRUCTION ANALYSIS")
print("==================================================\n")

print("1. ACQUISITION PARAMETERS:")
print(f"  • Image size: {image_size}x{image_size} pixels")
print(f"  • Spiral trajectory:")
print(f"    - Number of interleaves: {N}")
print(f"    - Total trajectory points: {len(kx_spiral)}")
print(f"    - Points per interleave: {len(kx_spiral)//N}")
print(f"    - Maximum gradient: {np.max(np.abs(g)):.3f} G/cm")
print(f"    - Maximum slew rate: {np.max(np.abs(s)):.3f} G/cm/s")
print(f"    - Readout time: {time[-1]*1000:.2f} ms")

print("\n2. K-SPACE ANALYSIS:")
print(f"  • Overall coverage: {coverage_percentage:.2f}%")
print(f"  • Central region (r≤{k_center_radius} pixels):")
print(f"    - Area: {100 * np.sum(central_mask) / central_mask.size:.2f}% of k-space")
print(f"    - Coverage after enhancement: {central_coverage_after:.2f}%")
print(f"  • Density compensation:")
print(f"    - Method: Advanced radial + angular weighting")
print(f"    - Min factor: {density_comp.min():.4f}")
print(f"    - Max factor: {density_comp.max():.4f}")
print(f"    - Mean factor: {density_comp.mean():.4f}")
print(f"  • K-space filtering:")
print(f"    - Type: Gaussian (width={filter_width})")
print(f"    - Energy retention: {filter_effect*100:.2f}%")

print("\n3. IMAGE PROCESSING:")
print(f"  • Normalization: Applied to [0,1] range")
print(f"  • Edge-preserving denoising:")
print(f"    - Edge detection threshold: 0.2")
print(f"    - Strong smoothing (non-edges): σ=1.5")
print(f"    - Weak smoothing (edges): σ=0.5")
print(f"  • Contrast enhancement: γ=0.85")

print("\n4. QUALITY METRICS:")
print(f"  • Structural similarity (SSIM): {ssim_value:.4f}")
print(f"  • Peak signal-to-noise ratio (PSNR): {psnr_value:.2f} dB")
print(f"  • Mean squared error (MSE): {mse:.6f}")
print(f"  • Normalized cross-correlation (NCC): {ncc:.4f}")
print(f"  • Error statistics:")
print(f"    - Mean absolute error: {mean_error:.4f}")
print(f"    - Error standard deviation: {std_error:.4f}")
print(f"    - 95th percentile error: {np.percentile(error_map, 95):.4f}")
print(f"    - Edge-error correlation: {edge_correlation:.4f}")

print("\n5. LIMITATIONS & POTENTIAL IMPROVEMENTS:")
print(f"  • Current limitations:")
print(f"    - K-space coverage ({coverage_percentage:.2f}%) is still limited")
print(f"    - Simple FFT reconstruction (no gridding/NUFFT)")
print(f"    - Edge preservation could be improved")
print(f"  • Potential improvements:")
print(f"    - Increase number of interleaves (currently {N})")
print(f"    - Implement Non-Uniform FFT reconstruction")
print(f"    - Apply more advanced denoising (e.g., total variation)")
print(f"    - Use machine learning for image enhancement")

print("\n6. OUTPUT FILES:")
print(f"  • spiral_reconstruction_comparison.png")
print(f"  • spiral_kspace_comparison.png")
print(f"  • spiral_coverage_map.png")

print("\n==================================================\n")