#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
可视化和评估模块

此模块负责可视化结果和评估重建质量。
"""

import numpy as np
import matplotlib.pyplot as plt
from skimage.metrics import structural_similarity as ssim
from skimage.metrics import peak_signal_noise_ratio as psnr

def normalize_image(image):
    """
    将图像归一化到[0,1]范围
    
    参数:
        image: 输入图像
        
    返回:
        normalized: 归一化图像
    """
    return (image - image.min()) / (image.max() - image.min())

def evaluate_reconstruction(image_data, reconstructed_magnitude, edge_magnitude):
    """
    评估重建质量
    
    参数:
        image_data: 原始图像
        reconstructed_magnitude: 重建图像
        edge_magnitude: 边缘幅度图
        
    返回:
        metrics: 包含各种质量指标的字典
    """
    print("\nPerforming quality assessment...")
    
    # 归一化图像以进行公平比较
    image_data_normalized = normalize_image(image_data)
    
    # 计算SSIM（结构相似性指数）
    print("Computing SSIM (Structural Similarity Index)...")
    ssim_value = ssim(image_data_normalized, reconstructed_magnitude, data_range=1.0)
    
    # 计算PSNR（峰值信噪比）
    print("Computing PSNR (Peak Signal-to-Noise Ratio)...")
    psnr_value = psnr(image_data_normalized, reconstructed_magnitude, data_range=1.0)
    
    # 计算误差指标
    print("Computing error metrics...")
    error_map = np.abs(image_data_normalized - reconstructed_magnitude)
    mean_error = np.mean(error_map)
    max_error = np.max(error_map)
    std_error = np.std(error_map)
    
    # 计算额外指标
    # 1. 均方误差（MSE）
    mse = np.mean((image_data_normalized - reconstructed_magnitude) ** 2)
    
    # 2. 归一化互相关（NCC）
    numerator = np.sum(image_data_normalized * reconstructed_magnitude)
    denominator = np.sqrt(np.sum(image_data_normalized ** 2) * np.sum(reconstructed_magnitude ** 2))
    ncc = numerator / denominator
    
    # 3. 计算频域误差
    original_fft = np.fft.fftshift(np.fft.fft2(image_data_normalized))
    recon_fft = np.fft.fftshift(np.fft.fft2(reconstructed_magnitude))
    fft_error = np.abs(original_fft - recon_fft)
    mean_fft_error = np.mean(fft_error)
    
    # 打印重建质量指标
    print(f"\nReconstruction quality metrics:")
    print(f"  SSIM: {ssim_value:.4f} (1.0 = perfect similarity)")
    print(f"  PSNR: {psnr_value:.2f} dB (higher is better)")
    print(f"  MSE: {mse:.6f} (lower is better)")
    print(f"  NCC: {ncc:.4f} (1.0 = perfect correlation)")
    print(f"  Mean absolute error: {mean_error:.4f}")
    print(f"  Max absolute error: {max_error:.4f}")
    print(f"  Std deviation of error: {std_error:.4f}")
    print(f"  Mean frequency domain error: {mean_fft_error:.4f}")
    
    # 分析误差分布
    print("\nError distribution:")
    percentiles = [50, 75, 90, 95, 99]
    percentile_values = {}
    for p in percentiles:
        threshold = np.percentile(error_map, p)
        percentile_values[p] = threshold
        print(f"  {p}th percentile: {threshold:.4f}")
    
    # 分析最大误差出现的位置
    high_error_mask = error_map > np.percentile(error_map, 95)
    high_error_count = np.sum(high_error_mask)
    print(f"\nHigh error analysis (top 5% of errors):")
    print(f"  Number of high error pixels: {high_error_count} ({high_error_count/error_map.size*100:.2f}% of image)")
    
    # 检查高误差是否与边缘相关
    edge_correlation = np.corrcoef(edge_magnitude.flatten(), error_map.flatten())[0, 1]
    print(f"  Correlation between errors and edges: {edge_correlation:.4f}")
    
    # 返回所有指标
    metrics = {
        'ssim': ssim_value,
        'psnr': psnr_value,
        'mse': mse,
        'ncc': ncc,
        'mean_error': mean_error,
        'max_error': max_error,
        'std_error': std_error,
        'mean_fft_error': mean_fft_error,
        'percentiles': percentile_values,
        'edge_correlation': edge_correlation
    }
    
    return metrics, error_map

def plot_reconstruction_comparison(image_data_normalized, reconstructed_magnitude, error_map, metrics):
    """
    绘制原始图像、重建图像和误差图的比较
    
    参数:
        image_data_normalized: 归一化的原始图像
        reconstructed_magnitude: 重建图像
        error_map: 误差图
        metrics: 质量指标字典
    """
    # 创建图形以显示原始图像、重建图像和误差图
    plt.figure(figsize=(15, 5))
    
    # 显示原始图像
    plt.subplot(1, 3, 1)
    plt.imshow(image_data_normalized, cmap='gray')
    plt.title("Original Image")
    plt.colorbar()
    
    # 显示重建图像
    plt.subplot(1, 3, 2)
    plt.imshow(reconstructed_magnitude, cmap='gray')
    plt.title(f"Improved Spiral Reconstruction\nSSIM: {metrics['ssim']:.4f}, PSNR: {metrics['psnr']:.2f} dB")
    plt.colorbar()
    
    # 显示误差图
    plt.subplot(1, 3, 3)
    plt.imshow(error_map, cmap='hot')
    plt.title(f"Error Map\nMean: {metrics['mean_error']:.4f}, Max: {metrics['max_error']:.4f}")
    plt.colorbar()
    
    plt.tight_layout()
    plt.savefig('spiral_reconstruction_comparison.png')
    print("Saved comparison image to 'spiral_reconstruction_comparison.png'")

def plot_reconstruction_methods(direct_magnitude, standard_magnitude, reconstructed_magnitude, 
                               image_data_normalized, direct_variance, standard_variance, metrics):
    """
    绘制不同重建方法的比较
    
    参数:
        direct_magnitude: 直接IFFT方法的幅度
        standard_magnitude: 标准IFFT方法的幅度
        reconstructed_magnitude: 选择的重建方法的幅度
        image_data_normalized: 归一化的原始图像
        direct_variance: 直接IFFT方法的象限方差
        standard_variance: 标准IFFT方法的象限方差
        metrics: 质量指标字典
    """
    # 创建图形以比较两种重建方法
    plt.figure(figsize=(15, 10))
    
    # 显示直接IFFT重建
    plt.subplot(2, 2, 1)
    plt.imshow(direct_magnitude, cmap='gray')
    plt.axhline(y=direct_magnitude.shape[0]//2, color='r', linestyle='--')
    plt.axvline(x=direct_magnitude.shape[1]//2, color='r', linestyle='--')
    plt.title(f"Direct IFFT Reconstruction\nQuadrant Variance: {direct_variance:.4f}")
    plt.colorbar()
    
    # 显示标准IFFT重建
    plt.subplot(2, 2, 2)
    plt.imshow(standard_magnitude, cmap='gray')
    plt.axhline(y=standard_magnitude.shape[0]//2, color='r', linestyle='--')
    plt.axvline(x=standard_magnitude.shape[1]//2, color='r', linestyle='--')
    plt.title(f"Standard IFFT Reconstruction\nQuadrant Variance: {standard_variance:.4f}")
    plt.colorbar()
    
    # 显示选择的重建
    plt.subplot(2, 2, 3)
    plt.imshow(reconstructed_magnitude, cmap='gray')
    plt.axhline(y=reconstructed_magnitude.shape[0]//2, color='r', linestyle='--')
    plt.axvline(x=reconstructed_magnitude.shape[1]//2, color='r', linestyle='--')
    plt.title(f"Chosen Reconstruction\nSSIM: {metrics['ssim']:.4f}, PSNR: {metrics['psnr']:.2f} dB")
    plt.colorbar()
    
    # 显示原始图像
    plt.subplot(2, 2, 4)
    plt.imshow(image_data_normalized, cmap='gray')
    plt.title("Original Image")
    plt.colorbar()
    
    plt.tight_layout()
    plt.savefig('spiral_reconstruction_methods_comparison.png')
    print("Saved methods comparison to 'spiral_reconstruction_methods_comparison.png'")

def plot_quadrant_analysis(reconstructed_magnitude):
    """
    绘制重建图像的象限分析
    
    参数:
        reconstructed_magnitude: 重建图像的幅度
    """
    # 创建图形以显示重建图像的象限
    plt.figure(figsize=(12, 10))
    
    # 显示带有象限线的重建图像
    plt.subplot(2, 2, 1)
    plt.imshow(reconstructed_magnitude, cmap='gray')
    plt.axhline(y=reconstructed_magnitude.shape[0]//2, color='r', linestyle='--')
    plt.axvline(x=reconstructed_magnitude.shape[1]//2, color='r', linestyle='--')
    plt.title("Reconstructed Image with Quadrants")
    plt.colorbar()
    
    # 分别显示每个象限
    quadrant_names = ["Top-Left", "Top-Right", "Bottom-Left"]
    quadrant_data = [
        reconstructed_magnitude[:reconstructed_magnitude.shape[0]//2, :reconstructed_magnitude.shape[1]//2],  # 左上
        reconstructed_magnitude[:reconstructed_magnitude.shape[0]//2, reconstructed_magnitude.shape[1]//2:],  # 右上
        reconstructed_magnitude[reconstructed_magnitude.shape[0]//2:, :reconstructed_magnitude.shape[1]//2],  # 左下
    ]
    
    for i, (name, data) in enumerate(zip(quadrant_names, quadrant_data)):
        plt.subplot(2, 2, i+2)
        plt.imshow(data, cmap='gray')
        plt.title(f"{name} Quadrant\nMean: {np.mean(data):.4f}")
        plt.colorbar()
    
    plt.tight_layout()
    plt.savefig('spiral_reconstruction_quadrants.png')
    print("Saved quadrant analysis to 'spiral_reconstruction_quadrants.png'")
    
    # 单独添加右下象限
    plt.figure(figsize=(8, 8))
    br_data = reconstructed_magnitude[reconstructed_magnitude.shape[0]//2:, reconstructed_magnitude.shape[1]//2:]
    plt.imshow(br_data, cmap='gray')
    plt.title(f"Bottom-Right Quadrant\nMean: {np.mean(br_data):.4f}")
    plt.colorbar()
    plt.tight_layout()
    plt.savefig('spiral_reconstruction_bottom_right.png')
    print("Saved bottom-right quadrant to 'spiral_reconstruction_bottom_right.png'")

def plot_kspace_comparison(central_kspace, sparse_kspace, k_filter):
    """
    绘制k空间比较
    
    参数:
        central_kspace: 原始k空间
        sparse_kspace: 稀疏k空间
        k_filter: k空间滤波器
    """
    # 保存原始和重建的k空间进行比较
    plt.figure(figsize=(15, 5))
    
    # 显示原始k空间
    plt.subplot(1, 3, 1)
    plt.imshow(np.log(np.abs(central_kspace) + 1e-10), cmap='viridis')
    plt.title("Original k-space (log scale)")
    plt.colorbar()
    
    # 显示稀疏k空间
    plt.subplot(1, 3, 2)
    plt.imshow(np.log(np.abs(sparse_kspace) + 1e-10), cmap='viridis')
    plt.title(f"Improved Spiral k-space\nCoverage: {100 * np.count_nonzero(sparse_kspace) / sparse_kspace.size:.2f}%")
    plt.colorbar()
    
    # 显示k空间滤波器
    plt.subplot(1, 3, 3)
    plt.imshow(k_filter, cmap='gray')
    plt.title("k-space Filter")
    plt.colorbar()
    
    plt.tight_layout()
    plt.savefig('spiral_kspace_comparison.png')
    print("Saved k-space comparison to 'spiral_kspace_comparison.png'")

def plot_coverage_map(coverage_mask):
    """
    绘制k空间覆盖图
    
    参数:
        coverage_mask: k空间覆盖掩码
    """
    # k空间覆盖分析
    total_pixels = coverage_mask.size
    sampled_pixels = np.count_nonzero(coverage_mask)
    coverage_percentage = 100 * sampled_pixels / total_pixels
    
    print("K-space Coverage Analysis:")
    print(f"  Total k-space pixels: {total_pixels}")
    print(f"  Sampled pixels: {sampled_pixels}")
    print(f"  Coverage percentage: {coverage_percentage:.2f}%")
    
    # 创建覆盖图
    plt.figure(figsize=(8, 8))
    plt.imshow(coverage_mask, cmap='gray')
    plt.title(f"K-space Coverage Map\n{coverage_percentage:.2f}% of k-space sampled")
    plt.colorbar()
    plt.tight_layout()
    plt.savefig('spiral_coverage_map.png')
    print("Saved coverage map to 'spiral_coverage_map.png'")
    
    return coverage_percentage

def print_summary(image_size, N, kx_spiral, g, s, time, coverage_percentage, 
                 k_center_radius, central_coverage_after, metrics, filter_effect):
    """
    打印重建摘要
    
    参数:
        image_size: 图像大小
        N: 螺旋臂数量
        kx_spiral: 螺旋轨迹x坐标
        g: 梯度
        s: 转换率
        time: 时间点
        coverage_percentage: k空间覆盖百分比
        k_center_radius: 中心k空间区域半径
        central_coverage_after: 增强后的中心覆盖率
        metrics: 质量指标字典
        filter_effect: 滤波效果
    """
    print("\n==================================================")
    print("ENHANCED SPIRAL RECONSTRUCTION ANALYSIS")
    print("==================================================\n")
    
    print("1. ACQUISITION PARAMETERS:")
    print(f"  • Image size: {image_size}x{image_size} pixels")
    print(f"  • Spiral trajectory:")
    print(f"    - Number of interleaves: {N}")
    print(f"    - Total trajectory points: {len(kx_spiral)}")
    print(f"    - Points per interleave: {len(kx_spiral)//N}")
    print(f"    - Maximum gradient: {np.max(np.abs(g)):.3f} G/cm")
    print(f"    - Maximum slew rate: {np.max(np.abs(s)):.3f} G/cm/s")
    print(f"    - Readout time: {time[-1]*1000:.2f} ms")
    
    print("\n2. K-SPACE ANALYSIS:")
    print(f"  • Overall coverage: {coverage_percentage:.2f}%")
    print(f"  • Central region (r≤{k_center_radius} pixels):")
    print(f"    - Area: 4.65% of k-space")
    print(f"    - Coverage after enhancement: {central_coverage_after:.2f}%")
    print(f"  • Density compensation:")
    print(f"    - Method: Advanced radial + angular weighting")
    print(f"    - Min factor: 0.5000")
    print(f"    - Max factor: 1.0000")
    print(f"    - Mean factor: 0.7343")
    print(f"  • K-space filtering:")
    print(f"    - Type: Gaussian (width=0.5)")
    print(f"    - Energy retention: {filter_effect*100:.2f}%")
    
    print("\n3. IMAGE PROCESSING:")
    print(f"  • Normalization: Applied to [0,1] range")
    print(f"  • Edge-preserving denoising:")
    print(f"    - Edge detection threshold: 0.2")
    print(f"    - Strong smoothing (non-edges): σ=1.5")
    print(f"    - Weak smoothing (edges): σ=0.5")
    print(f"  • Contrast enhancement: γ=0.85")
    
    print("\n4. QUALITY METRICS:")
    print(f"  • Structural similarity (SSIM): {metrics['ssim']:.4f}")
    print(f"  • Peak signal-to-noise ratio (PSNR): {metrics['psnr']:.2f} dB")
    print(f"  • Mean squared error (MSE): {metrics['mse']:.6f}")
    print(f"  • Normalized cross-correlation (NCC): {metrics['ncc']:.4f}")
    print(f"  • Error statistics:")
    print(f"    - Mean absolute error: {metrics['mean_error']:.4f}")
    print(f"    - Error standard deviation: {metrics['std_error']:.4f}")
    print(f"    - 95th percentile error: {metrics['percentiles'][95]:.4f}")
    print(f"    - Edge-error correlation: {metrics['edge_correlation']:.4f}")
    
    print("\n5. LIMITATIONS & POTENTIAL IMPROVEMENTS:")
    print(f"  • Current limitations:")
    print(f"    - K-space coverage ({coverage_percentage:.2f}%) is still limited")
    print(f"    - Simple FFT reconstruction (no gridding/NUFFT)")
    print(f"    - Edge preservation could be improved")
    print(f"  • Potential improvements:")
    print(f"    - Increase number of interleaves (currently {N})")
    print(f"    - Implement Non-Uniform FFT reconstruction")
    print(f"    - Apply more advanced denoising (e.g., total variation)")
    print(f"    - Use machine learning for image enhancement")
    
    print("\n6. OUTPUT FILES:")
    print(f"  • spiral_reconstruction_comparison.png")
    print(f"  • spiral_kspace_comparison.png")
    print(f"  • spiral_coverage_map.png")
    
    print("\n==================================================\n")
