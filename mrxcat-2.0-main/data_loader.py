#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据加载和预处理模块

此模块负责加载MRI数据并进行基本的预处理操作。
"""

import numpy as np
from scipy.ndimage import zoom, gaussian_filter

def load_cardiac_data(file_path):
    """
    加载心脏MRI数据
    
    参数:
        file_path: 数据文件路径
        
    返回:
        data: 包含所有参数图的字典
    """
    print("[DEBUG] Starting to load cardiac parameter maps...")
    try:
        data = np.load(file_path)
        print("[DEBUG] Successfully loaded cardiac data")
    except Exception as e:
        print(f"[ERROR] Failed to load cardiac data: {e}")
        import os
        print(f"[DEBUG] Current working directory: {os.getcwd()}")
        print(f"[DEBUG] Checking if file exists: {os.path.exists(file_path)}")
        raise
    
    # 显示可用参数
    print(f"[DEBUG] Available parameters: {list(data.keys())}")
    print(f"[DEBUG] Data shapes:")
    for key in data.keys():
        print(f"  - {key}: {data[key].shape}")
    
    return data

def extract_axial_slice(data_input, slice_idx):
    """
    从3D体积中提取轴向切片
    
    参数:
      data_input : 3D numpy数组，形状为[X, Y, Z]
      slice_idx  : 轴向切片索引（沿第一个轴）
      
    返回:
      slice_data : 2D numpy数组，提取的切片
    """
    return data_input[slice_idx, :, :].copy()

def resize_to_square(image, target_size=256):
    """
    将图像调整为指定大小的正方形。
    如果图像已经小于目标大小，则会进行填充。
    """
    h, w = image.shape
    
    # 如果图像大于目标大小，则调整大小
    if h > target_size or w > target_size:
        scale = target_size / max(h, w)
        new_h, new_w = int(h * scale), int(w * scale)
        # 使用scipy.ndimage.zoom进行调整大小
        resized = zoom(image, (new_h/h, new_w/w))
        h, w = new_h, new_w
    else:
        resized = image
    
    # 创建正方形画布
    result = np.zeros((target_size, target_size), dtype=image.dtype)
    
    # 计算居中偏移
    h_offset = (target_size - h) // 2
    w_offset = (target_size - w) // 2
    
    # 将调整大小后的图像放在画布中心
    result[h_offset:h_offset+h, w_offset:w_offset+w] = resized
    
    return result

def preprocess_image(image_data, sigma=0.5, gamma=0.8):
    """
    对图像进行预处理，包括去噪和对比度增强
    
    参数:
        image_data: 输入图像
        sigma: 高斯滤波的sigma参数
        gamma: gamma校正参数
        
    返回:
        processed_image: 处理后的图像
    """
    print("Applying preprocessing to input image...")
    print(f"  Original image range: [{image_data.min():.4f}, {image_data.max():.4f}]")
    
    # 1. 应用轻微去噪
    denoised = gaussian_filter(image_data, sigma=sigma)
    print(f"  After denoising: [{denoised.min():.4f}, {denoised.max():.4f}]")
    
    # 2. 增强对比度
    normalized = (denoised - denoised.min()) / (denoised.max() - denoised.min())
    enhanced = np.power(normalized, gamma) * (denoised.max() - denoised.min()) + denoised.min()
    print(f"  After contrast enhancement: [{enhanced.min():.4f}, {enhanced.max():.4f}]")
    
    return enhanced
