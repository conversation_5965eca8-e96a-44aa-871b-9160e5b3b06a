% Content of parameter file (CINEpar.m/PERFpar.m)

% Change parameters in section "MRXCAT settings" only
%
% Note: Not all combinations of any parameter values are possible.
%       Some parameter changes require changes in the XCAT files.
%       E.g. if you want to increase the number of segments, you need
%       more XCAT heart phases for the additional segments, 
%       i.e. additional	*.bin files.


% --------------------------------------------------------------------
%   MRXCAT settings
% --------------------------------------------------------------------
RhoMuscle       = 80;                            % Proton density muscle [%]
RhoFat          = 80;                            % Proton density fat    [%]
RhoBlood        = 95;                            % Proton density blood  [%]
RhoLiver        = 90;                            % Proton density liver  [%]
RhoBone         = 12;                            % Proton density bone   [%]

T1Muscle        = 900;                           % T1 muscle             [ms]
T1Fat           = 350;                           % T1 fat                [ms]
T1Blood         = 1200;                          % T1 blood              [ms]
T1Liver         = 800;                           % T1 liver              [ms]
T1Bone          = 250;                           % T1 bone               [ms]

T2Muscle        = 50;                            % T2 muscle             [ms]
T2Fat           = 30;                            % T2 fat                [ms]
T2Blood         = 100;                           % T2 blood              [ms]
T2Liver         = 50;                            % T2 liver              [ms]
T2Bone          = 20;                            % T2 bone               [ms]

TR              = 3;                             % Repetition time       [ms]
TE              = 1.5;                           % Echo time             [ms]
Flip            = 60;                            % Flip                  [deg]
Frames          = 20;                            % Number of heart phases (default: 24; 0=use # XCAT frames (.bin files))
Segments        = 1;                             % Number of segments

BoundingBox     = [0,1;0,1;0,1];                 % [0.25,0.75;0.25,0.75;0,1];  % BoundingBox in rel. units
RotationXYZ     = [115;35;240];                  % Rotations about x,y,z [deg]  (default: 115/35/240)
                                            % x=(RL) y=(AP) z=(FH)
LowPassFilt     = 1;                             % low-pass filter images
FilterStr       = 3;                             % low-pass filter strength (default: 1.2)

SNR             = 20;                            % signal-to-noise ratio
Coils           = 8;                             % number coils (Biot-Savart)
CoilDist        = 450;                           %450 default             % body radius [mm]    = distance of coil centres from origin
CoilsPerRow     = 8;                             % number of coils on 1 "ring" or row of coil array (default: 8)

Trajectory      = 'Cartesian';                   % k-space trajectory (Cartesian, Radial, GoldenAngle)
Undersample     = 1;                             % undersampling factor (only for Radial/GoldenAngle right now)

% ---------------------------------------------------------------------------------------------------------------------
% ---------------------------------------------------------------------------------------------------------------------


