#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
MRI信号模拟和重建模块

此模块负责MRI信号的模拟和图像重建。
"""

import numpy as np
from scipy.ndimage import gaussian_filter

def simulate_spiral_acquisition(image_data, kx_spiral, ky_spiral, image_size):
    """
    模拟螺旋轨迹的MRI信号采集

    参数:
        image_data: 输入图像
        kx_spiral: 螺旋轨迹的x坐标
        ky_spiral: 螺旋轨迹的y坐标
        image_size: 图像大小

    返回:
        spiral_kspace: 螺旋采样的k空间数据
        kx_pixel_float: 浮点像素坐标
        ky_pixel_float: 浮点像素坐标
        kx_pixel_int: 整数像素坐标
        ky_pixel_int: 整数像素坐标
    """
    # 计算k空间
    kspace = np.fft.fftshift(np.fft.fft2(image_data))

    # 将螺旋轨迹坐标转换为像素坐标
    # 螺旋轨迹坐标范围为[-kmax, kmax]，需要映射到[0, image_size-1]
    kmax = np.max(np.abs(kx_spiral))
    scale_factor = (image_size / 2) / kmax

    # 浮点像素坐标
    kx_pixel_float = kx_spiral * scale_factor + image_size / 2
    ky_pixel_float = ky_spiral * scale_factor + image_size / 2

    # 整数像素坐标（用于索引）
    kx_pixel_int = np.clip(np.round(kx_pixel_float).astype(int), 0, image_size - 1)
    ky_pixel_int = np.clip(np.round(ky_pixel_float).astype(int), 0, image_size - 1)

    # 从k空间采样
    spiral_kspace = kspace[ky_pixel_int, kx_pixel_int]

    return spiral_kspace, kx_pixel_float, ky_pixel_float, kx_pixel_int, ky_pixel_int

def apply_density_compensation(kx_pixel_float, ky_pixel_float, spiral_kspace, image_size, N):
    """
    应用密度补偿以考虑非均匀采样

    参数:
        kx_pixel_float: 浮点像素x坐标
        ky_pixel_float: 浮点像素y坐标
        spiral_kspace: 螺旋采样的k空间数据
        image_size: 图像大小
        N: 螺旋臂数量

    返回:
        spiral_kspace: 密度补偿后的k空间数据
        density_comp: 密度补偿因子
    """
    print("Applying density compensation...")

    # 使用浮点坐标进行更准确的半径计算
    kx_rel = kx_pixel_float - image_size/2
    ky_rel = ky_pixel_float - image_size/2
    radius = np.sqrt(kx_rel**2 + ky_rel**2)
    max_radius = image_size/2

    # 归一化半径
    radius_norm = radius / max_radius

    # 计算基于角度的密度
    angles = np.arctan2(ky_rel, kx_rel)
    # 归一化角度到[0, 2π]
    angles = np.mod(angles, 2*np.pi)

    # 计算到最近螺旋臂的距离
    angle_per_interleave = 2*np.pi / N
    angle_to_nearest = np.minimum(np.mod(angles, angle_per_interleave),
                                angle_per_interleave - np.mod(angles, angle_per_interleave))

    # 使用更简单、更均匀的角度因子
    angle_factor = np.ones_like(angles)  # 从均匀权重开始

    # 应用更均匀的径向权重
    # 使用随半径线性增加的简单斜坡函数
    radial_weight = 0.5 + 0.5 * radius_norm

    # 确保k空间中心的权重较低（因为它采样更密集）
    center_mask = radius_norm <= 0.1
    radial_weight[center_mask] = 0.5

    # 最终密度补偿只是径向权重
    # 这提供了更均匀的权重，不会引入伪影
    density_comp = radial_weight

    # 打印有关密度补偿的信息
    print(f"  Density compensation statistics:")
    print(f"    - Min value: {density_comp.min():.4f}")
    print(f"    - Max value: {density_comp.max():.4f}")
    print(f"    - Mean value: {density_comp.mean():.4f}")
    print(f"    - Center value: {density_comp[np.where(radius_norm <= 0.01)].mean():.4f}")
    print(f"    - Edge value: {density_comp[np.where(radius_norm >= 0.9)].mean():.4f}")

    # 应用密度补偿
    spiral_kspace *= density_comp

    return spiral_kspace, density_comp

def create_sparse_kspace(spiral_kspace, kx_pixel_int, ky_pixel_int, image_size):
    """
    创建用于重建的稀疏k空间表示

    参数:
        spiral_kspace: 螺旋采样的k空间数据
        kx_pixel_int: 整数像素x坐标
        ky_pixel_int: 整数像素y坐标
        image_size: 图像大小

    返回:
        sparse_kspace: 稀疏k空间
        coverage_mask: 覆盖掩码
    """
    # 创建用于重建的稀疏k空间表示
    sparse_kspace = np.zeros((image_size, image_size), dtype=complex)
    sparse_kspace_count = np.zeros((image_size, image_size), dtype=int)

    # 使用整数坐标索引稀疏数组
    # 累积映射到同一k空间位置的样本，而不是覆盖
    for i in range(len(ky_pixel_int)):
        y, x = ky_pixel_int[i], kx_pixel_int[i]
        sparse_kspace[y, x] += spiral_kspace[i]
        sparse_kspace_count[y, x] += 1

    # 通过每个位置的样本数量归一化
    # 这防止了具有多个样本的位置的过度加权
    mask = sparse_kspace_count > 0
    sparse_kspace[mask] /= sparse_kspace_count[mask]

    # 创建覆盖掩码以分析k空间采样密度
    coverage_mask = (sparse_kspace != 0).astype(float)

    return sparse_kspace, coverage_mask

def enhance_kspace(sparse_kspace, image_data, image_size):
    """
    增强k空间数据，填充中心区域并应用滤波

    参数:
        sparse_kspace: 稀疏k空间
        image_data: 原始图像数据
        image_size: 图像大小

    返回:
        sparse_kspace: 增强后的k空间
        central_mask: 中心区域掩码
        central_coverage_before: 增强前的中心覆盖率
        central_coverage_after: 增强后的中心覆盖率
    """
    print("Enhancing k-space data...")

    # 填充缺失的k空间数据，使用低通滤波方法
    # 这有助于减少缺失高频分量导致的伪影
    k_center_radius = image_size // 8
    y_grid, x_grid = np.ogrid[:image_size, :image_size]
    center_y, center_x = image_size // 2, image_size // 2
    dist_from_center = np.sqrt((x_grid - center_x)**2 + (y_grid - center_y)**2)

    # 为中心k空间区域创建掩码
    central_mask = dist_from_center <= k_center_radius
    print(f"  Central k-space region radius: {k_center_radius} pixels")
    print(f"  Central k-space region covers {100 * np.sum(central_mask) / central_mask.size:.2f}% of k-space")

    # 计算增强前中心区域中有多少样本
    central_samples_before = np.count_nonzero(sparse_kspace[central_mask])
    central_coverage_before = 100 * central_samples_before / np.sum(central_mask)
    print(f"  Central k-space coverage before enhancement: {central_coverage_before:.2f}%")

    # 计算原始k空间数据
    print("Computing original k-space data...")
    # MRI的正确FFT序列：fft2 -> fftshift
    # 在MRI中，图像已经在空间域中，因此在fft2之前不需要ifftshift
    central_kspace = np.fft.fftshift(np.fft.fft2(image_data))
    print(f"  Original k-space shape: {central_kspace.shape}")
    print(f"  Original k-space center value magnitude: {np.abs(central_kspace[central_kspace.shape[0]//2, central_kspace.shape[1]//2]):.4f}")

    # 检查k空间中心是否正确定位
    max_idx = np.unravel_index(np.argmax(np.abs(central_kspace)), central_kspace.shape)
    print(f"  Maximum k-space value at: {max_idx}, expected center: {(central_kspace.shape[0]//2, central_kspace.shape[1]//2)}")

    # 复制中心区域以确保它完全采样
    sparse_kspace[central_mask] = central_kspace[central_mask]

    # 验证稀疏k空间是否具有正确的中心
    max_idx_sparse = np.unravel_index(np.argmax(np.abs(sparse_kspace)), sparse_kspace.shape)
    print(f"  Maximum sparse k-space value at: {max_idx_sparse}")

    # 计算增强后中心区域中有多少样本
    central_samples_after = np.count_nonzero(sparse_kspace[central_mask])
    central_coverage_after = 100 * central_samples_after / np.sum(central_mask)
    print(f"  Central k-space coverage after enhancement: {central_coverage_after:.2f}%")

    # 应用额外的k空间滤波以减少噪声
    print("Applying k-space filtering...")
    # 使用可调宽度的高斯滤波器
    filter_width = 0.5  # 较小的值=更锐利的截止
    k_filter = np.exp(-(dist_from_center / (image_size/2))**2 / filter_width)
    print(f"  Filter width parameter: {filter_width}")
    print(f"  Filter min value: {k_filter.min():.4f}")
    print(f"  Filter max value: {k_filter.max():.4f}")

    # 应用滤波器
    sparse_kspace_before = sparse_kspace.copy()
    sparse_kspace *= k_filter

    # 计算滤波效果
    filter_effect = np.sum(np.abs(sparse_kspace)) / np.sum(np.abs(sparse_kspace_before))
    print(f"  Filter reduced k-space energy to {filter_effect*100:.2f}% of original")

    return sparse_kspace, central_mask, central_coverage_before, central_coverage_after, dist_from_center

def balance_kspace_quadrants(sparse_kspace):
    """
    平衡k空间四个象限的能量

    参数:
        sparse_kspace: 稀疏k空间

    返回:
        balanced_kspace: 平衡后的k空间
    """
    print("Balancing k-space energy across quadrants...")
    k_center_y, k_center_x = sparse_kspace.shape[0]//2, sparse_kspace.shape[1]//2

    # 计算每个象限的能量
    k_quadrants = [
        np.sum(np.abs(sparse_kspace[:k_center_y, :k_center_x])),  # 左上
        np.sum(np.abs(sparse_kspace[:k_center_y, k_center_x:])),   # 右上
        np.sum(np.abs(sparse_kspace[k_center_y:, :k_center_x])),   # 左下
        np.sum(np.abs(sparse_kspace[k_center_y:, k_center_x:]))    # 右下
    ]
    print(f"  K-space quadrant energies: {k_quadrants}")

    # 计算平均能量
    avg_energy = np.mean(k_quadrants)
    print(f"  Average quadrant energy: {avg_energy}")

    # 创建缩放因子以平衡象限
    scaling_factors = [avg_energy / energy if energy > 0 else 1.0 for energy in k_quadrants]
    print(f"  Scaling factors: {scaling_factors}")

    # 应用缩放以平衡象限
    balanced_kspace = sparse_kspace.copy()
    balanced_kspace[:k_center_y, :k_center_x] *= scaling_factors[0]  # 左上
    balanced_kspace[:k_center_y, k_center_x:] *= scaling_factors[1]  # 右上
    balanced_kspace[k_center_y:, :k_center_x] *= scaling_factors[2]  # 左下
    balanced_kspace[k_center_y:, k_center_x:] *= scaling_factors[3]  # 右下

    return balanced_kspace

def reconstruct_image(balanced_kspace, image_size):
    """
    从k空间数据重建图像

    参数:
        balanced_kspace: 平衡后的k空间
        image_size: 图像大小

    返回:
        reconstructed_image: 重建的复数图像
        direct_quadrants: 直接IFFT方法的象限均值
        standard_quadrants: 标准IFFT方法的象限均值
    """
    print("Performing FFT reconstruction...")

    # 尝试直接IFFT方法
    print("Trying direct IFFT approach...")
    reconstructed_image_direct = np.fft.ifft2(balanced_kspace)

    # 尝试带移位的标准方法
    print("Trying standard IFFT approach with shifts...")
    reconstructed_image_standard = np.fft.fftshift(np.fft.ifft2(np.fft.ifftshift(balanced_kspace)))

    # 比较两种方法
    direct_magnitude = np.abs(reconstructed_image_direct)
    standard_magnitude = np.abs(reconstructed_image_standard)

    print(f"  Direct IFFT - min: {direct_magnitude.min():.4f}, max: {direct_magnitude.max():.4f}, mean: {direct_magnitude.mean():.4f}")
    print(f"  Standard IFFT - min: {standard_magnitude.min():.4f}, max: {standard_magnitude.max():.4f}, mean: {standard_magnitude.mean():.4f}")

    # 基于象限平衡选择更好的方法
    direct_quadrants = [
        np.mean(direct_magnitude[:image_size//2, :image_size//2]),  # 左上
        np.mean(direct_magnitude[:image_size//2, image_size//2:]),  # 右上
        np.mean(direct_magnitude[image_size//2:, :image_size//2]),  # 左下
        np.mean(direct_magnitude[image_size//2:, image_size//2:])   # 右下
    ]

    standard_quadrants = [
        np.mean(standard_magnitude[:image_size//2, :image_size//2]),  # 左上
        np.mean(standard_magnitude[:image_size//2, image_size//2:]),  # 右上
        np.mean(standard_magnitude[image_size//2:, :image_size//2]),  # 左下
        np.mean(standard_magnitude[image_size//2:, image_size//2:])   # 右下
    ]

    print(f"  Direct IFFT quadrant means: {direct_quadrants}")
    print(f"  Standard IFFT quadrant means: {standard_quadrants}")

    # 计算象限方差作为平衡度量
    direct_variance = np.var(direct_quadrants)
    standard_variance = np.var(standard_quadrants)

    print(f"  Direct IFFT quadrant variance: {direct_variance:.4f}")
    print(f"  Standard IFFT quadrant variance: {standard_variance:.4f}")

    # 基于视觉检查，Direct IFFT方法产生结构上更正确的图像
    # 因此我们将使用它，无论方差如何
    print("  Using direct IFFT approach (better structural integrity)")
    reconstructed_image = reconstructed_image_direct

    # 打印重建图像的象限均值
    quadrant_means = [
        np.mean(np.abs(reconstructed_image[:image_size//2, :image_size//2])),  # 左上
        np.mean(np.abs(reconstructed_image[:image_size//2, image_size//2:])),  # 右上
        np.mean(np.abs(reconstructed_image[image_size//2:, :image_size//2])),  # 左下
        np.mean(np.abs(reconstructed_image[image_size//2:, image_size//2:]))   # 右下
    ]
    print(f"  Quadrant mean values: {quadrant_means}")
    print(f"  Quadrant ratios: TL/BR={quadrant_means[0]/quadrant_means[3]:.2f}, TR/BL={quadrant_means[1]/quadrant_means[2]:.2f}")

    # 打印有关重建图像的信息
    print(f"  Reconstructed image shape: {reconstructed_image.shape}")
    print(f"  Reconstructed image min magnitude: {np.min(np.abs(reconstructed_image)):.6f}")
    print(f"  Reconstructed image max magnitude: {np.max(np.abs(reconstructed_image)):.6f}")
    print(f"  Reconstructed image mean magnitude: {np.mean(np.abs(reconstructed_image)):.6f}")

    return reconstructed_image, direct_quadrants, standard_quadrants

def postprocess_image(reconstructed_image):
    """
    对重建图像应用后处理以提高质量

    参数:
        reconstructed_image: 重建的复数图像

    返回:
        reconstructed_magnitude: 处理后的幅度图像
    """
    print("Applying post-processing to improve image quality...")

    # 获取幅度
    reconstructed_magnitude = np.abs(reconstructed_image)

    # 1. 归一化强度范围
    print("  Step 1: Normalizing intensity range")
    reconstructed_magnitude = (reconstructed_magnitude - reconstructed_magnitude.min()) / \
                             (reconstructed_magnitude.max() - reconstructed_magnitude.min())

    # 2. 应用边缘保留去噪
    print("  Step 2: Applying edge-preserving denoising")
    # 计算边缘图
    edge_x = np.abs(np.diff(reconstructed_magnitude, axis=0, prepend=0))
    edge_y = np.abs(np.diff(reconstructed_magnitude, axis=1, prepend=0))
    edge_magnitude = np.sqrt(edge_x**2 + edge_y**2)

    # 归一化边缘图到[0, 1]
    edge_magnitude = edge_magnitude / (edge_magnitude.max() + 1e-10)

    # 创建边缘掩码（1表示边缘，0表示平坦区域）
    edge_mask = edge_magnitude > 0.2

    # 基于边缘掩码应用不同级别的平滑
    print("  Step 3: Applying adaptive smoothing")
    smoothed_strong = gaussian_filter(reconstructed_magnitude, sigma=1.5)
    smoothed_weak = gaussian_filter(reconstructed_magnitude, sigma=0.5)

    # 基于边缘掩码组合平滑图像
    reconstructed_magnitude = np.where(edge_mask, smoothed_weak, smoothed_strong)

    # 3. 应用对比度增强
    print("  Step 4: Enhancing contrast")
    reconstructed_magnitude = np.power(reconstructed_magnitude, 0.85)  # Gamma校正

    # 打印有关处理后图像的统计信息
    print(f"Post-processing complete:")
    print(f"  Min value: {reconstructed_magnitude.min():.4f}")
    print(f"  Max value: {reconstructed_magnitude.max():.4f}")
    print(f"  Mean value: {reconstructed_magnitude.mean():.4f}")
    print(f"  Std deviation: {reconstructed_magnitude.std():.4f}")

    return reconstructed_magnitude
