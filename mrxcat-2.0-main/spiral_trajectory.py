#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
螺旋轨迹生成模块

此模块负责生成MRI螺旋采样轨迹。
"""

import numpy as np
from scipy.integrate import odeint

def vds(smax, gmax, T, N, Fcoeff, rmax):
    """
    生成变密度螺旋轨迹
    
    参数:
        smax: 最大转换率 (G/cm/s)
        gmax: 最大梯度 (G/cm)
        T: 采样周期 (s)
        N: 螺旋臂数量
        Fcoeff: FOV系数 [a, b]，其中FOV(r) = a + b*r/rmax
        rmax: 最大k空间半径 (1/cm)
        
    返回:
        k: 复数形式的k空间轨迹
        g: 复数形式的梯度
        s: 复数形式的转换率
        time: 时间点 (s)
        r: 径向距离
        theta: 角度 (rad)
    """
    # 计算所需的GmaxFOV
    a = Fcoeff[0]
    b = Fcoeff[1]
    GmaxFOV = 2 * np.pi * (a + b)
    print(f"Required GmaxFOV: {GmaxFOV}")
    
    # 定义ODE系统
    def fprime(y, t):
        r, theta = y
        FOV_r = a + b * r / rmax
        
        # 计算dr/dt和dtheta/dt
        if r < 0.01:  # 避免r=0时的除零错误
            dr = 0
            dtheta = 0
        else:
            lambda_r = np.sqrt(1 + (2 * np.pi * FOV_r / (N * r))**2)
            dr = gmax / lambda_r
            dtheta = 2 * np.pi * FOV_r * dr / (N * r)
        
        return [dr, dtheta]
    
    # 初始条件
    y0 = [0.01, 0]  # 从接近原点的位置开始
    
    # 创建时间点
    max_time = 50e-3  # 最大50ms
    time = np.arange(0, max_time, T)
    
    # 求解ODE
    sol = odeint(fprime, y0, time)
    r = sol[:, 0]
    theta = sol[:, 1]
    
    # 找到第一个超过rmax的点
    idx = np.where(r > rmax)[0]
    if len(idx) > 0:
        idx = idx[0]
        r = r[:idx]
        theta = theta[:idx]
        time = time[:idx]
    
    # 每10000个点打印一次状态
    for i in range(0, len(r), 10000):
        if i > 0:
            print(f"{i} points, |k|={r[i]:.6f}")
    
    # 计算k空间轨迹
    kx = r * np.cos(theta)
    ky = r * np.sin(theta)
    k = kx + 1j * ky
    
    # 计算梯度和转换率
    g = np.zeros_like(k)
    g[1:] = (k[1:] - k[:-1]) / T
    g[0] = g[1]  # 使用第二个点的值作为第一个点的值
    
    s = np.zeros_like(k)
    s[1:] = (g[1:] - g[:-1]) / T
    s[0] = s[1]  # 使用第二个点的值作为第一个点的值
    
    # 下采样以减少点数
    ds_factor = 1  # 可以增加以减少点数
    k_ds = k[::ds_factor]
    g_ds = g[::ds_factor]
    s_ds = s[::ds_factor]
    time_ds = time[::ds_factor]
    r_ds = r[::ds_factor]
    theta_ds = theta[::ds_factor]
    
    # 打印轨迹摘要
    print("Trajectory summary:")
    print(f"  k-space points: {len(k_ds)}")
    print(f"  Max gradient: {np.max(np.abs(g_ds)):.3f} G/cm")
    print(f"  Max slew rate: {np.max(np.abs(s_ds)):.3f} G/cm/s")
    print(f"  Readout time: {time_ds[-1]*1000:.2f} ms")
    
    return k_ds, g_ds, s_ds, time_ds, r_ds, theta_ds

def generate_spiral_trajectory(smax, gmax, T, N, Fcoeff, rmax):
    """
    生成螺旋轨迹并返回笛卡尔坐标
    
    参数:
        smax: 最大转换率 (G/cm/s)
        gmax: 最大梯度 (G/cm)
        T: 采样周期 (s)
        N: 螺旋臂数量
        Fcoeff: FOV系数
        rmax: 最大k空间半径
        
    返回:
        kx_spiral: 所有螺旋臂的x坐标
        ky_spiral: 所有螺旋臂的y坐标
        g: 梯度
        s: 转换率
        time: 时间点
        r: 径向距离
        theta: 角度
    """
    # 生成单个螺旋臂
    k, g, s, time, r, theta = vds(smax, gmax, T, N, Fcoeff, rmax)
    
    # 将复数k转换为实数坐标
    kx_arm = np.real(k)
    ky_arm = np.imag(k)
    
    # 为所有螺旋臂分配空间
    n_points = len(kx_arm)
    kx_spiral = np.zeros(n_points * N)
    ky_spiral = np.zeros(n_points * N)
    
    # 生成所有螺旋臂
    for i in range(N):
        # 计算旋转角度
        phi = 2 * np.pi * i / N
        
        # 应用旋转
        kx_rotated = kx_arm * np.cos(phi) - ky_arm * np.sin(phi)
        ky_rotated = kx_arm * np.sin(phi) + ky_arm * np.cos(phi)
        
        # 存储旋转后的坐标
        kx_spiral[i*n_points:(i+1)*n_points] = kx_rotated
        ky_spiral[i*n_points:(i+1)*n_points] = ky_rotated
    
    return kx_spiral, ky_spiral, g, s, time, r, theta
