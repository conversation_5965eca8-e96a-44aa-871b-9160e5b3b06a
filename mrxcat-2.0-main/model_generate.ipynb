{"cells": [{"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "Key backend: 'module://ipympl.backend_nbagg' is not a valid value for backend; supported values are ['gtk3agg', 'gtk3cairo', 'gtk4agg', 'gtk4cairo', 'macosx', 'nbagg', 'notebook', 'qtagg', 'qtcairo', 'qt5agg', 'qt5cairo', 'tkagg', 'tkcairo', 'webagg', 'wx', 'wxagg', 'wxcairo', 'agg', 'cairo', 'pdf', 'pgf', 'ps', 'svg', 'template', 'inline']", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[6], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;66;03m# Cell 1: Initial setup\u001b[39;00m\n\u001b[0;32m----> 2\u001b[0m \u001b[43mget_ipython\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun_line_magic\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mmatplotlib\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mipympl\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m      4\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mnumpy\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mnp\u001b[39;00m\n\u001b[1;32m      5\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mmatplotlib\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpyplot\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mplt\u001b[39;00m\n", "File \u001b[0;32m/opt/anaconda3/envs/mrxcat20/lib/python3.9/site-packages/IPython/core/interactiveshell.py:2456\u001b[0m, in \u001b[0;36mInteractiveShell.run_line_magic\u001b[0;34m(self, magic_name, line, _stack_depth)\u001b[0m\n\u001b[1;32m   2454\u001b[0m     kwargs[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mlocal_ns\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mget_local_scope(stack_depth)\n\u001b[1;32m   2455\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mbuiltin_trap:\n\u001b[0;32m-> 2456\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[43mfn\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   2458\u001b[0m \u001b[38;5;66;03m# The code below prevents the output from being displayed\u001b[39;00m\n\u001b[1;32m   2459\u001b[0m \u001b[38;5;66;03m# when using magics with decorator @output_can_be_silenced\u001b[39;00m\n\u001b[1;32m   2460\u001b[0m \u001b[38;5;66;03m# when the last Python token in the expression is a ';'.\u001b[39;00m\n\u001b[1;32m   2461\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mgetattr\u001b[39m(fn, magic\u001b[38;5;241m.\u001b[39mMAGIC_OUTPUT_CAN_BE_SILENCED, \u001b[38;5;28;01mFalse\u001b[39;00m):\n", "File \u001b[0;32m/opt/anaconda3/envs/mrxcat20/lib/python3.9/site-packages/IPython/core/magics/pylab.py:99\u001b[0m, in \u001b[0;36mPylabMagics.matplotlib\u001b[0;34m(self, line)\u001b[0m\n\u001b[1;32m     97\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAvailable matplotlib backends: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m%\u001b[39m backends_list)\n\u001b[1;32m     98\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m---> 99\u001b[0m     gui, backend \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mshell\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43menable_matplotlib\u001b[49m\u001b[43m(\u001b[49m\u001b[43margs\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgui\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mlower\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43misinstance\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43margs\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgui\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mstr\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01melse\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43margs\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgui\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    100\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_show_matplotlib_backend(args\u001b[38;5;241m.\u001b[39mgui, backend)\n", "File \u001b[0;32m/opt/anaconda3/envs/mrxcat20/lib/python3.9/site-packages/IPython/core/interactiveshell.py:3645\u001b[0m, in \u001b[0;36mInteractiveShell.enable_matplotlib\u001b[0;34m(self, gui)\u001b[0m\n\u001b[1;32m   3641\u001b[0m         \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mWarning: Cannot change to a different GUI toolkit: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[1;32m   3642\u001b[0m                 \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m Using \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m instead.\u001b[39m\u001b[38;5;124m'\u001b[39m \u001b[38;5;241m%\u001b[39m (gui, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpylab_gui_select))\n\u001b[1;32m   3643\u001b[0m         gui, backend \u001b[38;5;241m=\u001b[39m pt\u001b[38;5;241m.\u001b[39mfind_gui_and_backend(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpylab_gui_select)\n\u001b[0;32m-> 3645\u001b[0m \u001b[43mpt\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mactivate_matplotlib\u001b[49m\u001b[43m(\u001b[49m\u001b[43mbackend\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   3646\u001b[0m configure_inline_support(\u001b[38;5;28mself\u001b[39m, backend)\n\u001b[1;32m   3648\u001b[0m \u001b[38;5;66;03m# Now we must activate the gui pylab wants to use, and fix %run to take\u001b[39;00m\n\u001b[1;32m   3649\u001b[0m \u001b[38;5;66;03m# plot updates into account\u001b[39;00m\n", "File \u001b[0;32m/opt/anaconda3/envs/mrxcat20/lib/python3.9/site-packages/IPython/core/pylabtools.py:361\u001b[0m, in \u001b[0;36mactivate_matplotlib\u001b[0;34m(backend)\u001b[0m\n\u001b[1;32m    356\u001b[0m matplotlib\u001b[38;5;241m.\u001b[39minteractive(\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[1;32m    358\u001b[0m \u001b[38;5;66;03m# Matplotlib had a bug where even switch_backend could not force\u001b[39;00m\n\u001b[1;32m    359\u001b[0m \u001b[38;5;66;03m# the rcParam to update. This needs to be set *before* the module\u001b[39;00m\n\u001b[1;32m    360\u001b[0m \u001b[38;5;66;03m# magic of switch_backend().\u001b[39;00m\n\u001b[0;32m--> 361\u001b[0m \u001b[43mmatplotlib\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrcParams\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mbackend\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m \u001b[38;5;241m=\u001b[39m backend\n\u001b[1;32m    363\u001b[0m \u001b[38;5;66;03m# Due to circular imports, pyplot may be only partially initialised\u001b[39;00m\n\u001b[1;32m    364\u001b[0m \u001b[38;5;66;03m# when this function runs.\u001b[39;00m\n\u001b[1;32m    365\u001b[0m \u001b[38;5;66;03m# So avoid needing matplotlib attribute-lookup to access pyplot.\u001b[39;00m\n\u001b[1;32m    366\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mmatplotlib\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m pyplot \u001b[38;5;28;01mas\u001b[39;00m plt\n", "File \u001b[0;32m/opt/anaconda3/envs/mrxcat20/lib/python3.9/site-packages/matplotlib/__init__.py:741\u001b[0m, in \u001b[0;36mRcParams.__setitem__\u001b[0;34m(self, key, val)\u001b[0m\n\u001b[1;32m    739\u001b[0m         cval \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mvalidate[key](val)\n\u001b[1;32m    740\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m ve:\n\u001b[0;32m--> 741\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m<PERSON>ey \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mkey\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mve\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m    742\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_set(key, cval)\n\u001b[1;32m    743\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n", "\u001b[0;31mValueError\u001b[0m: Key backend: 'module://ipympl.backend_nbagg' is not a valid value for backend; supported values are ['gtk3agg', 'gtk3cairo', 'gtk4agg', 'gtk4cairo', 'macosx', 'nbagg', 'notebook', 'qtagg', 'qtcairo', 'qt5agg', 'qt5cairo', 'tkagg', 'tkcairo', 'webagg', 'wx', 'wxagg', 'wxcairo', 'agg', 'cairo', 'pdf', 'pgf', 'ps', 'svg', 'template', 'inline']"]}], "source": ["# Cell 1: Initial setup\n", "%matplotlib ipympl\n", "\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "import ipywidgets as widgets\n", "from IPython.display import display"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Cell 2: Define helper functions\n", "# Define constant for dimension size\n", "def read_phantom_log(log_file_path):\n", "    \"\"\"\n", "    Read and parse XCAT phantom log file to extract basic parameters.\n", "    \"\"\"\n", "    # Default values for voxel dimensions\n", "    DEFAULT_PIXEL_WIDTH = 0.1  # cm, typical XCAT value\n", "    DEFAULT_SLICE_WIDTH = 0.1  # cm, typical XCAT value\n", "    DEFAULT_ARRAY_SIZE = 512   # pixels, typical XCAT value\n", "    \n", "    params = {\n", "        'pixel_width': DEFAULT_PIXEL_WIDTH,  # Set default\n", "        'slice_width': DEFAULT_SLICE_WIDTH,  # Set default\n", "        'voxel_volume': DEFAULT_PIXEL_WIDTH * DEFAULT_PIXEL_WIDTH * DEFAULT_SLICE_WIDTH,\n", "        'array_size': DEFAULT_ARRAY_SIZE,\n", "        'start_slice': None,      \n", "        'end_slice': None,\n", "        'subvoxel_index': None,\n", "        'time_per_frame': None,\n", "        'total_frames': None\n", "    }\n", "    \n", "    try:\n", "        with open(log_file_path, 'r') as f:\n", "            lines = f.readlines()\n", "            \n", "            # First pass: collect all numeric values\n", "            numeric_values = {}\n", "            for line in lines:\n", "                line = line.strip()\n", "                if '=' in line:\n", "                    key, value = line.split('=', 1)\n", "                    key = key.strip()\n", "                    value = value.split('#')[0].strip()  # Remove comments\n", "                    try:\n", "                        numeric_values[key] = float(value)\n", "                    except ValueError:\n", "                        continue\n", "\n", "            # Second pass: process parameters with potential expressions\n", "            for line in lines:\n", "                line = line.strip().lower()  # Convert to lowercase for better matching\n", "\n", "                def extract_value(text):\n", "                    \"\"\"Extract numeric value, evaluating expressions if necessary\"\"\"\n", "                    text = text.split('#')[0].strip()  # Remove comments\n", "                    try:\n", "                        return float(text)\n", "                    except ValueError:\n", "                        if '*' in text:\n", "                            # Handle multiplication expressions\n", "                            parts = text.split('*')\n", "                            try:\n", "                                return float(parts[0].strip()) * float(parts[1].strip())\n", "                            except ValueError:\n", "                                # Try using stored numeric values\n", "                                val1 = numeric_values.get(parts[0].strip(), 0)\n", "                                val2 = numeric_values.get(parts[1].strip(), 0)\n", "                                return val1 * val2\n", "                        return None\n", "                \n", "                if 'pixel width' in line or 'pixelwidth' in line:\n", "                    try:\n", "                        value = line.split('=')[1].split('#')[0].strip()\n", "                        params['pixel_width'] = float(value)\n", "                    except:\n", "                        print(f\"Warning: Could not parse pixel width, using default {DEFAULT_PIXEL_WIDTH}\")\n", "                \n", "                elif 'slice width' in line or 'slicewidth' in line:\n", "                    try:\n", "                        value = line.split('=')[1].split('#')[0].strip()\n", "                        params['slice_width'] = float(value)\n", "                    except:\n", "                        print(f\"Warning: Could not parse slice width, using default {DEFAULT_SLICE_WIDTH}\")\n", "                \n", "                elif 'array_size' in line:\n", "                    val = line.split('=')[1].strip()\n", "                    try:\n", "                        params['array_size'] = int(val)\n", "                    except ValueError:\n", "                        print(f\"Warning: Could not parse array size, using default {DEFAULT_ARRAY_SIZE}\")\n", "                        continue\n", "                \n", "                elif 'starting slice number' in line:\n", "                    val = line.split('=')[1].strip()\n", "                    try:\n", "                        params['start_slice'] = int(val)\n", "                    except ValueError:\n", "                        continue\n", "                \n", "                elif 'ending slice number' in line:\n", "                    val = line.split('=')[1].strip()\n", "                    try:\n", "                        params['end_slice'] = int(val)\n", "                    except ValueError:\n", "                        continue\n", "                \n", "                elif 'time_per_frame' in line:\n", "                    params['time_per_frame'] = extract_value(line.split('=')[1])\n", "                \n", "                elif 'total output frames' in line:\n", "                    val = line.split('=')[1].strip()\n", "                    try:\n", "                        params['total_frames'] = int(val)\n", "                    except ValueError:\n", "                        continue\n", "                \n", "        # Calculate voxel volume\n", "        params['voxel_volume'] = (params['pixel_width'] ** 2) * params['slice_width']\n", "        \n", "        print(\"\\nVoxel dimensions:\")\n", "        print(f\"Pixel width: {params['pixel_width']:.4f} cm\")\n", "        print(f\"Slice width: {params['slice_width']:.4f} cm\")\n", "        print(f\"Voxel volume: {params['voxel_volume']:.6f} ml\")\n", "        \n", "        return params\n", "        \n", "    except Exception as e:\n", "        print(f\"Error parsing log file: {str(e)}\")\n", "        print(\"Using default parameters\")\n", "        return params\n", "\n", "def load_phantom(filepath, log_params=None):\n", "    \"\"\"Load XCAT phantom binary data using parameters from log file.\"\"\"\n", "    try:\n", "        data = np.fromfile(filepath, dtype=np.float32)\n", "        total_size = data.size\n", "        print(f\"Total size of data: {total_size}\")\n", "\n", "        # Use array_size from log file or auto-detect\n", "        if log_params and log_params['array_size']:\n", "            DIM_SIZE = log_params['array_size']\n", "        else:\n", "            possible_dim_sizes = [256,512, 920, 1024]\n", "            DIM_SIZE = next((dim for dim in possible_dim_sizes \n", "                           if total_size % (dim * dim) == 0), None)\n", "            \n", "            if not DIM_SIZE:\n", "                raise ValueError(\"Could not determine array size\")\n", "\n", "        slice_size = DIM_SIZE * DIM_SIZE\n", "        n_slices = total_size // slice_size\n", "        data = data.reshape((n_slices, DIM_SIZE, DIM_SIZE))\n", "        \n", "        # Get voxel dimensions with defaults if not available\n", "        voxel_dims = (\n", "            log_params['pixel_width'],\n", "            log_params['pixel_width'],\n", "            log_params['slice_width']\n", "        ) if log_params else (0.0977, 0.0977, 0.0977)\n", "        \n", "        print(f\"Loaded phantom with shape {data.shape}\")\n", "        print(f\"Voxel dimensions (cm): {voxel_dims}\")\n", "        \n", "        return data, voxel_dims\n", "\n", "    except Exception as e:\n", "        print(f\"Error loading phantom: {e}\")\n", "        return None, None\n", "\n", "def create_slice_viewer(data, voxel_dims, view_type='Axial'):\n", "    \"\"\"\n", "    Create interactive slice viewer with proper spatial scaling.\n", "    \n", "    Parameters:\n", "    -----------\n", "    data : ndarray\n", "        3D phantom data\n", "    voxel_dims : tuple\n", "        (pixel_width, pixel_width, slice_width) in cm\n", "    view_type : str\n", "        'Axial', 'Sagittal', or 'Coronal'\n", "    \"\"\"\n", "    fig, ax = plt.subplots(figsize=(10, 10))\n", "    plt.subplots_adjust(bottom=0.25)\n", "    \n", "    def get_slice_data(idx):\n", "        if view_type == 'Axial':\n", "            return data[idx, :, :]\n", "        elif view_type == 'Sagittal':\n", "            return data[:, idx, :]\n", "        else:  # <PERSON><PERSON>l\n", "            return data[:, :, idx]\n", "    \n", "    # Set default slice and extent\n", "    slice_default = data.shape[0]//2 if view_type == 'Axial' else data.shape[1]//2\n", "    img = get_slice_data(slice_default)\n", "    \n", "    # Calculate proper extent based on view type\n", "    if view_type == 'Axial':\n", "        extent = [0, data.shape[2]*voxel_dims[0], \n", "                 0, data.shape[1]*voxel_dims[1]]\n", "        xlabel, ylabel = 'X (cm)', 'Y (cm)'\n", "    elif view_type == 'Sagittal':\n", "        extent = [0, data.shape[2]*voxel_dims[0], \n", "                 0, data.shape[0]*voxel_dims[2]]\n", "        xlabel, ylabel = 'X (cm)', 'Z (cm)'\n", "    else:  # <PERSON><PERSON>l\n", "        extent = [0, data.shape[1]*voxel_dims[1], \n", "                 0, data.shape[0]*voxel_dims[2]]\n", "        xlabel, ylabel = 'Y (cm)', 'Z (cm)'\n", "    \n", "    im = ax.imshow(img, extent=extent, cmap='gray')\n", "    ax.set_xlabel(xlabel)\n", "    ax.set_ylabel(ylabel)\n", "    ax.set_title(f'{view_type} Slice {slice_default}')\n", "    \n", "    # Add colorbar\n", "    cbar = fig.colorbar(im, ax=ax)\n", "    cbar.set_label('Intensity')\n", "    \n", "    def update_slice(slice_num):\n", "        img = get_slice_data(slice_num)\n", "        im.set_data(img)\n", "        ax.set_title(f'{view_type} Slice {slice_num}')\n", "        im.set_clim(vmin=data.min(), vmax=data.max())\n", "        fig.canvas.draw_idle()\n", "    \n", "    max_slice = data.shape[0]-1 if view_type == 'Axial' else data.shape[1]-1\n", "    slice_slider = widgets.IntSlider(\n", "        min=0,\n", "        max=max_slice,\n", "        step=1,\n", "        value=slice_default,\n", "        description=f'{view_type} Slice:',\n", "        continuous_update=False\n", "    )\n", "    \n", "    interactive_plot = widgets.interactive_output(update_slice, {'slice_num': slice_slider})\n", "    display(widgets.VBox([slice_slider, interactive_plot]))\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Warning: Could not parse pixel width, using default 0.1\n", "Warning: Could not parse slice width, using default 0.1\n", "Warning: Could not parse pixel width, using default 0.1\n", "\n", "Voxel dimensions:\n", "Pixel width: 0.1000 cm\n", "Slice width: 0.1000 cm\n", "Voxel volume: 0.001000 ml\n", "Total size of data: 131334144\n", "Loaded phantom with shape (501, 512, 512)\n", "Voxel dimensions (cm): (0.1, 0.1, 0.1)\n"]}], "source": ["# Cell 3: Load data\n", "# phantom_file = \"/home/<USER>/XCAT_Project/XCAT_V2_LINUX/output_phantom_act_1.bin\"\n", "# Example usage:\n", "phantom_logfile = \"/Users/<USER>/Documents/important document backup/Local python code/MRXCAT2/mrxcat-2.0-main/female50/singleframe_log\"\n", "phantom_file = \"/Users/<USER>/Documents/important document backup/Local python code/MRXCAT2/mrxcat-2.0-main/female50/singleframe_act_1.bin\"\n", "params = read_phantom_log(phantom_logfile)\n", "phantom_data, voxel_dims = load_phantom(phantom_file, params)\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x1000 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0fb76d672ac646058e2e9290559600e6", "version_major": 2, "version_minor": 0}, "text/plain": ["VBox(children=(IntSlider(value=250, continuous_update=False, description='Axial Slice:', max=500), Output()))"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Cell 4: Axial View\n", "if phantom_data is not None:\n", "    create_slice_viewer(phantom_data, voxel_dims, 'Axial')"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAyQAAAKyCAYAAAAkQhruAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuNCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8ekN5oAAAACXBIWXMAAA9hAAAPYQGoP6dpAAEAAElEQVR4nOzdd5xU9b0//tc50+v2zu7Sll4VgoAELGAUieWapjHtJjExTb25JsYkQq6C4SbG5OLVi9dCcsPPEjWJsUFUsIANFBAQELYBu2zfmdnpc87vD77nOLM7u9NOnXk/H495wM6cOecz7ZzP+1PeH4bneR6EEEIIIYQQogJW7QIQQgghhBBCChcFJIQQQgghhBDVUEBCCCGEEEIIUQ0FJIQQQgghhBDVUEBCCCGEEEIIUQ0FJIQQQgghhBDVUEBCCCGEEEIIUQ0FJIQQQgghhBDVUEBCCCGEEEIIUQ0FJIQQzXr77bdx1VVXoaGhARaLBVVVVVi8eDH+7d/+TfZjMwyDtWvXin8fOnQIa9euRUtLy4htt27dinvvvTen461YsQIrVqxIuV0kEsH//M//YOHChSgtLYXdbkdjYyOuuOIKPPPMM+J2LS0tYBgGjz76qHjfo48+CoZhkr4GuXR0dODnP/85Fi9ejPLycrjdbpx77rnYvHkzYrFYwrY7duwAwzBJb2+99daIfUciEdxzzz2YPXs2bDYbiouLsWTJEuzatUupl0cIIUQCRrULQAghyTz33HP47Gc/ixUrVmDjxo2oqalBR0cH3nvvPTz22GP47W9/K+vxd+/ejXHjxol/Hzp0COvWrcOKFSswfvz4hG23bt2KDz/8EDfddJOsZQKA66+/Hk8//TRuuukmrFu3DhaLBSdOnMCLL76Il156CVddddWoz129ejV2796Nmpoa2csp2LNnD/74xz/iK1/5Cn7xi1/AZDLhhRdewHe/+1289dZbePjhh0c8Z/369bjgggsS7ps1a1bC37FYDFdddRXeeOMN3HrrrViyZAmGhoawZ88eDA0NyfqaCCGESIsCEkKIJm3cuBETJkzASy+9BKPxk1PVF7/4RWzcuFH245933nmyHyNTzc3NePzxx/HLX/4S69atE++/6KKL8K1vfQscx435/IqKClRUVMhdzARLly7F8ePHYTKZxPtWrlyJcDiM++67D+vWrUN9fX3Cc5qamlK+///1X/+FF154AW+++WbCtqtXr5b2BRBCCJEdDdkihGhSb28vysvLE4IRAcsmnroef/xxrFq1CjU1NbDZbJg+fTp++tOfJm0pf/DBBzFlyhRYLBbMmDEDW7duxde+9rURvR7xQ7YeffRRfO5znwMAXHDBBeIwokcffRQrVqzAc889h9bW1oQhRoJ169Zh0aJFKC0thdvtxjnnnIOHHnoIPM9n9Z4AGLWHY/j7MtxoQ7ZefPFFXHTRRSgqKoLdbsf06dOxYcOGhG3ee+89fPazn0VpaSmsVivmz5+PJ554ImWZS0pKEoIRwac+9SkAwMmTJ1PuI5nf//73+PSnP63JwJEQQkhmKCAhhGjS4sWL8fbbb+OHP/wh3n77bUQikVG3PXbsGC677DI89NBDePHFF3HTTTfhiSeewJo1axK227x5M7797W9jzpw5ePrpp/Hzn/8c69atw44dO8Ysy+rVq7F+/XoAwH333Yfdu3dj9+7dWL16Nf77v/8bS5cuRXV1tXj/7t27xee2tLTghhtuwBNPPIGnn34aV199NX7wgx/gP/7jPzJ+T6ZPn47i4mKsW7cOmzdvlmQuyEMPPYTLLrsMHMfhgQcewLPPPosf/vCHCYHCq6++iqVLl2JgYAAPPPAA/va3v2HevHn4whe+kDBHJROvvPIKjEYjpkyZMuKx733vezAajXC73bjkkkvwxhtvJDze3t6OlpYWzJ49Gz/72c9QVVUFo9GImTNnYsuWLVmVhxBCiIp4QgjRoJ6eHv7888/nAfAAeJPJxC9ZsoTfsGED7/V6R30ex3F8JBLhd+7cyQPg9+3bx/M8z8diMb66uppftGhRwvatra28yWTiGxsbE+4HwN9xxx3i308++SQPgH/11VdHHHP16tUjnp9MLBbjI5EI/6tf/YovKyvjOY4TH1u+fDm/fPnylPt47rnn+PLycvF9KSsr4z/3uc/xf//73xO2a25u5gHwjzzyiHjfI488wgPgm5ubeZ7nea/Xy7vdbv78889PKMtw06ZN4+fPn89HIpGE+y+//HK+pqaGj8ViKcsd76WXXuJZluVvvvnmhPv37t3L/+hHP+KfeeYZ/rXXXuMffvhhfvr06bzBYOBffPFFcbvdu3fzAHi3283PmDGDf+KJJ/iXXnqJv+aaa3gA/ObNmzMqDyGEEHVRDwkhRJPKysrw+uuv491338Xdd9+NK664AkePHsVtt92G2bNno6enR9z2xIkTuPbaa1FdXQ2DwQCTyYTly5cDAA4fPgwAOHLkCDo7O/H5z38+4TgNDQ1YunSpbK/jlVdewcUXX4yioiKxbL/85S/R29uLrq6ujPd32WWXoa2tDc888wx+/OMfY+bMmfjrX/+Kz372s/j+97+f0b527doFj8eDG2+8MWGYWbyPP/4YH330Ea677joAQDQaFW+XXXYZOjo6cOTIkbSPuXfvXnz+85/HeeedN2JY2Pz583HvvffiyiuvxLJly/D1r38du3btQk1NDW699VZxO2GuTDAYxPPPP4/Pfe5zWLVqFZ544gmcc845+NWvfpXR+0AIIURdNKmdEKJpCxYswIIFCwCcTfP6k5/8BL/73e+wceNGbNy4ET6fD8uWLYPVasWdd96JKVOmwG63o729HVdffTUCgQCAT+ZfVFVVjThGVVUVmpubJS/7O++8g1WrVmHFihV48MEHMW7cOJjNZvz1r3/FXXfdJZYtUzabDVdeeSWuvPJKAEBbWxsuvfRS3Hffffjud7+LmTNnprWf7u5uAEjIJjbcmTNnAAA//vGP8eMf/zjpNvHB4Vjef/99rFy5Ek1NTXj++edhsVhSPqe4uBiXX345HnjgAQQCAdhsNpSVlQEApk2bhsbGRnFbhmFwySWXYMOGDejq6kJlZWVa5SKEkFSCwSDC4bAqxzabzbBaraocWykUkBBCdMNkMuGOO+7A7373O3z44YcAzvZAnD59Gjt27BB7RQBgYGAg4blCJVaoYMfr7OyUpbyPPfYYTCYT/vGPfyRcTP76179KepyGhgZ8+9vfxk033YSDBw+mHZAIGbfGmlheXl4OALjttttw9dVXJ91m6tSpKY/1/vvv4+KLL0ZjYyO2bduGoqKitMoIQEwAIPTiTJo0CXa7fcxtU03wJ4SQdAWDQUyYMEG2a0Uq1dXVaG5uzuughAISQogmdXR0JM0mJQzBqq2tBfBJJXV4a/v//M//JPw9depUVFdX44knnsAtt9wi3t/W1oZdu3aJ+xuNsP9kvRoWiyXp/QzDwGg0wmAwiPcFAgH86U9/GvNYo/F6vWAYBk6nc8Rjw9+XdCxZsgRFRUV44IEH8MUvfjHpsK2pU6eiqakJ+/btEyf2Z+qDDz7AxRdfjHHjxmH79u0oKSlJ+7n9/f34xz/+gXnz5okXY6PRiCuuuAJ/+ctf0NLSImZI43keL774IiZNmiQGUoQQkqtwOIzOzk60t7fD7XYremyPx4P6+nqEw2EKSAghRGmXXHIJxo0bhzVr1mDatGngOA4ffPABfvvb38LpdOJHP/oRgLOV6pKSEnznO9/BHXfcAZPJhD//+c/Yt29fwv5YlsW6detwww034JprrsE3vvENDAwMYN26daipqUnZoi4szLd582a4XC5YrVZMmDABZWVlmD17Np5++mncf//9OPfcc8GyLBYsWIDVq1fjnnvuwbXXXotvf/vb6O3txW9+85u0hiolc+TIEVxyySX44he/iOXLl6Ompgb9/f147rnnsHnzZqxYsQJLlixJe39OpxO//e1v8c1vfhMXX3wxvvWtb6Gqqgoff/wx9u3bh02bNgE4G9xdeumluOSSS/C1r30NdXV16Ovrw+HDh7F37148+eSTY5b54osvBgDcddddOHbsGI4dOyY+PmnSJLGn5tprr0VDQwMWLFiA8vJyHDt2DL/97W9x5syZEdm8/uM//gMvvPACPvOZz2Dt2rVwu9343//9X+zbty+tdMSEEJIpt9uteEBSMFSeVE8IIUk9/vjj/LXXXss3NTXxTqeTN5lMfENDA3/99dfzhw4dSth2165d/OLFi3m73c5XVFTw3/zmN/m9e/eOyDLF8zy/efNmfvLkybzZbOanTJnCP/zww/wVV1zBz58/P2E7DMuyxfM8f++99/ITJkzgDQZDwr77+vr4a665hi8uLuYZhuHjT60PP/wwP3XqVN5isfATJ07kN2zYwD/00EMJ2a54Pr0sW/39/fydd97JX3jhhXxdXR1vNpt5h8PBz5s3j7/zzjt5v98vbptOli3B888/zy9fvpx3OBy83W7nZ8yYwf/6179O2Gbfvn385z//eb6yspI3mUx8dXU1f+GFF/IPPPDAmGUWjjnaLb58GzZs4OfNm8cXFRXxBoOBr6io4K+66ir+nXfeSbrvAwcO8KtXr+ZdLhdvtVr58847j3/22WfHLA8hhGRqcHCQB8APDAzwHMcpehsYGOAB8IODg2q/DbJieD6L1bkIISRPDAwMYMqUKbjyyiuxefNmtYtDCCFEYzweD4qKijAwMKDKkK3i4mIMDg7mde8MDdkihBSMzs5O3HXXXbjgggtQVlaG1tZW/O53v4PX6xWHgBFCCCHJ8DwPpdvxC6XfgAISQkjBsFgsaGlpwY033oi+vj7Y7Xacd955eOCBB9LOTEUIIYQQaVFAQggpGCUlJXj22WfVLgYhhBBC4lBAQgghhBBCSAo0ZEs+qq4ctXbtWjAMk3Crrq4WH+d5HmvXrkVtbS1sNhtWrFiBgwcPqlhiQgghhBBCiJRUX8p25syZ6OjoEG8HDhwQH9u4cSPuuecebNq0Ce+++y6qq6uxcuVKeL1eFUtMCCGEEEIKjdBDovStEKg+ZMtoNCb0igh4nse9996L22+/HVdffTUAYMuWLaiqqsLWrVtxww03pLV/juNw+vRpuFyupKsQE0IIIYQQdfE8D6/Xi9ra2pQL1ZL8o3pAcuzYMdTW1sJisWDRokVYv349Jk6ciObmZnR2dmLVqlXithaLBcuXL8euXbtGDUhCoRBCoZD496lTpzBjxgzZXwchhBBCCMlNe3s7xo0bp3YxiMJUDUgWLVqEP/7xj5gyZQrOnDmDO++8E0uWLMHBgwfR2dkJAKiqqkp4TlVVFVpbW0fd54YNG7Bu3boR90+YMGFExB2LxSR4FZmJRCLo7u5GOBxW/NhEfSUlJSgqKkq5HcuyuuvRk+r3pMfXniue58FxnPj3tGnTMHfu3KTbMgwDk8kke5kikciYQwU6Ozuxc+dOAPn3mXEcp9owCYZhNNE6PPw7GS8ajeLkyZMKl4gUCpfLpXYRRkWT2uWjakBy6aWXiv+fPXs2Fi9ejEmTJmHLli0477zzAGDERY7n+TEvfLfddhtuueUW8W+Px4P6+nqwLAuDwTBiX0qzWCwjykEKR7qVDZZlNVEpyYRUvyc9vvZcDa/4mUwmWCyWpNsyDAOz2Sx7mcxmM6LR6Kifq9lsFj+nfPvMUl1n5MQwjCauEaMFIwDy6rMm2pNPjRskfZo6qzgcDsyePRvHjh0T55UIPSWCrq6uEb0m8SwWC9xud8KNEKIvY1WG8o1WX2uqIMNms6G+vl7BEhFCiLpoUrt8NBWQhEIhHD58GDU1NZgwYQKqq6uxfft28fFwOIydO3diyZIlKpaSECInrVbQ5RT/mq1WqyI9ILkqLi4We7JJfinE3yAhRF2qDtn68Y9/jDVr1qChoQFdXV2488474fF48NWvfhUMw+Cmm27C+vXr0dTUhKamJqxfvx52ux3XXnutmsUmhBDZnH/++UkzD2qZUIGloTz5oZBaZQkh2qBqQHLy5El86UtfQk9PDyoqKnDeeefhrbfeQmNjIwDg1ltvRSAQwI033oj+/n4sWrQI27Zty3nCE8dx1AJEVNHX14dgMKi7CqfSqIKbnMFggNGoenJEQggpSDSpXT6qXtkee+yxMR9nGAZr167F2rVrlSkQIQrIt5OLmhmJiLyE7FnRaFTtohBCCMlj1PxICMkZBST5KVU6X6PRiPnz51OvjUQYhqEMQ4RoGE1qlw9dRQjRKI7jND9kSc6hj8I6CFp/D5Q01toQchnreAaDATNnzsTBgwdpGKwEtLIGCSGEKI0CEkI0SE8V8UwqoumusRCLxcRWIT29F7mKX9sjGZr/RgghJB9RQEIIUUxRURGamppSbnfs2DEMDAyI3dX5HJAIr43jOFxxxRWjLohICCFEXTSpXT4FF5BooYWxqqoKg4OD6O/vV7UchOQivhdjNAzDYM6cOQkreqdj4sSJCT0kBw8ezK2whOiAkCBCCyu1E0KIkgouINGCVBNFSf5yuVxwu91qFyNnYwUjNTU1YmpuhmGyWuRveIVs0qRJAICuri54vd6M90dIumKxmNpFUFU6DQ2EFCrqIZEPBSSEKMhkMul+SE6yNL8Mw6CqqgoAUFpaCrvdLukxhSAuFouBYRh4PB5J968VDQ0NGBwcRDAYVLsoaWFZFvX19TAYDHmTGljti3984gI1hiqq/foJIYWJAhJCSNqSDXlkWRZ2ux319fWyH7+kpCSh8uv3+2U/ppImTpyI9vZ2DA4OZr2P+B4kg8GQNDjkOA5DQ0MAAKfTmXWPrdFoRFNTE4xGI0KhUHYFJiPE/8byef4UIXpDPSTyoYCEEJIVYc0Eu92O6dOnK3Zct9st9pjs27dP9TlhUopEIpg6dWpOQzp37twpDjsqKSnBvHnzRmwzODiIPXv2AADmz5+fc6+dwWDIi4qz1r5LavaUEEKIkgoqIKGxsYRkb3jvSFNTE4qKilQsETB37lx89NFHCAQCqpYjVyzLgmVZ3Hzzzbj11ltzCvCWL1+ecpuioiJceOGFWR8jH2kh4UkyFJQQQgoBneEIISkNr6zNnDlTM5PzJ06ciNLSUrWLIQlqMCF6wLIsGhoaKDkLKTi0Urt8CqqHhBCSufhJ7CzLYsKECbDZbJqpjJjNZlRWVsJoNKKrq0vt4hSUUCiEhx9+WDeT8EeTLFGDlggT3bXSSyL06BFCiFQKJiDheV4zFSjgbCXKbrfn3aRckl/ie0ZMJhNKSko02Rths9lQWlqKSCRC6/soKBqN4u23386LCqrWA5L4RgFCiDpoUrt8CubMprUWMLvdjpKSErWLQciohg/TslqtaGxsVLFEY7PZbKivr89q3ROt0FKjCdEeLc5xIYQQKRRMQEIISV+yCb56qCwbDAbMnDlTt63IBoNBF+8zIYQQIqWCGbJFCElPsmCktrYWtbW1KpWIaBXDMDAa9X0Z0Vv2xWg0mhdD5AjRIxqyJR86oxFCRKOlPmVZVlct91OnTpV8tXiSf/QWjBBCSL7Sd9OWzhmNRpSVlaG3t1ftohCiuXlWubBaraiurkZPTw88Ho/axclKMBhEa2urKseeOnVqym06OzvxwgsvKFAa+eTL911KLMsWVKpRQjJBPSTyoYBERQaDAS6XiwISMoLSvRFjLQrncrlgtVoVLY8UioqKwHEcotGorrLZHTt2DEajESaTCe3t7aqUoaSkJOl3sLy8XLy/r68Pr732GgwGg9LFk0SqCeJOpzOtBAn9/f2qVBjkSgPMsixisVha29rtdgQCAZpsTwjJGQUkhGgQwzCKjRFPtUL1uHHj4HQ6FSmL1EpKSmA2m3H06FG1i5K2p59+GjNmzMD8+fNVK8P+/fuT3r906VJEo1FwHIehoSFdByPJkjaYTCbx7/r6epSXl6fc13vvvSdW4IUAWG5aWZOkqqoKp06dQigUUrsohBCdo4CEkAKWKhghJN6bb76JF198ET09PWAYRrcBSTI2mw3nnntuxs9bsGCB+H+Px4N9+/ZJWSxCiIbQkC35UEBCSIFSOhhJlhlIiUnFDocDM2fOxMGDB2U9DtGn6dOnS7YmlMvlwqJFi/D2229Lsr+xCL9dLfSUEEJIruhMRkgBSjcYmTp1Kmw2W1bHEIadxd+GMxgMilSozGYzpkyZopvKW3NzsyKV2kIkfO/nzJmDOXPmoLi4GAaDQZLeHmHY19y5cxVJh1woLaeEaIXQQ6L0rRDo4+qc50pKSnRTUSLZ08rk8EyyaTmdzqwqagzDjAhIxqLE99/hcKCmpkbTK7kLn43f70d/f7/axclLVqsVjY2NKCoqQlFRkeSBA8MwcLvdaGhoyDqYJ4SQQkO1YJWxLEsBSYFwu92qByRCz4jcLS6ZTMpX8rtfWVmp+mcwlvheq3A4jK6uLhVLM1JXVxfC4TAA5TPBScFsNqO0tBTjxo2T/Vh1dXUoLy/X9PdNCmazOa/mEhFC1EG1YEIKhFJzRrKtqCpVwTUYDJqsTA//bDweD1555RWEw2HVu+x5nkc4HMYrr7wCj8ejy5XCWZZFdXU1JkyYoNgxx48fj3HjxunuvcpERUWFbrPwEZIpGrIln/w9SxJCREpOYM8mZTHLsoq1so4fPx61tbWKHCtdo30+0WgUTzzxBAKBgAql+kQgEMATTzyhSEpbuUyfPh11dXWKH7empgbz5s2TZd88z6e9ZkgmlJrbRQghAjrjEJLnKLXvSOXl5WhqalK7GAC0//m0t7fjH//4h9rFyMn8+fPhcrlUO77dbsenPvUpTfbMEUIyQ70j8qCARCMqKiryfqwxUZ4S80WG43le0xVs4GyPjBJZkKSye/dudHd3K37cI0eO4MCBA+K8EYEePmMAMBqNmDZtGiwWS0bBwPDscLn2FgxfdJEQQkgiCkg0wmaz0cRAIjktL+Ik9AzE35TEsiwqKioUPeZw6QaMHR0dig/bOnHiBFpaWtDX1zfiMT203JnNZlRVVaGkpCTnYCT+lsscqZqaGskDYb0Eh4QQMhb9NBESUiCkGtaRSyWFYRhZU5YKFfGxKrWjvQ9SVYTNZjPq6urg8/kQDAZVq2Cne1yfzwe/3w+73S5zic768MMP4fF4FDmWHBwOBxoaGjJ6zljzn4T7hd9Vpt8XhmEwadIkBINBeDweSefjcBxHcz4IUYCWG/n0js5ghGiMVBNKcxmuZTQaMXPmzKzLwXEcotHoqJWudMrGMIy4YJ1wk3oMPsMwmDZtmi6G0+zduxf79+9XpDVcjonSShLWwcn0Oen0UufSSwIAM2fORGlpadbP1yKaG0MIyRX1kBCSZ7Q2STqbluDRxu3H36/njE/Z+vjjj9HT04PLL79ctmNwHIfHH39cU9+hTI0fPx7V1dVpb5/pPBEtfg+j0aik6ZiH9wiNpbS0FFarFZ2dnZIcmxCtoh4S+VAPCSEKGDdunCKt8FoLRrKRbqVKT5PSk4nFYll9Vnr/fLUm10p8Nj0mEyZMwMSJE7M+JiGE5BsKSDTE7XarmpqSyMdoNMo+xluNjFpSEsbvZ9pSLcVwkbq6OsXmZmhZIBDA7t27df09qq+vR1FRUVrbSvGbzGYfZrNZF8MECSFEKRSQaIjNZqPUvyQnUlQkzWYzSkpKJChN+oQx/9ksqCiF4uJi3fz2otEoWlpaZAkawuEwmpubM8qUpjWlpaVpJ2SQaohTNnNWrFarLHNJtPiZEJIvaKV2+VBAQghJ4HA40NjYmPHzhEpZJrd4amcJMhqNig0Dy6XS6Pf7sWvXLtUvUlpMN2s2m9MODKSciJ1NT53b7cbkyZMlDYSFz0NrnwshhKRCAQkhJGfJMmKlc9NSdp66ujqMGzdOkWPpfXidVp1zzjlp945oYd0ni8WChQsXShqMq/Xd0tJvmRC5UA+JfCggIYQk6O/vx759+9LePt10qckYDAYYjUYwDKP7VLOE5BuWZdPuNbTZbBmv+0IIIQIKSAghI6jRIsPzfMZBidRBjNvtxqRJkyTdZzyO4yjwkoHZbMacOXOolf7/yea3JAV6/0m+ox4S+VBAQoiGqD2PQi3C5GJhXkKy23DprPaeKYPBIPvk9kK5uCiJZVk4HA61i5G1CRMmwGw2S7pPLc7xIYSQ0eg7kT8heSSbLFPJ9qGFCi/LsimHcUUikRHPyWQdFbkqWyzLwu12w+PxyLL/XBmNRlRWVlJrdB6pra3FmTNnEA6HJd0vx3EF28hBCNEXOlNpjBSVUlKYpFqTIxOjHc9oNMJms415E8qbbplT9ZhIxWg0YtKkSZqt8Nvtdlx44YWylI9lWclb6uXGMIzuF8mUE/WSECIdvQzZOnXqFL785S+jrKwMdrsd8+bNw549exJex9q1a1FbWwubzYYVK1bg4MGDUr5VGaOar8Y4nU7U1dWpXQyiU1Ktq5CuXCa0O51OuFwuWCwWiUulTXIHUlJwuVy45pprdNUoUl5ejtmzZ6tdDM3Sw/eOECKd/v5+LF26FCaTCS+88AIOHTqE3/72tyguLha32bhxI+655x5s2rQJ7777Lqqrq7Fy5Up4vV7Vyk3NSoQQyUUiEXFIlsvlGnNbs9kMg8GAoaEhJYqmCqkqhZMmTcKCBQskKFHhyiWIHk0sFtPEUMnRCN+9bANNYTglIYVOjUnmmR7v17/+Nerr6/HII4+I940fPz5hf/feey9uv/12XH311QCALVu2oKqqClu3bsUNN9wgSbkzpZ9mMEJ0iGVZ1NTUKNbirJX1LeK7mv1+v3hLNkY+Go0iGAyqUMqxTZ48WZLhS7FYTLLKHMuyMJlMkuxrNAzD4MILL4Tdbk9reylfX6bq6uok71G2Wq2wWq2aWKdEK/TUY0ZIvvJ4PAm3UCiUdLu///3vWLBgAT73uc+hsrIS8+fPx4MPPig+3tzcjM7OTqxatUq8z2KxYPny5di1a5fsr2M0dJYhREYsy6a9UJtUtBCQxItGo+ItEokgHA6PuGWbolQYopbsluscC6fTmXOlVCsBYiYYhkF1dTVmzJiB0tLSlNur+frsdrtkvy+GYcTeOoPBAJPJlNbnr5eeAyWybjEMg+LiYs3OvyJEz+rr61FUVCTeNmzYkHS7EydO4P7770dTUxNeeuklfOc738EPf/hD/PGPfwQAdHZ2AgCqqqoSnldVVSU+pgYaskWIDqSTLSedoSjpVkh4nsfQ0JDkqVRjsVjWwUeySs5Y70n8a1Wr0qyXymoy06ZNw+DgIPr6+tQuiuSG/56E305875PwWxqtIi/cp5fPWInfAMuyKC0thcfj0V0gTkg61Byy1d7eDrfbLd4/2vxLjuOwYMECrF+/HgAwf/58HDx4EPfffz++8pWviNsNv6byPK9qYwL1kBCiAWOlyU13/oHJZILNZoPT6Rz1lu7JJhqN4tChQ5qpbAkVxuG3sQjvaSaZvKSklfeOJEoWYBiNxqQXd4PBkPR+JbK9aQ31ehCiLrfbnXAbLSCpqanBjBkzEu6bPn062traAADV1dUAMKI3pKura0SviZIoICFE41iWTZnW1GazpZX61OVy6XI8eC5Dp9JZE0VqhVZZzVeRSASBQED8OxaLIRqNJv1shd9p/E1rlXie5xGNRsfcZrTXJwT3hBQyPaT9Xbp0KY4cOZJw39GjR9HY2Ajg7EKs1dXV2L59u/h4OBzGzp07sWTJktzfpCzpr2ZSAAwGAxoaGtQuBtEJu92eUUXB6XRKNjGa5/msh2ClQ8o1JrKpIE6ePDmteRRKaW5uxrZt2xQ51rZt29Dc3KzIsbIh92T6cDiMSCQCjuPEOVCjVQyUTredq9GCDkKI/t1888146623sH79enz88cfYunUrNm/ejO9973sAzl5Xb7rpJqxfvx7PPPMMPvzwQ3zta1+D3W7Htddeq1q5aQ6JBunpwkaUE4vFEhbOZBgm68DCYrGAYZicV4YWxpzmmlZUKUJAkm6Lk9DSnQk5J7JPmzYNK1euRHV1NQ4cOCDLMTiOw5tvvon+/v6UrelqyvU9FgJpq9Wa9DMWekPGInzftf69z0Q+vRZCCtHChQvxzDPP4LbbbsOvfvUrTJgwAffeey+uu+46cZtbb70VgUAAN954I/r7+7Fo0SJs27YtZZp+OVFAQohOxE84E3oOsu09EFbk5nleXC8kl3KlqhymU8lJ1WI7WgreSCSSduVUWE9B7kmJcu2/oqICc+fOBXB28SvhOIFAIOnkc4vFgvLy8oyOwXEc2tvbNd2CLkXZhPcuPsiPZzAYxN6RZPTWK5KufHxNhEhFD+uQAMDll1+Oyy+/fNTHGYbB2rVrsXbt2hxKJi0KSAiRSS49GKn2azQac943y7KwWq1jDn0JBAKw2WxpV1LGqiimyoiVqpIp9OoMFz9JOd3UpgzDZNRLYjKZ0grclKzET5s2Tfx/b29v0pz0xcXFmD59ekb7lXMInlSE91lIGy3FejHDmc1mMAyTNCDJ12CEEELUQgEJITKx2+1pZazIpHLDcRwsFotkgQ7DMHA6nfB6vUkr6IcOHcL06dPhdDpzOo6ck7yHr0Ph8XjG3D7TXpKqqirYbDYcP3485bZqtJ4BQFlZGRYvXizJvvQQkAiam5sxNDSEqVOnKnZMCkZSo8nvJF/ppYdEj+isSgjRRfYtr9er6TkN+aCzsxPf+c53NDtca6whVNny+/0IBoMJ93m9XgwODsLv9yfcXyjByGjrBRkMhrRef319veILwhJC9I16SDSKZVnU19ejo6ODKmFEZLfbJcs6JbXh5ZKiVyQ+i1e6qXtdLhd8Pp/irUqxWEz2Y7799tvo6+vDzTffLPm+3333XTzxxBOaDUZG09PTA6/XC4ZhcM455yRtnR++Bg/P8/D5fOLfQkYtgRTvQSbfhz179owIitSUaw9HIQRthBBpabNmQwCcXeiOur5JPDnX07BarQiHwyMC4I6ODlRWVqKoqGjU56ZaRT2Xbm7heRzHifMkxgrK5PjNWK1W1NXV4dSpU6Nuo0QA5Pf7cezYMTzyyCMAgKuvvnrMz2U0PT09ePbZZxPu6+joQHd3tyTlVBLHceJaIR9//DEAiN9XhmFgtVqT/m6EFvxQKJTWEL5Me0cy+T4Eg0FNBYIUUBCSHA3Zkg8FJITohNwBqtFoTNobNzAwAKfTmVXFV6jYcBwnTiTP9uTKcRwikUhaC0Wm2k+mzGYzSktLRw1IlKxM+v1+7Ny5EwAwbtw4FBcXZ7yPM2fOiPvQi3Te4/iVhyORCAwGA8aPH590W2EifKo1OYTfXCaVdC0FF3LIJCkEIYSkgwISQnRCqTHZclQ24gOTZNI9nlTDwKR+fWpVQP/85z+rcly1pPs+d3Z2oqurC06nc9SAJB3ZzBmRM4GDFiiVOpsQLaIeEvlQQEKIirQ2SVZYJG74ZF6pJHutmVZuQqEQIpFIVj0DcszzyOfKp55VVlZiwYIFWT8/34MRvSxmSggpDBSQaFxdXR16e3vh9XrVLgpRCcMwimasMRqNcLlcGX3nhMnn2cxxia/4pZvAgeM49Pf3p9xu+MrsuQYjlGBCHZkGkjNmzFC8Z4QQQkj26IyrcSzL0sR2HSouLkZpaanaxcja8O9cV1cXWltbx3yOEJTk0kKcyfdd6DrP5JYLg8GAGTNmUEVV4+bPn4+6ujrFPycaxpSorKwMbrdb7WIQIqlsrjtS3AoBXVkJkYEUK6kDZyvocqz2ng6r1Sr+PxwOi5mMxpLriVMISKRqoZbyZM4wDBwOhyT7IunLpKI/depUVFRUwGKxyFyqTwjDtAqp4gCkHuplNps1m6KcEKI9dLYgRCVCxXssuWaUyoXZbEY0GhWHy8RiMXg8HtlbPZNl5iLax3GcLL0S6fS4sSyLsrIyTJ48OaMe5dEWABSOm+r15DpnhOM4DA4O6u47nipJBSH5iia1y4d6SHSAYRgatpWH5FxTRCp2u1387glrYKQi1cRerQ5XNBqNmiyXmuSqmKYbjDidTixatCjjz2VoaCjpMdI5rhTf83A4jA8//LBgKhyEEDIaCkh0oKysDJWVlWoXg5C0cRyHaDSal5PA586dC6fTqXYx8l66Ff66ujp8+tOfluX4wnc42Y16BwghRDoUkBCiUWazWVy8TU1Op1MsB8dxeP/99zMKNIRhMblOeCeFQ+70uZFIBNu2bUMoFJLtGIVAq72YhMiFJrXLhwISQiRWXl4Ou92udjFkk2mvx/AT61jj9jPBMAzcbrd4U/I9r6+vR3l5uWLH0wOe53UVcIbDYRw5cgQej0ftougWZZwjhEiFJrUTIjGbzZZ32WWMRiN4nkckEslpP/EtPfGV19EWTBzObDYnzL1R6312OBya6L3SEp7nJWktlzt9bjAYRHt7OwBgYGAAdXV1sh1rLH6/H11dXaocWykWiwVOpxM+n0/tohAiCZrULp/8qjXlMZZlYTabEQ6H1S4KkUCqipvWFmYzGo1gWVYcduX3++FwOHKamB+/UvRoLevx9xuNRnEl+VwMf++zPdlbLBZYrVYEg8GcypNPhF6SbL+7cqfPDQaD6OrqwpEjR2TZfyZ8Pp8YGOUrm80GlmUpICGEpKSdGg8Zk81mQ3V1tdrFIBJgGCZlRd5qtWoqIAE+yWYEAEeOHIHX68264ihkjhPS+o52i9/O6XRKEowI779wyzaLXXl5OSZMmJBTefJNrsFEps/PNHBva2vD/v37sykaIYQAUH4eSaGgHhJCSFaOHTuG6upq1NfXZ/S8dAKyZHw+H+x2e05BSbLjCvfJPZGajC2bjGxz585VbcgVOctgMNBvhxCSMwpICJFQfX29LtYXyYXL5YLP51Ol5SYQCMBkMiWsIp+OdIIgWuxNGjzPIxqNph04KlGZ3blzJ6LRaM5zoKRy/PhxdHd3q10MQgjRDApICJGQwWDIeaiV1idLMwwDm82GYDCIgYEBMAyDcePGKXJsuYOgTIMSq9WKCRMmoLm5Wc5i6ZKQSY1hmFF/E7lOYG9paUE0GkVjY2PSxyORCA4cOKBaAD0aLQVHhJD00aR2+WhrkDoZU/wYfqJP6cxX0EOGLqPRKCZZ6O3tRW9vr9pFGlWmc0QyCSiNRiNKS0uzKVbeix8DPdZq6LlcbPv7+zEwMJD0sUAggJMnT+L06dOauqD39PQUVCIEum4RQtKh/ZoPEbEsi8rKSgwNDWnqAkvSN1Zrsd4IPTnBYBAtLS0oKytT5LjZfPczfc+FyfYkd8L7ODwokaqlMRqNIhAIwGazATgbiHAch56eHhw8eDDn/UutublZkoBktMQX4XA4q/k4cjGZTCgvL6dMWyQvUA+JfCggIUQihb5icbrrUGQ6x2C4SCSCWCwma6urwWBALBYrmAuB3OS8iHd0dGBwcBAXXHABAOCdd96B1+tN67lK/2alfA/mzp2bNPPiBx98gLa2NsmPRwghcqKAhBAJmEwm1NXV5U3vR7rMZjOMRiN8Ph/27NmDOXPmpD0HJhqNZr3eCsdx6O/vT7md1tZzIfLw+/144YUXAGSWlGDhwoWwWCxyFSsBx3HYvXu37BP4582bh7lz5yIQCGD79u2yHktACSEIIbmiKzUhEklV8U3ncWHYiZ4IY8R5nseRI0cwODiY9nNHm18wFo7jxN6LQs3XTkbKJluXnntIxiIknrjgggsoICdEQkqvQVJI1zY6U+lQWVmZLiY+k5FSVQ70OuxLCKZCoRA6OzvTnuQ+1qTnZDJZyTuXiliu2Z+ItrEsi0mTJil2Hg2Hwzhx4oRk36kZM2agqKhozG0YhoHb7ZbkeIQQIjeq1eqQ2+2G1+vV1MTFQmY0GnXZsyE1k8mEWCwmTl41mUxpVYiSTXoeHkzEZ2TKdCXvbNDQk/xXW1uryHGCwSD6+vrQ0dEh2T7r6+vTXounuroa3d3do6YZ5jhOkV4Uu92OQCBAgT7RNZrULh/qISEkR3a7HeXl5WoXQxOsViuMRiO8Xm9Ga3MIvSTxt3jCfUqcmCkYIVLq6+vD8ePHVTv+woUL4XA4kj6m1HedZVlUV1fT8DFCyKioh4QQIimr1QqDwZBT8CDF6t3C8zOpBCmxajhRH1WMKeEDIURb6GxEiAJSXfxNJlPaQzD0wGQywWQyYc+ePbrpbqZ5I4XB7XbjU5/6lCLH+uijjzLqKZTLsmXLMHHixIT7KPgmJHM0qV0+FJDoVEVFBa1+qyOF2BLJMAzsdjuOHDmCUCikShnSnTCfyWR5on8Gg0H2Yxw8eBD9/f2SVvpZlsWyZcsyTlU8WoNINlnuUh2HEEKyQUO2dEpY/4Goy+FwwG63q10MzWJZFuFwGJ2dneLk/9LSUsWOHz9hfrTKEgUjREocx6G9vR0DAwOS90CwLCvp7ydZQon4Y2WCZVnqcSF5jya1y4dqtITkwOl0UkCSgtlsxsDAACKRCBwOBywWy6iTbOUwVqVrrPtJ/jGbzbL3LPM8L66UrgfJKlhy9XRYrVYEAgH6zRFCRqD+VUKI7KxWK0wmE4aGhvDxxx8jFoshFospdvxkWbxoDH1hYVkWdXV1mDRpkmzH4Hle0e91JgwGg+pDqqqqqihFOiEkKeohIURmNLTuLKvVKk7c37t3LwBg+vTpNBeKKGL27NmyLxTo9Xqxb98+WY+RrWnTpqGsrAy7du1Kua0wjFGJuTaE6AkN2ZIP1ZR0rLi4GBaLBWfOnFG7KAWprq4OJpMp5/0U4nwgYXHPY8eOoaamBtXV1WoXieQhk8mEc845R/y/nE6ePIn29nZZj0EIIfmqsGpBeYbyyKtLqiEQhfoZCkM3BgYG4PV6wbKsrMNpiPZNmDAh6zlZPM/j8OHDmDhxotgTxzAMzGazlEVMqqWlBT09PYhGo7IfixCiHuohkQ8FJIQQVQi9QtFoFMFgELFYDF1dXQDO9v4pUZEk6hNW8QaA8vLynNbjqampQXl5uSLfHY7j0NnZCQDo7u5GMBiU/Zi5stlsaGxsRGtrqyz7FxpXaG4WISRTFJAQIiOGYdQuguYZjUawLAu/34/29naxMuNyucRttDoRluM4BAIBtYuha0ajUbKeMSV62MLhMCKRCKLRKI4fPy778aTkdDoxc+bMtAOSsdJljyZV+l+j0QiDwaDZyf+EEHVQQEKITBiGoUmhaWJZVpzc7vP5EipMDMPgnHPOAcMwmgvw/H4/Dh8+rHYxiAKESnZ7eztOnz6tcmlyk86aIXINEykrK4PJZEJPT48s+ydETjRkSz4UkBCSIYZh0NjYWLBzP+TmdDrh9/vF8fg8z4tZuSZMmICysjI1i0cKEM/z2L17d14soGkymbBmzRo8//zziEQiaheHEEIAUECiexaLBXV1dTh16pTaRSkoFIzIy263i2s6+P1+sRLY3t6OkydPwmq1YurUqSqXkkghEongvffewznnnKOp39Xx48fR398P4JN1bHLBsiy++93vjtrL9/jjj6O7u3vMfUSjUWzfvh3Lly9XZJ4Mz/OIRqMFlwWQkNFQD4l86CyjcyzLyp7OkhA1MAwDo9Eozh8JBoNii240GkVzczOAs+mX1ZoAb7fbMXPmzBH3h0IhfPzxxyqUSH94nkcgEMCxY8fQ0NCg2nyhUCiElpYW8W+Px5PzRPVFixYlpLQuLS0dNSC58MILEQwG0dnZibfffnvUffr9fpo0TgjJOxSQEJIBlmXhcDhSbqfF+Q56JQTcsVgM0WhUXGFdGINutVphNpthMplkX/guXiAQAMdxqKurG/FYMBiEz+cTszCR1Lq6umC1WlFRUZF16t908Tw/ojciGAyKWd6yZbfbEybWz5kzBzU1NWk9V+jxq6qqgsfjoblJhJCCQgEJIRkwmUyoqKhIuR3DMCOGn1itVvE+oVWYpM9qtSIYDIpzS4RW4pMnTwI4u9hifMpYk8kkW1AYDocxMDCAcDicNCCxWq2YOXMmzpw5UzDd7VJoa2sDx3EJlXiLxZLyc+R5HqFQKO3jxGIxHDlyJOtyDud0OmE0GlFbW4urrroqp31VV1fjsssukz0gsdlsiMViafW2ZJNtaywsy1KmLaJLNGRLPhSQEKKQRYsWoaSkBMDZCu3zzz+vcon0Rwg4eJ6H1+tNeMzj8WDfvn3i31OnTpWtx6S5uRnl5eWYPn26LPsvZCdPnhSDTODs7ybVkLxgMIj33ntP7qKNIARK11xzDRoaGiTft5wVkQsuuAC7du1KOW8F+CT4lyoocTqdsFgstLI9IUREAQkhCrjsssuSzvURhiBpaTKvHjAMIwYbPp8vaSvv0aNHR7Ssz549O6v5JoODgwlzQubNmycGl0Re7777rtpFSMput+Omm24CAMnTezscDtx22234zW9+g3A4LOm+syUMlYw32mR3o9GIWCxWMC27pHBQD4l8KCAhJE0ulwvFxcUpt4sPLsxmM5YsWTJi+JDJZMKKFSuwc+dOOYpaUBwOh5gNKH4ScrILx/AghWVZLFy4MOWQoFAoBIPBgHPOOQfA2cpoqiCSYRh86lOfwr59+3SxirdWaW0C9+LFizFr1iywLCtr9ilhwVCtqaysxOzZswEAr7/+uuY+H0KIPlFAQkiaDAZD2hnNhIqEwWBIGsQwDIPi4mLYbLaCaf2Qi5BAIP6zGS0AEObtFBcXo7KyEsDZzyhVQOJ2uzFx4sSMhoAJvTgTJ07EyZMn4fF40n4u0aZFixZh1qxZaU9UzydTp06FxWKBy+Wi3kFSsKiHRD4UkBCikPhhHcJkTrPZrJkhGXrHMIw4HEvIyDXaidzlcqGxsTHtfdtstqznCNTV1YllGT7vhWifyWQSvyuLFi1Kq5c0H40fPx4ulyvhvqqqKnR3d4uJJgghJFsUkBAik+FrxNhsNhgMBkSjUQwNDalYsvxns9kQCAQSVnuPF4vFEA6HFVu/pLGxERaLBQcOHFDkeEQaRqMRFRUVuPbaa7N6PsuyCb1v+ZZVatGiRdixYwcGBgayej7LsjTkixACgAISQmRTU1ODhQsXql2MgiUssMdxHHw+X8Jjp0+fhsfjweLFi9UoGtGJGTNm4Morr8z6+TU1NeK6RTzP49ixYxKVTP9MJhMaGhoSFqMkROtoyJZ8KCAhJA1VVVVprSDNsqwmJ6IWMpZlxbkfXq9XPLkPDQ1h586d+PSnP02LWGrchAkTRgwXSubAgQOSXbxXr16NOXPmZPQcq9WasC5N/LmAYZiERROBs1n2WltbcytoDhYtWoRjx46NuSYLwzC4/PLLR+1NXLp0KQ4fPowTJ07IVUxCSAGggISQNKQbaFAwom0OhwN+vx8cx4HneYTDYezZswcMw2D8+PEoKyuT7dglJSWYPXs2DdtKE8uyYgXeZrOlzGjF8zwmT56ccF80GkVzc3PGx77qqqswfvz4tJNYVFRUwGKxwGAwjJkCePhjmQ5XYlkWixcvhsViyeh5Y5UnVcpinufx1ltvYeHChbDb7SMeN5lMkqc9JkSrqIdEPhSQ6Fw0GqXsPToSjUYRiUTULkbBYlkWVqtVnFsSDofR398P4JO1E4TsW1KzWCwoKiqSZd/5Kp1eEQHDMCO2j8ViqK6uBgD09vam/O0JFf7Jkyen1SMKnA00nU5n2sGLIBwOY3BwMOljkUgEb7/99ojJ4izLory8PKPjSKG7u1uc/xI/Ny4UCileFkJIftJMc+6GDRvAMIy40BRwNipcu3YtamtrYbPZsGLFChw8eFC9QmpQJBLJekIhUV40GqWsWiozGo2wWq2wWCwJPVpdXV1ob2+nAD+PGAwG1NbWora2FkVFRSmDBpZlceGFF6YMRoTA1mq1ory8PONgJBKJwOv1isHwcOFwGK+88ooms1cZDAbxtdNQR0KIVDQRkLz77rvYvHnziPG6GzduxD333INNmzbh3XffRXV1NVauXEmpM4misr3o8jw/ZlYd6ilRF8MwcDqdCZ9vX18f9u3bJ0s2JJ7n8z6jEMMw4vBG4aaVSmtDQwMqKipGHVY5fC2b0TAMA4fDgYaGBjQ0NGT8+nieR29vL3p7e5M+znGcLs4NUn2XaZgr0RNhyJbSt0Kg+pnA5/Phuuuuw4MPPpiw2BLP87j33ntx++234+qrr8asWbOwZcsW+P1+bN26VcUSk0LT0NCQ9vCNeKdPn8Y///nPpI+Fw2H87W9/o94SDXC5XAmVomAwiFdffVXy4OHMmTPYtWuXpPvUmoqKCsydOzfhpqV1O6qqqkZMLBdUVFTg3//931MGGBUVFTktjNjS0jJmL9zx48fxhz/8Iev9K4HjuIQEEdliWTajuTqEkPylekDyve99D6tXr8bFF1+ccH9zczM6OzuxatUq8T6LxYLly5ePeVEPhULweDwJN0JykW4LaKqWvqGhIQpANMrpdMLlcomBJ8/zeP3117Fz506cPHky5/0fPXoUH330Uc770bLGxkbU1NSAYZiEW319PWbNmoWmpia1iwiGYWC32zFjxowRj8nZUh8Oh3H8+HEcP34862FYxcXFuOiiiyQuWeai0eiINNrJCL1lhOQT6iGRj6qT2h977DHs3bsX77777ojHOjs7AZxt0YpXVVU1ZprEDRs2YN26ddIWlJAU0rn4FspJRa8YhoHRaBQXVRSCx/b29oThNbNmzco4q1AkEtHFMJxsNTY2wu12J31fhOxYBoMBEyZMAACcOnVKteDcYDCAZVlMmDABra2tGfWEVVRUiOuKpGtoaAj9/f1pDQPct28f9u7dm/QxYd6K2oafxw4ePIiOjo4R29Gih4SQTKgWkLS3t+NHP/oRtm3bNuZJdnjrNM/zY7ZY33bbbbjlllvEvz0eD+rr63MvMCFZiEajaGlpQWNjo/i9DQQCaG9vV7lkJBlhHoGwkjtwdlhpfIvwyZMnxYq32WyWLSuXnhQVFaVMy2swGMRhucFgUAzQOI5DX19fwrYmkylhCK/UGIZBSUkJhoaGMDAwkHZwZLfbMxpeNDQ0BI/HA7/fn3Lbw4cPY//+/bo7N3R3d6fVY0IIIWNRLSDZs2cPurq6cO6554r3xWIxvPbaa9i0aZO4UFNnZ2fCeN2urq4RvSbxLBaLZDnaCcl1bHMkEsEHH3yA4uJisRLb29uL/fv3S1E8IhMhNXCyFt6jR4+K/3e5XGKLud1u18wEbiVl02off06PRCIjKux2ux3jxo3LuWypjBs3DhzHpZWp0Gw2Z/T5RiIR9Pf3pxWM9PT04NVXX0VPT0/a+1fa0NAQrFYrzfcgBY9GO8hDtYDkoosuGrFA2Ne//nVMmzYNP/nJTzBx4kRUV1dj+/btmD9/PoCz43B37tyJX//612oUmRQYlmUl613bsWPHiH3TcAZtczqd8Pl8Y35OXq9XnNO2bNkyWCyWggpKWJZNOh8jEyaTKed95KKhoSFhbZrRxPdypqOtrS2tYVrRaBSbN28e8/ha+E698cYbOPfcc8Vhd4B0mbYIIUS1gMTlcmHWrFkJ9zkcDpSVlYn333TTTVi/fj2amprQ1NSE9evXw26349prr1WjyIRIJt2V34m6nE4ngsFgWkN63njjDcyZM4eGcJG0DQwMYNOmTSkr9k1NTZg+fbpCpUou2Tnr2WefRSwWo3MZKRi0Urt8NL1S+6233opAIIAbb7wR/f39WLRoEbZt25bR6r2EEJILq9UKg8GAQCAw5nY8z+Pw4cPw+XyYOHGiQqUjUigtLc14svpoYrEY2tvbxwwyXnnlFXz00UeIxWJp9TKo3UMiBCOHDx/Gxx9/DOCTipkUwUh1dTX6+vowNDSU874IIfqkqYBk+LAWhmGwdu1arF27VpXyEJIOIb0pyV/C6u7BYHDM7cLhMDo6OhAKhRLuT2eOgt5YrdYx5/PpidFohNlsHnObrq6utPc3Vo/aq6++ikOHDo2YyK9lQtARDAZT/gayYTKZqJeFkAKnqYCEEK0wGAyw2+1pb08X0/zGMAzMZjNisVjK9L1+vz+ticx6ZzKZUFZWpnYxFDM4OCjJfj788EP09/dLsq9c9fb20lpdhGSAhmzJh2pRhCRhNptRUVGhdjGIxthsNgo+cTYAT5XmV294npel9T+ex+NJa6K7Uo4cOSLJwp+EEJIrurISQkgGnE5n3lXGM1VdXZ2QbSkf+Hw+vPLKK7K1RkajUfz+97+nHglCdIxWapdPwVxVI5FIXq5PYrPZ0NDQgLa2NrWLQkjBsNvtaWffyjdNTU1wOp1qF0NXurq68NBDD2VVsVi8eDH11hJC8h71kOQBGkJCiPKsVitsNpvaxVBUU1NTXi8AyXEc3nzzTUmHbh06dAhPP/10yrlHozEYDKq+30pdX4qLi1FaWqrIsQgh2lMwPSSEECI1k8mEWCyW9z0lLMuiuroaTqczb4MRwcDAAI4dO4bGxka43e6c9rVv3z4cOHAgowxdWsIwjGIBiclkolXgiebRpHb5FExAEgwGYTAYCn7sNyFEWlarFRzHpVzpW6+MRiNcLheqq6vVLopiWltbYTKZwDBMxutecRyH1tZWAMCePXtymjReWlpKlXRCSEEomNp5b28vGIbJucWL5D8lWwVJfrDb7fD5fAktWfnSquV0OvNuAns6Pv74Y/h8PsyePTvl/MNQKCQucBgOh/GnP/1JkjIsXryYGtEI0RDqIZEPnekIGaakpATFxcVqF4PoTPxEb57n4fV6VSwNkcLBgwexe/du/OAHPxhzu8cee0zsFSGEEJI5CkjyAMuyaGxsRHt7u9hKRwhRj9Abq/cUr7W1tQWf4WlgYAC/+c1vxtxG7vVL5LB9+/aU5eZ5HtFolHppCPl/qIdEPnSWyRMGg0HtIhBChrHb7QgEArq9oBiNxoI+t7hcLjQ0NORl70ckEqEGLEKIZtBAeUIIkYnRaITFYqE5STplNptpzRVCCFFAQfWQRKNRRCIRylpCCFGM2WwGcHayM7VI60s0GkUgEFD8uAzDoKioKO9TLBOiNzRkSz4F1Ww3MDCAvr4+tYtBCCkwZrMZVqtV7WKQDMRiMQwMDODEiROKH9tkMmH58uUFOVyOgjBCClNB9ZAQQggh6Whvb6cGLIU5HA7U19ejra1N7aIQkhT1kMinoHpI8t24ceOoFZYQjTIajbqbj3D69GmqHOaZSCSC7du35+1CnoQUurVr14JhmIRb/MK2PM9j7dq1qK2thc1mw4oVK3Dw4EEVS3xWwQUkoVAIPT09ahdDFkajkbq7c1RSUgK73a52MUie0tvvMxqNwuPxoKWlRbyFQiG1iyW7kydPwufzqV0M2fj9/oJpdSWkEM2cORMdHR3i7cCBA+JjGzduxD333INNmzbh3XffRXV1NVauXKn62lkFN2QrGo3C7/erXQyiUXa7XZyErATK8U+0LhwOJwxdMpvNI1Yud7vdkmQSY1lW1aCN53n09fWhr6+PehAIISPoZciW0WhM6BWJ39e9996L22+/HVdffTUAYMuWLaiqqsLWrVtxww035FzebFFNiBAVhcNhsCxLaWHzzGgXkFgspnBJpNfZ2QngbG+PkLHQarVKMgE7PgOi0sFJLBZDOBzOyzVHBBzHYWhoSO1iEEKyMHyhXYvFMqJxSHDs2DHU1tbCYrFg0aJFWL9+PSZOnIjm5mZ0dnZi1apVCftZvnw5du3aRQEJkY7ehoQQkm8ikYgqqWKVZrPZMH78eEn3GYlEEv5OdrGV4xzH8zyCwSCOHDki+b61pLe3F7t27VK7GITolpo9JPX19Qn333HHHVi7du2I7RctWoQ//vGPmDJlCs6cOYM777wTS5YswcGDB8UGpaqqqoTnVFVVqd4YQwFJnqmsrITH46HsMAqh3g1SaBobGxWbZzV8vgrLsrIMqezu7sapU6ck32++iEajdK4jRGXt7e1wu93i36P1jlx66aXi/2fPno3Fixdj0qRJ2LJlC8477zwAIxt2eJ5XvUG7IM8u0WgUJ0+ezMtFytQeg00IyV8TJkyA1WoVM7cojeM4hEIh8ZbrELijR4/i8OHDOHPmDE3yJoRomtvtTriNFpAM53A4MHv2bBw7dkycVyL0lAi6urpG9JoorSADEuDs2H1CCCGpsSyL6upqyeaK5EIYMsHzPGKxGCKRSNa3YDCIQCAwYqhYPurs7ERzc7PaxSBE1+LPP0rechEKhXD48GHU1NRgwoQJqK6uxvbt28XHw+Ewdu7ciSVLluT69uSEhmzlIaPRCKvVimAwqHZRCCF5gGVZlJaWql2MEXLt5bbb7eB5HqFQKO8bqXp7e9HR0aF2MQghMvvxj3+MNWvWoKGhAV1dXbjzzjvh8Xjw1a9+FQzD4KabbsL69evR1NSEpqYmrF+/Hna7Hddee62q5S7ogETo7s+3cbEOhwNGo5HGRBNCcsYwTN6mph43bhwAoL+/H93d3QBA6X4JIaPSQ9rfkydP4ktf+hJ6enpQUVGB8847D2+99RYaGxsBALfeeisCgQBuvPFG9Pf3Y9GiRdi2bRtcLpccxU9bfl5l0tTe3o66urq0x+ERQkihcblcYsU9X5WUlKCkpAQ8z+Pw4cNqF4cQQrL22GOPjfk4wzBYu3Zt0gxdairogCSfWSwWNDY2qp7GjYwtGo2C47i866UjhcVgMGQ9yV2YC6IFDMNg6tSpAICOjo4Ref/16s0339RN5kWj0Yjx48ejtbWVEg0QUkCoFpTHKNsWIcozmUyKpcWVW2lpKSoqKsbcJpdgBDh7njIYDElvahCOXVlZicbGRlV6hyKRCF5//XXJAjWh4UMvqIGGaJUeJ7XrRcH/6j0eD03+JrLgOE5XlQAiHbUzUUllrJWABVI0fAhphIffhLUv1Ehnbjab4XA44HA4UFFRoejxeZ5HX19fwVRECCGk4Idseb1emEwmWK1WtYsiC6vVilAoRBc2lYz1vlOwQrTMarWmnMwudyU9vqU82e9FifOawWBARUUFhoaGEAwGdfe77e/vp4n6hEhED5Pa9arge0jyGcuyqK2tzZvWWr1hWXbU9556T4jW1dXVjZl1RRhqpRTh9xR/U7LXYvz48bBarYquWJ5rIBGNRvHGG2/A5/NJVCJCCJEHBSSEqMzv99OwQUKyYDAYYDQak97kCJbGjx+PadOmoa6uTvJ9J/PSSy+ht7c3q+cGAgE899xz1PBBCNEFCkhwtkv7zJkzahdDNnV1dXA4HGoXg5CCwTCM6jndC93wyfJS9qY4nU5MnDhRsv3pAfXqEkKT2uVEAQnOfsHy+USr9NAGMjaO4wrmBFPI8vk3J0w417rhE+STZfLK5nNiGAYWiwX19fWyvw+HDh3KeJHbvr4+fPDBB/IUiBBCZFDwk9oLhc1mQywWQyAQULsoBSfZOiMUkBAtKykpGXPIk1DJ15PRyiu8lkxbIoVesOLiYng8Htkmjvf19aVMvRyvp6cH7e3t6OrqkqU8hBQymtQuH+03cSmE4ziEw2G1iyEbl8sFp9OpdjEKDsMwcDqdcLvdYFk2r3viSH5gGAbV1dUpM2zli+GphTMNtqqrq+F0OmV9v4LBIPx+f8rthoaGcOLECbS1tclWFkIIkQMFJP9PKBTC6dOn1S4GyTM2mw2rVq3ChRdeiOLiYgCU7pcQLcplzkltbS1KSkpk6zVqbW3Fnj17ko4pj79v165d6OjokKUMhBAip8JoAiNEBbW1tVi4cKH497Jly3DgwAEcPXpUxVIRQtJhMBgymshdUVEBu92O1tZWWcrT19eHZ599Vvz7U5/6FILBIPbv3y/ep8bQjlgspps5RYTkioZsyYfOIHE4jkNbW1vetmA7HA7U1NSoXYyCMXzohx7H3RNSyOInwqfDarXKmn0rvjdk3759OHz4sKKZeIYHaLFYbNTjxmKxvL2WEkKkRwHJMNFoNG+jUZZlC2ZcuNoaGhowfvz4EfePGzcOU6ZMUb5ARBU2m013QSjP8zh16hSt7v3/xGfpSsVgMMBisaC2tlb2HoNgMKjKvEeO4xCLxcYMRgjJZ5TyVx4UkBQYlmVpTRIFlJWVJc2MU1JSgqqqqhH38zxPFcA8ZDKZdBeQAIDH40EsFlO7GJqS7grtDMOguLg4r4cwMQyDcePGob6+HvX19XC73WoXiRCic9RcnkQ0Gs3bMbEGgwEVFRUYGhpSuygkTiQSQSwWo0xohGiYcE1IZyiSyWTKy14ElmVRXFyMRYsWifcdPXoUH3/8cV5nqiQEoDkkcsq/GrcETp06hVAopHYxZKXHVltCCFGbMK8klQkTJuRlA0NdXR0uvPDChPumTJmCBQsWqFQiQkg+oICkALEsi8bGRphMJrWLomujDeGgyZwk3wnzCAqVkCI4lXHjxqGyslKBEilj/vz5CZkDCSFEKhSQjKK7uxuDg4NqF0M2wiJgRHrJsmk5nU4xeCktLcWnP/1pNYpGVOBwOCj4z0PpBCX5NvQ33Xk0UqitraXfDdEcpSe0F9LE9vw5U0osGo3mfQtgUVERLBaL2sXIO8mCvfiF1kwmk7hIIsl/lO45f6UTcNjtdpSWlipUImUwDAOr1SrrMSwWS14Fc4SQsdGvvYC5XC4KSAghWSuUlruxpOpttlqtedEAUV5eDpvNBuBsQGKxWEa8bmGoKg1ZJfmKekjkQwHJGDJZpZcQQsait16SVGsy8Tyf973IUsmHoVvnnnsuqqurR32c53nxmslxXMFUoggh0tD3GVJmHo8HHR0daheDaJDRaJS8gsFxHLxer6T7JNpgtVrF1mW9aG1txcDAgNrF0AWDwTDm+cBisWDq1KkKlkg91IhHCMkGrUNS4EpLS2G1WtHV1aV2UQghOhSNRhPmSBFCSL6idUjkQz0kBY6ybSnHZrNR1pgCZjQaYbfb1S4GkUmqYVkMw6CxsRFGI7UDEkLIcHRmTCEajWJgYCAvJiUSaWQ7VCvVsA6S/9JZu0JLfD4fDAYD3G73mNsJLXiF3LiRzmt3OBx0DiBEx6iHRD50ZkwhFouhv79f7WLIymAwwGw2q10MXUh3curQ0FDCfBAhjTSNry5sak1uFlIPj3VLxuv1wuPxpNw/fa8LR7qLQhJCSCYoIElTPl9wrVYrampq1C5GXjl69Cg++ugj8e+hoSH4fD5EIpExn1coLSGFzOl0Kt6TYDAYUt6o5V45eu5JoqGHhBA50BUoDTzPo6WlBdFoVO2ikALg8XgoMMlzLpdLkQCAYRjF5ixQD2B67/fkyZPhcrkUKhEhREq0Dol8aA4JIWmSqgLZ3d2Nt99+W5J9ETIaNXo8hPUnaEjP2PTeG8VxHIaGhsSK0kcffYSjR4+qXCpCiJ7p+6xIJMMwDCorK3U9lEAJmVQkent7sXfv3hH3cxyHYDAoZbGIDlmtVtl6L4TvaaYV32Tb+/3+jNZjogUTC0N8b1gkEkE4HFaxNIQog3pI5EMBSQbSmQOgVyzLwul0ql2MvOL3+3Hy5Em0tLSIJ5T+/n6cOXNmzInEpDAYjUaYzWbJgxLheyVFMAKcTcgwODiI/v7+tC+MwqrdJP9wHJdwHTx9+nTeJ34hhMiPhmxloK+vD0ajkdaSIGlhWRYcx+GDDz5AcXExDAYDWlpa0NraKg5picViSSt5sViM1isoAMJnLNX8NLkyIHEch46ODnEtnXSOwXEcBd55KBaLJfSAffTRR+jr61OxRISQfEA1HkIUsGPHjqT3GwyGpEGJ3++H3W6noIRkJNdghGVZsCw7aoB04sQJNDY2wuFw5HQcoi9jnYdYlhUf5ziOesZIXqN1SORDQ7YISYNQUSNEakajMeXCg1rS1taGnp6etLalzFv6lmmWtvjghBBCMkFnjgz19vYiHA6jtLRU7aIQQggAjDo0ymw2o7i4eMT9kUhkzHH/wnDDZHieR09PD8LhMGpra1OWTdgPBfT6tnv3bsyaNQu1tbUIBoN48803AZxdY4mQQkE9JPKhgCRDw8fP5pvy8nL09/fTmiuEKMxmsyEYDGZ88RGCkeEVfqvVOuqwP5Zl4Xa7R12FfayABDgbZPh8PjH7VmVl5ZjDxYRJ7hSU6MfwINfv9+Pjjz9GV1cXIpEIvF6viqUjhOQbCkhIApfLBY/HQwGJBkSjURoqlkeEdLijDWnJdvJ3smDEbDbDbrfDarUmfQ7LsnA4HKMGJOkMs4pGo2Ivi81mg9PpHPW1Ca2K+T7JPZ3WU5/Pp5sUucO/V319fTSBnRAiC6rpZIHy7Ocng8GgqcpSOBzWTcWFjI3neUSjUbEHZPiN4zj4/X5JuuZZlkVxcfGowUg6Mp33cfr0afh8vpTnxdGyyuWTVO9dZ2cn/H6/QqVRh5bOo4RIidYhkQ/1kGTB5/MhFAqhvr5e7aIQCdXX16vSG2EwGCg7TR7z+XwJn62UQ12S9aBVVVWpUiE8ffo0SktLUV1dPeZ2sVhMc8E/kZYwfI/Oa4SQdFFAQggheYBl2YyCEYZhUFNTgzNnzoiVxlwrkP39/RgYGAAATJs2bdTtYrEYDUckhOgOTWqXD10NCElBzUpTJBJBIBBQ7fgkN8N7R6QUX6E3m80oKyvLuNdB6l4KYfgZx3Fobm4ecy5avrWep3o9PM+jpaUlYZVzrWJZVrLvBgWdhJB00JmCkDSodVGl+Ur6JmeFO/47aTAYYDKZZDtWNgKBAHp6ehAMBkfdJl+CEo7j0mo5lWqekBKkOOdRMEIISRcN2coSx3EIBAKw2WxqF4UQokFyZqqLb702Go1Js1sla+FOpzLMMIxklea+vj6xtd1isSTdhuM4XWffSmfiaSwWo55OQvIADdmSDzVfZCkWi6GjoyMvWvcIIdLieV62TEoMwySs+eF0OuFyuUZsw7IsDAaDeBPuHy6+FVt4jpR6enrQ3d09Zk+fXrNvCT2YY5Wd53kEg0G0tbUpWDJCCNEXCkgIISTPJJsDMFqgUVlZKXtPr8fjQXNz85jb6C0oSXc4ZX9/P1pbWxUoESFEbpT2Vz4UkBCicRzH0arIOhKJRODz+RQ5Vnl5Oex2e8J9RqNx1OFPaqbbDYfDOHr0aMqhTXrodaa5XYQQIi2aQ0KIjITJrrkOgymUFpJ8IdfnNXyuRfxwq2QrtmeLZVlZAoNoNIr29nbU1NRobhJ+OoTfc7q6u7sxODgoY4kIIUqiOSTyoR6SHHk8HmopI2MqlJMJOVvhznUyu9Vqhd1uF2/DjRV0ZNv7YbVaYTab0zpGrlKlQhZSB2uNkBEs3QpJf38/BgcHEQ6HFSidNmnxcySEaBP1kOSor68Pdrtd8omgajKbzYhGoxRoaQzHcZRGU8M4jkM4HM45ILHZbAm9B/H7jA844rfJJBARej/iK9U2mw08zydUnqXMtjVcKBSCwWBImh1MOCbP86pn3op//elWrnmeRygUSlhwspDRe0AISQfVbsgIFRUVI7L2kNxIcVH2+XzU26JhQ0NDOQcjySrgpaWl4iKI8QFpRUVFQoU+3UaRdId2GQwG2QLgkydPwuPxjPp91socDaEcmZQlFovhxIkTVBEnJA/RpHb5UEBCiALyZQE4Iq/y8vKkcyvKy8thtVplPbbdbkdlZWXCfcODICl1dnbi1KlTY24TjUYVvxgLw+6i0WjGv9mhoSEcPXpUppIpQ87PnBBCRkNDtghRwKpVq8TW7N7eXuzZsyer/fh8PthstqRDXYh6lOi9cjgcMBqNCIfDKC0tzWlfDMPAaDSO6NExGo2orKxEV1dXTvtPVzrvmRZ6StLR19eHnp4etYtBCJERTWqXDzWDkILHsuyIlmEp982yLBwOhzhJuaKiAvPmzctqf4XUfasnufZ+MQwDt9s95pyJ4auzx98vZYt2smBXrhbzYDCIzs5OWfatpO7ubvT19eU8ZI8QQgoVBSQS8Pv9iEQiaheDZIllWTidTtkqXcP3a7VaUVdXh4aGhqQTjIm+5PrbZ1kWNpst6yFZyRZBTNfwNMICu90u3i/n8J1IJIKBgQEMDAzo7jcQi8XEshd6Ni0iDYZh4HQ61S4GIaqgcR8S6OvrA8uyusyrT5QlVABNJhPOOeccdHR0IBKJZFQZ01vFLZ9Fo1EEAoGsn8+yLCwWS9qVkOETzXPNQmUwGJIOiSouLpYkY1g6OI7D6dOnYbVaYbFYVM+slY5YLIZwOIzTp0+rXRSSB4QFS4Uhk6FQSEx/TXMPtYWGbMmHAhJCFGSxWGAwGDA0NATgbMUm05NNMBhELBaDzWaTo4gkTeFwGMFgMKd9OByOjD5Hm82WkAEvn9KNnzhxAo2NjXA4HGoXZVTCb9Xv96O9vV3l0ihDCBALpVKkhrKysoRGifr6egBn5xvSwpqkUFBAQoiKLr/8crzxxhuKTSIm2lBRUaGZngChl2SsCqcwF0ruHpO2tjZUVFSgvLxc1uNkq6WlJecgVE9cLhcuuOACAMArr7wCn8+nconyz7hx4xIWJY1XUlICq9WKM2fOKFwqMhrqIZEPBSSEyMhsNmPp0qWjjsPPdnx+NBqF3+9PupI3kV8wGMxq7gjLsiguLs4qGDEajbJlV0snKFECz/Po6+tDOBxGbW2tqmUZrrm5GcFgUPX3SAkzZ85EZWVlQgrgRYsW4dChQ+jo6FC5dPmjrq5uzN+0ML+spqaG3neS92hSOyEy4jgOra2tCZUYlmUTJjBPnjwZNTU1Ge1XGF9M1JFpIgKr1QqXywWn05l1UDE8eJV6uJZW1p6IRqPw+XyayL4VCATQ2dmJzs5OBAKBgghGeJ6H3W5HUVFRwvBAl8s1akt+MpSsY2wMw8BisaT83bEsm9H7ToheUQ+JRMLhMEKhECwWi9pFkYTZbIbFYkEoFFK7KLoWjUZx4sQJzJw5U7xPSIAgDP2ora2Fx+PJuAWM53lEo1Fak0RhmSyYJ1QkbDZbTkkvhEmvgtGyY8ktnUxgHMflnHEqGo1iYGBAnE+i5No7Xq9X/P/Q0BD6+voUOa5WVFRUSLIIJ6UoHx3DMBn1bgvb+/1+GUtF0kXfa3lQTUYiHo8HsVgMVVVVahdFEk6nEwaDIe+7iaVewyEVKXs2eJ6H3++H2+2WZH8kPalayuODheLiYkmOGR/MqDX3hGXZtBZkjEaj4gKBuXzXOY4TJ47X1tYmzUQ2PFDLRLL5MDzPF8xk9dEsXLgQNpuNJrPLROgZyaSuwLIsqqur0dzcTJ8HyVsUkJCC5nA4ZFsUMZlQKIRoNEpzP3TK4/Gk3EYYniUnpbNrsSyb9kRzo9GI6upqAEBnZ6ckAfho6XVra2uzCvpisRiOHj2aY6nym8lkgsViSegxIrlzu90oKytTuxgkSzSpXT4UkBCic16vFw6HQzNzAPJROuuNlJaW5t3wOZfLldPwnfhW4Gg0iu7ubimKJero6NDEXJN8wLIs1qxZo5nvcGtra9I1cgpZY2MjOjo6aCg1yUtUgyEkBSmGWb355psJLY3C6vDCsIiGhgacc845WZfP7/crsohdIUvWSmU0GlFSUoKSkhLZKnLx89IYhlGsd8TtdosLFea6ErywGGh5eTnKy8slC57jF4/L9EYSCXPbGIYRF6lUUz5+RqWlpTkNsWVZVjPpwgmRmjaaQgjRMJ7nc74I9PX1iQEDz/MjEiDY7fac5htQJUs+0Wg0aYpfk8mU82T1dKhVAZEjwBIm+bvdbrH1m+d5Wt9CZTabDZMmTRL/FtL90jlFWmazOefzhTActJDWw9ESGrIlHwpICFEYx3GyZGQTghIauiUdjuMQiUQSAhKhQmGz2STJRlSI4udQ8TyfkJUrkyxmRBo2mw3Tpk0D8EmiAI7jEoZMDQwM0FChHKST4jcdLpcL0WiUAhKSdyggkZAwfCBfKoTCUItCic7lJlS04r8fUr6/4XAYHMfRhHkJ+f1+sXIcnzkr34dNKDn8j2GYhAnzPp8vYXhjoZ5/Mv2OZfs+sSyb0Btmt9vBsizC4XDCvKn33nuPerJyUF1dLdlwS7o2q4d6SORDAYmE/H4/2tvb0djYqHZRJGG1WtHY2IiWlha1i5IX3nzzTcycORNNTU3ifS6XCz6fL6ElUqgcZLNydjQahdfrlT3LUyGIz6hlNpslS+GrRVpYpV3gdDoTUvxKlalLT4Q0r5kYLRNZKk1NTZg9e3ZWzyXqKC4uhtVqzfozJ0SLKCAhBausrEy1ijvP8/B4PAkVr+FrogitYJlWxniep6AkB8PnNNjtdnGBPqK8+LTc4XA4bxcqLC0tzWlFbiGAGRwcTJkRLhmGYeB0OsGyLAKBgDhMked5bNu2jYYIZYlhGDQ0NCieqpsQvaGAhBQsYeKmklpbWxGJRDBjxgwxO1Z8wBFfnlwmlQr7tlqteTOEUG6RSATRaFTskne73eJwFi0M0VIyw5aWxH9/LRbLiMUZg8GgblewZllW7HkTMprlsi/gbK+rwWDIaniVsI/4YSk8zyMYDGqmB01vCvV3m69oyJZ8KCAhREEejwccx8HhcKCxsTGtPPvJxgqzLJvWhGph/xSUjC0ajYoTdq1WK8xms+oT1ocHzEp9hlq++AkpaeOlquxpLViJn+NlMBgk/54Zjca0MznV1taOWKRPmIsm/L+9vV3T34lCZTAY4HK5aOFKkjcoICFEYV6vFx9++CFKSkrgcrnAMAxCoVDSFk2hl2R4hcBoNCYM9xrrWBzHwWg0Ftw4/HQJmbRYloXZbE7rfVUCwzCqLFKnt8XoTCbTqPN7hM82E5luL2BZNq2WcKHnTU7CdydVcoLJkycnDIkDgFAoJJ4rQqEQDhw4IFs5852w/o4cTCYTysrKKCBRGPWQyIcCEkIUJFREIpEIXnnlFXzmM5+B2WxGS0sLDh8+nPF+UhHmkQhrn5CRhoaGUFZWVrC9SFqa0C41lmVRUVGR0XM6OjqyOtbwyfhqslqtMBqN6OrqynofUiwIW+gsFgtqa2vVLgYhukABCSEqeumllwBk1gLicrlgs9kyOo4w1IUmpp5lNBrF3ge1h2alKxaL0Xh0BdTU1KhdBEUMX/Wb53kMDg6Kfzc3N+P999+n7xshRBGF2SRI0sayLOrr6wu29VhumXb/Cukes1Xok9wtFovYeqx1sVgsYcFAQjJhNBpRWVmZdKJ8qoQe+/fvx0cffaSJZA565Xa7RwyHkxpdn5UnXLOVvhUC+hZLjOM4dHd351VXt8lkyrsLU2lpqW5axuMNb9XMhtFohNls1kWlXArC3BCz2ay777Hc55Hh80Xy6bxFMOI3LqQWH6sCe+DAAXR2diIcDlNFNwcGg0GRc2w+Xp9JYSqMGomChDUghmcuIdricDgynmw4fJV1vRJeg5C9S2+TmNMVjUZhNBpVSe+sB8Nb3mjOQGGI/y10d3ePGMbZ1taW9cR+KXAcB7/fr+tWYZvNltOaMplyOBwYGhrK23O5ltCkdvlQQEJIGvIlGIknZIARTnb5UhnleR4GgwHRaFSxVkq9yedAlCQyGAyjJi44evSoCiUaG8dxOU3G14KSkhJFe+DLy8sRjUY1l+KakEzQlZqQAie05EUikbyopHq9XlRWViraQqknySqnHMflTUBKElVWVqK7uxuxWCzvGlUIURr1kMiHAhJCdELuCZLCWORUaxdokTDsJNdJ/3ogBI3ZZD/Kh4CTEC1rbGykzGSEZIECEpKWmpoa9PT0UNrYPGc0GhMuplpfu8RisQBAXveGhMNh8fXFt5QJwUU6iQ7iAxHqHSFEPjTBnJDsUEBC0mI2m6m7v0DEX1DNZnNCZVXt3pNkWYPi/81HPp8PJSUlI15jJnN/Ruvyp2CEEGkwDIOSkhLVji8sgkvzSORFQ7bkQzVMQsioWJYVFxE0GAwJN7kDVJZlRxxTmKQev7BhvgsGgwgGg6MGDtnmsKesWoRIh2VZFBcXq9Zw53A4Ml4wlxSGDRs2gGEY3HTTTeJ9PM9j7dq1qK2thc1mw4oVK3Dw4EH1CgmVA5L7778fc+bMgdvthtvtxuLFi/HCCy+Ij2vxDUtXPrY8SrEGhhZQT092hKxcwk1IpzvWLV2jPT/+eIWYb18IJvr7+8WgRIrWMsqyRYi06LpSGPS2MOK7776LzZs3Y86cOQn3b9y4Effccw82bdqEd999F9XV1Vi5ciW8Xm+ub1HWVP0FjRs3DnfffTfee+89vPfee7jwwgtxxRVXiEGHFt+wdLW1teXdKsuVlZUoLS1Vuxg5a2hoyHgNErVoObAVFlgc65bLfgqlB2Q00WgU/f394t8DAwPo7OxEd3d3TvulYIQQadntdtTX16tdDEIS+Hw+XHfddXjwwQcThhPyPI97770Xt99+O66++mrMmjULW7Zsgd/vx9atW1Urr6oByZo1a3DZZZdhypQpmDJlCu666y44nU689dZbmn3DCCHps1qtKW+FHngkEwgEMDAwkPSxaDSKzs7OjPcZjUYRjUYpGCFEQqWlpbJnQCQEADweT8ItVdKZ733ve1i9ejUuvvjihPubm5vR2dmJVatWifdZLBYsX74cu3btkqXs6dBMH2MsFsNjjz2GoaEhLF68OOs3LBQKjfjQCCFEL3w+H4aGhsBx3KjBA8dx6OnpSbgN703p6enBwMCApnvZiDKKi4thsVjoeyADhmE0M1zL4XCgvLxc7WLkNTWHbNXX16OoqEi8bdiwYdRyPvbYY9i7d2/SbYQGraqqqoT7q6qqsmrskorqTZMHDhzA4sWLEQwG4XQ68cwzz2DGjBli0JHsDWttbR11fxs2bMC6detkLXMhs1gscLlcuhg2p2Usy2LSpEkJ9wWDQbS3twP4JHMSz/NgWTbv19YoNGNlwomfwC5MPE9W4Rk+JJRhGPh8PnEfQgIAqoQSk8kEg8FQMNl6lOJyucTU41pgNBrpWpHH2tvb4Xa7xb9H++61t7fjRz/6EbZt2zbm9yFZ5kY152mqHpBMnToVH3zwAQYGBvDUU0/hq1/9Knbu3Ck+nukbdtttt+GWW24R//Z4PDS2U0JWqxUMw1BAkiOj0YiZM2cm3Of1euH1etHX15dQiWQYBk6nU+kiEplEo1EMDQ2lXTkcHlCM1hrL87zYI2w0GmGz2ahyQoiMioqK8noNJDKSmml/hQRQqezZswddXV0499xzxftisRhee+01bNq0CUeOHAFwtqekpqZG3Karq2tEJ4CSVO9nNJvNmDx5MhYsWIANGzZg7ty5+P3vf4/q6moAGNF9lOoNs1gs4oeW7odHMsMwTMFlO1KCy+XCsmXLNNP9T6QXjUbR19eX8QVNGHaVzvMYhoHb7aZghBAZ0TWQaNVFF12EAwcO4IMPPhBvCxYswHXXXYcPPvgAEydORHV1NbZv3y4+JxwOY+fOnViyZIlq5dZczYfneYRCIUyYMEGTbxg5G0Q2NjaqXYy8ZDAYcOWVV1I++TwUDAbR19eX0z7SyZBVUVFBiQIIkVljYyP1jhBNcrlcmDVrVsLN4XCgrKwMs2bNEtckWb9+PZ555hl8+OGH+NrXvga73Y5rr71WtXKretX62c9+hksvvRT19fXwer147LHHsGPHDrz44osJb1hTUxOampqwfv161d8wQuRgNpthMpkwNDQEALjwwguxd+9edHR0qFwyIgWfz4dgMCjJvnieRzQaFf+OX7clH9JyE/kIAa3BYBDvG570INM1hAqN0WhEbW0tvUcFKl9War/11lsRCARw4403or+/H4sWLcK2bdvgcrkkP1a6VA1Izpw5g+uvvx4dHR0oKirCnDlz8OKLL2LlypUAtPmGkbNYlkVVVRV6enoKIo2o3Bef4cPgbDYbpk2bBpvNhpaWFlmPTeQn56roQuIDt9tNlSSS0vBetpqaGtTX14Pnebzzzjsqlkw/qAeS6M2OHTsS/mYYBmvXrsXatWtVKU8yqv6qHnrooTEf1+Iblgm/3w+GYTSVhUNKDocDvb29ahcjbSzLwul0Zl1pUyIoMZvNYvaksrIyeDweCkiysGzZMhw4cEBcy8NoNOL8888Xg76TJ0/i2LFjipQlGAwm9GhIzWQywW630/ARkjae59HY2AiWZVFdXY26ujpwHIfu7m6cOnWKMrONwmQyweFwqF2MMbEsS5kwZZQvPSRaRGG+jAYGBsCybN4GJMDZE3QsFtPFD8ZgMGg6RzvLsrDZbCPSuZLMXXzxxTh58mRCQPKZz3wGBoMBHMdh165digUkPp9P1gqe3W6H3W6Xbf8kvwhZ++bNmweTySTez7Iszj33XHR3d0s2vDCfGAwGOBwOzQ+LNBqNKCsro4CE6A4FJCQnNTU16OrqEtc/IEQL7rjjjoS/g8EgfvrTn6pUGkK0w2Qy4ZJLLlG7GLpTVlZG6dcJkREFJIQQIpPu7m5Zew8rKytpPDshhCiEhmzJh2ZAkpyVlpZqvhs7F3JknYlEInj55ZcRiUQk3S/RFgpGiBb09/fD7/ejvLwcF1xwgXi/zWbL6yHFUqmpqaFhkYTIjK5mJGdGoxEOhwM8z6O/v1/t4ugCz/Pwer1ihTUajSIUClHlIE9wHCemcJaakFGLghGSLpvNhlgsJk54FrAsK6YADoVCahVP08rLy2GxWCiDHQFAPSRyol8YkYTJZKLxtTmIxWJib4nJZKJVgHWO4zgEAgFZ9s2yLLXWkoxYrdZRs7ANTzlOPsEwDKXTJkQh9CsjkmEYJmHBLZIdu91OFQSSFMMwVDkiWTGZTLBareLfQiASiUQoq9Yo6HpGiHKoz59Ixmg0orGxEc3NzZrsYsymkk/DYoiW2Gw2FBcXq10MokPTp09HbW2t+LewJpOca+Tomdlsxrhx49QuBtEYGrIlH6ptEck1Njbi9OnTmlpPw+VyoaysTO1ipMXj8YgnIIZhFAuK1q5di9raWrz//vu4//77FTlmPgoGg7QGACGEEJIB6vuXmcfjQU9Pj9rFUJQWh5ToaahLfGuI2WxWbG7Oww8/jIMHD2LevHn48Y9/rMgx85EaLWiEEOk4HA5UVlaqXQyiUcI5XqlbodBHDU3HotFoQaZ2dbvdlDEqDUeOHBGzMfE8P2IsN8uyivWQtLW1YceOHdi/fz+mT5+Oa665hoasEUIKjtFoHDUJACFEHlTbILJwu91idK+loVuZUGJi+fHjx1FVVQWHwwGO4xJSb3o8HsWH/uzbtw+hUAhFRUWYNGkSDAYDjTHXCJPJRAEiITIzm80wmUxqF4OQgkNXNyKboqIimEwmdHV1geM4tYuTESUzhsViMcRisYTjRSIRHD58GL29vYqnU/7oo4+wfv16RY9JUnO5XAlZkgjJFcdxmhwSwrKsateM0tJSSqtNRkWT2uVDQ7aIrOx2O+rr69Uuhqa9/fbb+OijjxLue+WVV9DR0aFSiQghhcDv92tuQUSj0Yjx48dT6nNCCgz1kBCiAcePH0dra6v4t16HuRU6j8ejuQoeIemIxWJ46aWXCvrcM27cOBoWScZEPSTyoV8ekR3DMKiursaZM2dU+WEVFxdrfhV5juMKtiKwdOlSnHfeeQiHw/iv//ovtYuTE60OgVFSVVUVvvSlLwEAHnroIUqBrBOFfA4SGAwG3WRjJCTfUECigGg0Co/HA7fbrXZRVMGyLOx2O4qKiuD1ehGLxRQ9vsVioYwpGlZVVYU5c+YgHA7jM5/5DF599VXqZdAxu92OuXPnguM4+t1pSE9PD3ieR11dHYCz89SE4DkYDKKtrU3N4qmuqKgoL4aJxWIxagSQEfWQyIeaAhQQiUTQ19endjFUJ0wWVGqyONEem82G8ePHY/z48WJLZH9/P1pbW9HR0YElS5ZQhps8QK3M2nP69Gk0NzeLfweDQXHiuN/vx6FDh9QqmupYlkVZWVlefG9jsRjVN4guUQ8JUVRFRQX6+/vR39+vdlGICqZNm4Ybb7wRAPDDH/4QgUAAr776Kl599VWVS0bI2OIDZZ7ndZcOm2XZpBVunucV77UmhJDhKCAhhCjm/fffxw033AAAuksFnUpPT0/evaZs6LGyngrLsvjDH/4Aq9UKnufx/vvv636+k+DYsWMF3TtCSCZoyJZ8KCAhiotfn4QUHqq0E73hOA6/+MUvxDkGep383dPTg23btiXcF4lEVCqNNlgsFlRVValdDEIKHgUkRHEsy8Jms6GiogLd3d2yHqu8vBwWi0XWYxCiBJ/PB47jNL9oW09PDx588EEAZ8ucL3p6etQuQs44joPf71e7GJrCMAyl+iVpox4S+dCvkKjCYDDA4XAgEAhgaGhIth+cw+HQ7CT6ZD0F+TCpksgjHA7DaDRqPiDx+Xx466231C4GISmZzWbN/54IKRRU+1FQoXeND8eyLCorKzVbCZd7aJHVah1x4zgu4UYIIUQeDocDxcXFaheDEALqIVEMx3Fob29HY2OjZlvsySeEblm5giWz2YzLLrss4b6+vj4x25TQY1QoXbVSSfbb4nmegjtCCCE5oyFb8qGAhKiuvr4eXV1dBTO2uaKiAosXLx5xf0lJCa666ioAwAsvvIBAIIBAIIBIJILS0lKli6k78+bNww9+8IMR9x87dgx33323rMfu7u4umIsGIYQQIjVtjpUhBYVlWZSXl0vadc4wDMaNG6fJlXcZhkna8yLcz7Isli1bhsrKSgCUlSpdDMPAZDLBYrGIN7PZrEiPJAUjhOhLRUUF3G632sUgOiP0kCh9KwQUkBBNMBqNcDqdkgYlZrNZc/NTqqurMWHCBPFvq9UKm802YnVyt9sNs9msdPHyAsMw4o0QQpIxmUw0fJqQHHzta1/Da6+9Jtn+tFVbIwXNbDbD6XSqXQxZmc3mhDTEJpMJZrMZJpNpxAWypKSEWvCykM+tShzH6XYNDKKeSCSSd4tV5sJms2musUoKkUgEgUBA7WKQAuH1erFq1So0NTVh/fr1OHXqVE77y79fJNE9KVqtpGr5knq4VFtbGz766KMR95tMJtjt9oSAbOrUqWhqapL0+PksGo3C4/EgFouJNyGttJyUHFIXDAYxMDCg2PFIfvB6vRTIxqmqqsrLHmi/34/e3l61i5HXaMjWJ5566imcOnUK3//+9/Hkk09i/PjxuPTSS/GXv/wlq6yyFJAQTTGbzaivr89pHxaLBQ0NDZKUR+70u16vl1ouJXLgwAHcfvvtCfe98MILuPfee2U7ZjQazYsF8wghhJBMlZWV4Uc/+hHef/99vPPOO5g8eTKuv/561NbW4uabb8axY8fS3hdl2VJYW1sbqqurYbPZ1C6KZrEsi/Hjx+PkyZNUWcfZoKinpwfl5eVqF0XzvF4vvvOd74h/09o/hOhTY2MjOjo6EAqFJNunwWBAfX19Xg7XIsqgtL/JdXR0YNu2bdi2bRsMBgMuu+wyHDx4EDNmzMDGjRtx8803p9wHBSQK03L3m5awLIuqqir09fXRmFhQpq10cRwnfl+uv/561NfXo729HX/6059ULhkh6hF6euMr4uedd544tNXn82Hfvn1qFS8puYIGCkYIkUYkEsHf//53PPLII9i2bRvmzJmDm2++Gddddx1cLhcA4LHHHsN3v/td6QMSnuexc+dOvP7662hpaYHf70dFRQXmz5+Piy++OOehNoTEs1gsGc8FsVqtskyMH34xJ9q2evVqnHPOOSgvL0dFRQX6+vrw3HPPSXqMSCSCYDAo6T7TwXEcfD5f3ieAINLw+XyIRqOorKxEWVmZeH9FRYV4fnU6nQgEAjh69Kgkx+Q4jhreCMlzNTU14DgOX/rSl/DOO+9g3rx5I7a55JJL0s6emlYNKxAIYP369aivr8ell16K5557DgMDAzAYDPj4449xxx13YMKECbjsssvw1ltvZfJ6CBmTkH0qXVarVfLMVNQ7oT9r1qxBRUUFWJZFRUUFPvvZz0p+DLUy2nAcB4/Ho/hxiT55vV44nU40NjZi+vTp4i2+scdms0maQEOL50yWZRMyHBKSDZrU/onf/e53OH36NO67776kwQhwNltoc3NzWvtLKyCZMmUK9u7diwceeAAejwdvvfUWnnrqKfzf//0fnn/+ebS1teH48eNYtmwZvvCFL+DBBx9M+wURMpaSkhKUlJSktaYErT1BBMFgUPZKkRYrXYQks3DhwhEjGIafL1mWhdGYv6O4bTYbqqur1S6GbKhXiijt1VdfTTpPc2hoCN/4xjcy3l9aAckLL7yAv/zlL7j88stHba1ubGzEbbfdhmPHjmHFihUZF4SQ0TidTowbNy7ldtXV1SgpKVGgRMoZbVV3MrabbroJH330EaLRKGKxmOT7HxgYgN/vl3y/hCjF6XTC7XbDarUCODvh+/LLLxf/JvrS1dWFvr4+tYuR96iH5BNbtmxJOkogEAjgj3/8Y8b7S6s5ZNasWWnv0Gw209oJKXR1daGoqCjvKs9qqq+vl3XVXaE1XIrgoKenBy+//DIuuugiAGdzx1ssllGHE7AsS0FJFv7rv/5LbPHV6gk9F52dnSgvL8/rVm2SPY7j0NXVNeK7zzAMnE7niHNKLBbD9u3bJc1qpRWlpaW0yCwhEvF4PGKg5PV6ExoxYrEYnn/+eVRWVma836yuZMFgEPv370dXV9eIYQtyjNXON3KvbZGPDAYDqqqqcObMmVEf10ulneO4hIs+z/MIh8OIxWIJ34tjx46hvb1djSLmBa/Xq3YRZEXnEDKWsa4zwrkyGAyKQy44jlMlSYMSqFGHEOkUFxeLQz6nTJky4nGGYbBu3bqM95txQPLiiy/iK1/5StLFwBiGkWV4BCEsy8LhcKCoqAher1e80DIMI6aX07NklYf+/n6avKxBwWAwr9fHYVkWy5cvT6jABYNBvPnmmyqWisghGo1KHthqLVB2Op15uSo7UQetQ3J27gjP87jwwgvx1FNPobS0VHzMbDajsbERtbW1Ge8344Dk+9//Pj73uc/hl7/8JaqqqjI+ICG5KCsrQyAQQDgcBsMwsFgsii0YyPO8ZOl/eZ6Hx+OBy+VKOhGfVnDXLq/Xq7kLhBQcDoeYQOLaa68Vh0DyPI/+/n4KSHTK7XaLn6Xcc9K01vtvMplQUlKSUaZGQsjYli9fDgBobm5GQ0ODZMmEMg5Iurq6cMstt1AwQlQjfPmVzpoiZSU0EonglVdewWc+85mkrXe7du2iBSGJYgwGAz71qU/hy1/+MgCI81Ioc4++MQyDiy66SAxIWJbV1fo1uVZ06urqCmKolpaCwHxX6D0k+/fvx6xZs8CyLAYHB3HgwIFRt50zZ05G+844ILnmmmuwY8cOTJo0KdOnEiKJuro69Pb2Jk03pzcvvfRS0vu1dAIiZ/E8j+7ubrWLkaCrqwvFxcWw2+057eeb3/wmFi5cCIZhZE0OQUgmamtr0dvbi8HBQbWLomnt7e00XJ4oYt68eejs7ERlZSXmzZsHhmGS1leymcKRcUCyadMmfO5zn8Prr7+O2bNnj+gK/eEPf5jpLguS1+tFJBLJ67zociouLlal0s7zPGKxmGSVNgo81FNTU4Obb7454b73338f/9//9/+pVCJ1/OQnP0FjY+OoiSEOHjyIP/3pTyqUjGQrGAxiYGBA7WJIIpteEqPRiJqamoLoHSFESc3NzaioqBD/L6WMA5KtW7fipZdegs1mw44dOxJOFgzDUECSJo7jaI5ADtRsxaUgIj8YjcYRQ0/POecccByHxx9/POH+aDSq2XVHhHJl2ktiMpnw5S9/GY2NjbDZbCMqfsJwrWAwqLmeITI2Yb6bkgvFaml4H8MwNG+EyKLQh2w1NjYm/b8UMm4++PnPf45f/epXGBwcREtLC5qbm8XbiRMnJC0cIVpFY3bzU2VlJZYtW4bzzjsvIejVckrUcDic1doRLMvi/PPPh9VqHbFqN3D2Itjc3Ixjx45JVVSiMIZhcOrUKYTDYfE+Yahr/JDXcDiMjo6OnI+nhYqT0WjMeQijXnAcB5/Pp4n3nRSeLVu24LnnnhP/vvXWW1FcXIwlS5agtbU14/1lHJCEw2F84QtfoK5QUtC0lk2GSMflcuE73/kOKisrYTQaxdbmfGUwGEZtSX/99dexfft2hUtEpMKyLPbu3Sv2osViMTFZRiAQEMd4+3w+7N27N+vjaOX3IaSHLysrU7soikm2HhyRD63U/on169fDZrMBAHbv3o1NmzZh48aNKC8vHzEcOh0ZRxVf/epXRwxnIISQfMIwDO6++27U19djaGio4NaDEdan0OqFkGiPFirF5eXlBRWMEKKm9vZ2TJ48GQDw17/+Fddccw2+/e1vY8OGDXj99dcz3l/Gc0hisRg2btyIl156CXPmzBkxTvOee+7JuBCFKhwOo6WlBePHj1e7KEQH+vr6UFxcTL2TMkjWSxCNRvHzn/8c//u//4u//e1vKpUsPYFAAJFIBJWVlZLtc926dTh9+rRk+yPKYRhGTN08HM/zlLWKkCwV+hySeE6nE729vWhoaMC2bdvEXhGr1ZrVsgUZByQHDhzA/PnzAQAffvhhwmNKTqDLF1r9opHUpFwosaGhARMnTky47+DBgzSZWCHJzl1CkKKXADDdFuqlS5fioosuGrXCGolEsGHDBnR2dlIq0TyxZ88eTJkyBfX19SMea2trw9GjR1UoVXrcbjeMRiN6enrG3K66uhpWq1WhUhFCVq5ciW9+85uYP38+jh49itWrVwM4W3fJpqE944Dk1VdfzfgghOQjnuclCcI5joPJZEJxcXHC/RMnTkRlZSWCwaA4sViqAIikJny25513HoaGhrBt2zaVSzQ2nufh8XjgdrtH3Wb58uU477zzMHHiRDAMkxDECC1/HMehtbWVsgDmEa/XmzQpQ0tLC9ra2uDz+bLet9xD+4xGY8qMWaWlpbBarQV1boxEItTTRVR133334ec//zna29vx1FNPicMl9+zZgy996UsZ7y/jgGRwcBCxWAylpaUJ9/f19cFoNI55MSQkH+UaJBQXF8Plco24v6amBsDZtK79/f3o6urK+hhkpFAohIMHD2LmzJmjbsMwDObOnYtgMKiLgMTn8404BxuNRkybNg0AsGLFCkyYMEEMtoZ/b6nHNn8NDQ2NOIecOHEi5/lRak+6ZRhmRGNOIYjFYgU3t00LaMjWJ4qLi7Fp06YR969bty6r/WUckHzxi1/EmjVrcOONNybc/8QTT+Dvf/87nn/++awKUsikXGiPKEuKXP+zZs1KGPsv7E84CdntdixbtgzPPfdcQbUAyq2rqwv33HMPHnzwwVF7u1iWFXuwXC4XvF6vCiVNzWQyidlOnE5nwmtxOBy45ZZbAJx9PaN9X4VV2qlnRP+SVWBaWlrQ0tIi6XG0MJGdzomEqGdgYADvvPPOiGxvDMPg+uuvz2hfGQckb7/9dtKJ6ytWrMDtt9+e6e4KHs/zaG1tRUNDw6hjuknhYBhGbOH2+/3iWgEMw+Dyyy/Hnj17MDQ0pGYR85LQKDBaZX3+/Pl48MEH8cUvflHhkqVn8eLFuO222wAg4TwiDMGiBo/C4fV6s5pQmg21AxKz2Yxx48apWgZSeLTaY6G0Z599Ftdddx2GhobgcrlGLJSeaUCScdNCKBRK2oIWiUQUOwkSUgjsdrvY6i3o7e3V7AJ9+YplWd1W6IVeD0L0zGKxjJiQ73a7UVtbq1KJ1DUwMCDJQpaE5OLf/u3f8I1vfANerxcDAwPo7+8Xb319fRnvL+OAZOHChdi8efOI+x944AGce+65GReAEL3jeV6xbETUMiMtjuPwm9/8BgMDAym3dTgc2LBhgyYz+ezfvx9333131s/nOA6xWIyyapG0CN8XpQxvFCgtLUVRUVHBDtdSe94OIQBw6tQp/PCHP4TdbpdkfxmPEbrrrrtw8cUXY9++fbjooosAAC+//DLeffddzU/6JESLTp06BYZhUFFRMeIxg8EAs9mMcDgMAJgwYQL6+/sRCoWULmZe4jgOBw8eRDgcFi/wyYZtMQwDs9mMefPmabIS5HK5RqSNzlRbWxs+/PBDcByn+lAcon1qVIiFyesOhyNl5i1C5ECT2j9xySWX4L333sv52iPIOCBZunQpdu/ejf/8z//EE088AZvNhjlz5uChhx5CU1OTJIUqRKFQiIZX6Fgua5K0trbCbDajoqJC7G0RvgcGgwEWi0UMSKZMmYJjx45Rd73E2tvbYbfb4Xa7R51HIkxwb2pqwrFjx+D3+xUuZXLV1dVYsmQJPv/5z+eUYKG5uRl/+ctfJCwZyUdqBassy47I7kkIUc/q1avx7//+7zh06BBmz549opHgs5/9bEb7y2oW9bx58/DnP/85m6eSUZw5cwaVlZVwOp1qF4VkSao1Qnw+H1wulyZb4vPVH/7wB3z729/GsmXLUm5799134yc/+Qn279+vQMnGZjabcd111+Hiiy/OqUEjEolQdi2SNupBI4WKekg+8a1vfQsA8Ktf/WrEYwzDZDysM62AZGhoCA6HI+2dZro9ISR9wgSyQsy9LzdhkbfRKvZGo1FT8yw2bdqE+vp6sCybUwD74IMP4t1335WwZCQfxWIxzVaOCCHKkrphIq0r2OTJk7F+/XqcPn161G14nsf27dtx6aWX4g9/+INkBSSkkEUikZxWUSby+NnPfoZ/+Zd/Ue34LMvi0UcfRW1tbUbBSPzk9fgbyQ/9/f2UhS/PnTlzJq0kHIQoSYrzTlo9JDt27MDPf/5zrFu3DvPmzcOCBQtQW1sLq9WK/v5+HDp0CLt374bJZMJtt92Gb3/72zkXjJBC0tHRAYZhMGPGjIT7h3cP79u3D52dnUoXryC8/PLLCAQCWLly5ZjbsSyL4uJi1XqBy8vLccMNN6CyshIGgyGtYERoyUo23OD//u//cPz4cVnKSpQ1Vg+G0PsnyHR43/DnE3VQL5W6aMjWJ2KxGNavX48HHngAZ86cwdGjRzFx4kT84he/wPjx4/Gv//qvGe0vrYBk6tSpePLJJ3Hy5Ek8+eSTeO2117Br1y4EAgGUl5eLi4ZddtllNO49B4FAAAaDYcTaE3qRzYRarf7QlObz+dDd3S3+HYlEkmaROXPmDAKBACwWi5LFKwjHjx9HZWUlVq5cOerK7cAn3/NJkyZh0aJFePvttxUrY0NDA5YtW4ZPf/rTAMZepVr4bQkJF3bv3i0utBnvnXfeocU281hDQ4OYkMHv94sNGpnOeaN5I4SQeHfddRe2bNmCjRs3ivNJAGD27Nn43e9+J09AIhg3bhxuvvlm3HzzzRkdhKTH6/UCgC4DkmzHsNNE2uSCwSAYhhkRsBkMhrRbxUnmAoEAOjo6UF1dnbDi+XAMw2DRokVwuVyKBCQ1NTUwGAz49Kc/jS9/+ctpff48z8Pv96OnpwcAsHXrVs1kBiPSE86lZrMZZrMZwNnvaXyq6u7ubvh8voyHgVIwQshZ1EPyiT/+8Y/YvHkzLrroInznO98R758zZw4++uijjPeXVZYtQuLlOqGWnCWc6IQW+EAgkPRxIp+33noL+/fvxyOPPDLmdkKL8/D/y4FlWfznf/4nysrK0v6txWIxcByHDz/8EL/4xS8AnA1qckkLTLStq6sLwNnKwPjx45NuU1FRgQsvvBAvvPBCRucSCki0gT4HoiWnTp3C5MmTR9zPcVzS3vhUqBZJVDVWK7QeRaPRrC8aAwMDeO6555I+xvM8nn32WZrgrqBoNJqy0jZt2jQ8/vjjspXBarXib3/7G8rKytLuGRPGmG/ZsgV33HGHbGUj+mQ2m7FmzZq0FhbkOI56sTWkra2NkhYQzZg5cyZef/31Efc/+eSTmD9/fsb7y6/aYB4YGhpCNBpFTU2NquUYq+IzvMId31JMchONRvHyyy8nfYxax7SFZVlZFzOdOHEibrvtNhiNRhgMhrR6N4RgZMOGDfjggw8SvjPd3d0oKSmhFa7z2Pnnn4+ysrKU29G5Wp+oh1x9NGTrE3fccQeuv/56nDp1ChzH4emnn8aRI0fwxz/+Ef/4xz8y3h+dlTRGSy1SwvCQ+Jtw/3C5/GCEih05y+v1Jr0RZXg8Htx3331pp8M1mUz4wQ9+ALvdLlkZzj33XFx33XUYN25c2sEIcPZ3+MADD+CDDz6Ax+NJeCwajVKK3zzncDjEgJNhGNhsNvFGWbUIIVJas2YNHn/8cTz//PNgGAa//OUvcfjwYTz77LMps1UmQwEJyYgQjCSrIGXbgk+tdURL/H4/nnvuubTSazIMA5PJhMsuuwxWq1WyMkyePBlLly7NKFgPBoN488038fzzz48IRkj+4nk+6TAehmHECe5mszmj4bFCMEIBCSGJhN+F0jetuuSSS7Bz5074fD74/X688cYbWLVqVVb7SrsmeNFFF+Hpp58e9fGenh5MnDgxq0IQfWFZdkRrm5BalIYVET2L7wns6+tDJBJJ+Z0WfgslJSVidqNcuN1usbcl3WA9GAzixIkTuPPOO8ecTKj1ixvJXCwWQ19f35jbZNrbQd8T7dHKyAlCBBMnTkRvb++I+wcGBrKKB9IOSF599VV8/vOfH3WSZCwWQ2tra8YFINqTbiafZC1u2QYklMqWaIHZbEZpaSl4nsfXv/51tLS0pP3cTZs2YcGCBTmX4Wc/+xk+//nPp7VtLBZDNBrFrl278KMf/Sjl9v39/TT8rwD5/X6Ew2G1i0GyFIvF0NbWRkEi0ZSWlpakw4BDoRBOnTqV8f4ymtR+//3349///d+xf/9+/OlPf4LT6cz4gCS/GI3GEUNbotFoQacCpkn++SWdz1P4HfzkJz/Bc889h82bN+d0zHR+P8Lv7r//+7/x0ksv5XQ8UjjC4TC2bds2aos7tcQTMjqa1A78/e9/F///0ksvoaioSPw7Fovh5ZdfHjX1+FgyCkiuuOIKnH/++bjyyiuxePFi/O1vf6NhWjKIRqM4ffo0qqurC65SK/eaDoRkI90LgtlszimL1V133YWmpqaU2wnByN133419+/Zl1Prt9/vBcRyKi4uzLifRlkyuEzzPU9BBCMnalVdeCeDsPLWvfvWrCY+ZTCaMHz8ev/3tbzPeb8a13enTp+Odd95BfX09Fi5ciH/+858ZH5SMbbRJilrFMMyIibfCnJJMxGfxKrRAjGjTs88+i0OHDgFIPRxR+B1MmzYNn/3sZ7M63pQpU+B0OkedyC78rmKxGP70pz/h/fffx8DAQEbH0FImPyKNsc6ZoVAo7XMxNQYRMjaa1A5xvnBDQwO6urrEvzmOQygUwpEjR3D55ZdnvN+san1FRUV47rnn8K1vfQuXXXYZfve732WzG5InhExA8ZWobH9A8SmGCzkVcGlpKcrLy1FeXo6SkhK1i1OwXnrpJRw5ciSti4JQIZw8eTIuvfTSrI+ZLMCPFwgEsG/fPjz22GOUTYukFJ/kIBwOJ50AH1+hINoSi8V01UBJCkdzczPKy8sl21/aQ7aGXyAZhsHdd9+N+fPn41//9V/xyiuvSFYooj/CUCspI3mDwZBW6tV8YzKZsHDhQthsNgBn1yV57bXXAKS3ejiRVigUQjAYFD+PdLAsC7vdDr/fn9b2wpoRqYJwnudx5swZ/OxnP0u7LKPth+f5gg7681EsFgPHcWJwzHGc+D2MxWLo7+/H22+/nfS5FIxoUzAYxJkzZ9QuBiFJvfzyy3j55ZfFnpJ4Dz/8cEb7SruHZLRK0Be+8AW88cYbOHDgQEYHJvlPGFqSi0LLvsWyLFavXp1Q+XW5XFi9ejVWr15NiSRUsGXLFqxfvz6j5zQ0NODxxx9P+7vrcrnw1FNPweFwjLqNlC3YkUgEnZ2dkuyLaMeOHTvQ3t4O4Oz3RVhUlRbEJEQaNGTrE+vWrcOqVavw8ssvo6enB/39/Qm3TKXdQ/Lqq6+itLQ06WPz5s3Dnj178Nxzz2VcADK6trY21NXV5TRJVknxrXJ62K8eLVu2DAcOHKAWMxXwPI9YLDbmitcGgyGnwCHV4nX//Oc/c87gRQrTRx99hOPHj4+4n4ZqEUKy8cADD+DRRx/F9ddfL8n+0m56Xr58+ZgXy7KyMnzlK1+RpFDkLKmHQClh+ORKoRIn9X61LptJ/ckYDIaE3hKz2Yympqa0MjER6Rw7dgx33313Wr9HYRiUwWDAunXrRm3IyYRwLgiFQrSOCMnYgQMH0NbWNuaimYSQ1KiH5BPhcBhLliyRbH8Zpf0lJB0syyb8iKT6Mempp0Sq12wwGGA0GmGxWBAKhQCcXclbaKXv6OiQ5DjkEwzDjJj/MTAwgPfffx8AEsboj0aYU7VgwQJYrdacy8TzPN566y188MEHOe8rfp8+nw8Oh4PmkuSRrq6uEWmgT58+jUAgoFKJCCH56Jvf/Ca2bt2KX/ziF5LsjwISooh0KnHpGB7saF02r3tgYABut1t8rQBgtVoRjUbF3iaHw4GGhgYKSGTAMAycTicCgUDS7xnHcWNmwpKrcv/Pf/4Tb7zxhqT79Hg8sNvtFJDkkVOnTqW9SrIeGncKWTQapRTdGkMLI34iGAxi8+bN+Oc//4k5c+aMmF5wzz33ZLQ/CkiILIaPpZdy9fJcx+krRThxZfKaOY7Djh07cMkll8BmsyESiSAWi8HlcsHpdMLr9Wr+deezcDgMs9mc0XNMJhMt+Ek0SU+NO4VoYGCAUnsTzdq/fz/mzZsHAPjwww9z3p9+BuUTRehtrgYhSvF4PLjqqqswNDQkpldNx/3334+LL75Y5tIRQgghZ685c+bMgdvthtvtxuLFi/HCCy+Ij/M8j7Vr16K2thY2mw0rVqzAwYMHMz7Oq6++OuYtU9RDonEdHR0oLS2Fy+VS5HhyBiNS9pII+6FWZ6KkdL9vDMPAaDQiGo2CYRj867/+K6699tqEbe644w60trbKUUxCCCEy0MOQrXHjxuHuu+/G5MmTAZxNXX/FFVfg/fffx8yZM7Fx40bcc889ePTRRzFlyhTceeedWLlyJY4cOZJWXfPqq69OuQ3DMHjqqacyKjcFJBqXbwsDCtmncg1KhGBED0FJqlSx2eJ5Hh6PB263W/J9k9H94Q9/wJe//GU0NDSk/C4Ln3tJSQlKSkoSHvv2t7+NwcFB8Dyvm9TecnA4HGKw9ve//51SWisk364thJCz1qxZk/D3XXfdhfvvvx9vvfUWZsyYgXvvvRe33367GFhs2bIFVVVV2Lp1K2644YaU+y8qKpKl3BSQEFkxDJNw0RP+L2VQonXZXPRPnz6Nmpoa2O128DyPSCQyotLK8zyCwSAFJGkwGo0477zzEu4bGhoSM2clY7VaEQqFRnzHXnvtNaxevRr19fUAMOaK52NNFv/Upz6liTH8fr8fVqs15RoocjGbzeJn09PTgzNnzsDj8UgyJpmMTu3vHSF6pGYPyfD5RBaLBRaLZcznxmIxPPnkkxgaGsLixYvR3NyMzs5OrFq1KmE/y5cvx65du9IKSB555JEsXkVqFJCQnI0VFAwPSIDCvBBmGoAdOHAAVqtVDEj8fr9srRKFwGQy4Vvf+haAT4KEjo4OnDp1Cl1dXUmf43K5EIlEkn6/e3t74fV64XK5EIvFsq7Mj5WtSykejwdGo1G1gETAMIzYsnfixAmcOXMG3d3dqpaJEEK0QmgEE9xxxx1Yu3Zt0m0PHDiAxYsXIxgMwul04plnnsGMGTOwa9cuAEBVVVXC9lVVVaoPIabZyyRnwjCs0W5yYllW9YpUOqR6L9SuvOpZ/Bwmg8GAuro6bNiwIavhdBs3bsRTTz2lSHAdjUZ1uUhqpgwGg3hramrCr371K7WLRAghmtHe3o7BwUHxdtttt4267dSpU/HBBx/grbfewne/+1189atfxaFDh8THh9clxurpVwoFJCQvGI1G1X9MSnA6nRmnnSVAIBDAt771LXg8HsRisYQ1XbL15JNP4qc//SkAyL5WwDe+8Q3s3r1b1mMQQggZm5ortQtZs4TbWMO1zGYzJk+ejAULFmDDhg2YO3cufv/736O6uhoA0NnZmbB9V1fXiF4TpWm/aZmgv78fkUgEZWVlahdFMjzPyzbZW6tyzTLm8/kS9pPLvgpRNBrF3XffnfCdE76H2eA4LiEQicViYFlWlsA4HA7rYr5UrjiOw8svv4ydO3fmfY+QFILBoHheIMnFnyP19Bs6c+YMAoGA2sUgeYLneYRCIUyYMAHV1dXYvn075s+fD+Ds9WXnzp349a9/rWoZKSDRAaFFN99QhWNsra2t4DhOHDcaX3EeHBzEiRMnwLKsOCmZgpPU0l3BOl2dnZ14+OGH8Y1vfIO+z1kKBAJ47LHH8PnPfx4GgwEDAwNob29Xu1i6IAyXJSPFnw/1kgAl3mjz14i69JD292c/+xkuvfRS1NfXw+v14rHHHsOOHTvw4osvgmEY3HTTTVi/fj2amprQ1NSE9evXw263j0hNrzQKSIiqpMi2pSeZpD0WJltbrVZUVFSI93s8Hpw6dUqcgBYMBmE2mwvqfVSK2Wwec/5PX18f/va3v2HmzJmYP38+bDabpMdX6sIXDodVm9geDAaxfft2TJ48GWazmdL+SkDqNZ/0hmGYnF97sjH2hOjBmTNncP3116OjowNFRUWYM2cOXnzxRaxcuRIAcOuttyIQCODGG29Ef38/Fi1ahG3btim23t1oGD7Pf2UejycvshM5HA5FxvdlUyHJNZ+9FKvDC5N+9fB1FjIrpfua7XY7li9fLv794YcfjmhBdrvdupjcr0c+nw9+vz/ldn/+859RUVEh2ZCt+OFk3/ve99De3o5IJCLJvpOx2+0oLi6Wbf9EWjzPIxAIJAzZig+cOY4b85yQj73uwOjXk0wSiyTbh9Lrtpw8eRLhcFix42nJ4OCg5tLZC3XJ7373uylT7UotFArh/vvv1+T7IiWqwZCcGQwGRTJqjUVPXdtC4JRuQOL3+/HCCy/IXCoiBWEeiRQBdvx3+r777sNdd92FN954I9cikjzR39+PUCg0Yk6UHhpl5DLaby+TYGK0fWjhOkfUp4chW3pFAQlRnXCSH34hiMViknS9E6KEb33rW/jJT34yYgFGQqTW1dUl9nBEo1GwLIvLL788oXcuEAjglVdeUauIipOiIUCKfRBCskMBiU4EAgGcOXNG9mFbagYBw9daiG+JSFUelmV11zpYaFnG9Mput4Nl2ZTZjPx+f86phIGRvwNC4vX09CR8z+x2OxYuXDgiHbicw/u0ZqxAQuqhVnqcIE+kQz0k8qGmAJ3gOA7BYFD24+Qy/l2KIGZ43u10y6PHVi3KkKMPmS6+mesFS63AOhKJUApZjeN5HuFwWPx+lJSUYOrUqQlJLwQmkwlTp07N+/WZpOrVSGc/Sl1n+vv7JWncIERP9FeLI7JL96RbWloq3srLy1FaWip5WTLprdHrhZeCkvzR1taG06dP6/IzjUQiaU3eJ+oQ1hGIV1paikmTJol/GwwG8TxoMBhQWVmp2/NiOlJdH9LtbdTa0OBAIKDLcwghuaAhWyRBuukiWZbFBRdckHCfMPlarewtQpn1dCIvtLTHesYwTMrKzaOPPoqenh58//vfV6hUpFBEo1H09fWJfydr0bfb7QiFQgiHwwgGg3j99deVLqampHMtYBhGc0Nnq6urcerUqYIadqcnhTKESmlUEyKSsdvt+Jd/+ReqYJO8YzabUV5envb2PM/nbVpVog2LFi3C3Llz1S4GkUFraysFI6TgUM1RR2KxmLh6t5rcbjcuvfTSUR9fs2ZNXufKllqqVJIcx1HlVke2bduGn/70p1k/32AwUFBPEvh8PvT29ibcN3wolsvlgt/vT1mRzZfzibCeUzLpvkaWZTMe0kaZuApb/DxXJW+FgH5VOqOViW5Wq3XMx9Q8YefbxSKdC2A6FRGijHA4jIGBAQDS/l6vueYaXHbZZZLtL5lYLIb+/n5Zj0Gyk6ohSo+ZBnOR67wP4bnZ7CPfrjGEaAH9qogsGhoaVFn1Wa8XilQZt1K9rmg0qplgNV8xDAObzZbWtl6vF88//3zWvZnJKltTp07FxIkTs9pfuoTVv4l+MAwzIuWv1+tFe3u7SiVSV7rzRgD9Xi8IyUf0aySymD59OiorK9Uuhm6k07Kp9lA9cnZYTDpDPPr6+nD//fdn3WI91nAUUlii0Sg4jhO/E8O/GyzLwmazJZwfBgYGcPjwYTWKqxihESf+dQv/T3WupGGRJFs0ZEs+lGWLyEa4cBbKj0luwkU2PrVn/P1EGZl8p4VVtId/ZlrH87yuypvPBgYGEI1GR2SCGv75eL1eJYulumQL5xZS5Y2QfENNBEQ28+bNw9KlSxU/bvyci2wmLaolncxMHMdh1qxZWLNmDdasWYPLL7+cWvoUVl5ePmKITDLRaBRXXXUVTp8+rUCppNXR0ZEXE5/zQarW/FgshsHBwbT2lSqBRiHIZJFTQoajHhL50C9Th06ePImqqipYLBa1i5KSWsEAy7LihVfoqdH7hZhlWSxduhSlpaXi+8owDC666CJwHIfjx4/j5MmTlOFMQ4SF2YQhN5kEj8L6CDQ3qLBleg49dOgQWlpa5CmMxsQPX4vFYmNW3KQeBhl/jSGE5I6aVnUoGo2qGjEHg0G8//77aW1bUlKC+fPnZ3UcqVr+c8mmooZYLJb0QseyLOx2O0wmU8L9DocDLpcLBoOBLpAKsdvtafWSAGcXS/zwww+zOs7wCtScOXPwla98Jat9kcIgLIxYKFiWTRmMxG8r5XH10vtOiB7oo4ZGNCUcDqfdAmez2dDQ0JD1saQOSvRwAUk32GQYJiE4KS4uzmjxPpI9s9kMq9WaVlDyxhtv4NSpU1l3vcf/Burr6xUbBhkMBmnYlob19PSI6aUFnZ2dGBoaSrp9PjZWxJ/Pxzq36yVJBMdx8Pl8BTNER49oyJZ8KCAhshNa9tUmTC7OFwzDwG63ixfa+vp6zJo1a8w1Yoh0rFYrnE5nWtt6PB4MDg5mVSlUqyXW4/EUVEu73hw/fhzHjx+H3+8Xb/v370dPT8+oz8mnoCR+rqDBYBgzcUSua5YoJRaLoaurS+1iEKIKVX+hGzZswMKFC+FyuVBZWYkrr7wSR44cSdiG53msXbsWtbW1sNlsWLFiBQ4ePKhSiUk2zGYz1qxZM2KokdyMRmPBTeJ0u91ZD5Ej8nnooYfw3//932oXg+SZ1tZWPP/88+LN5/OpXSTNoZXViZSoh0Q+qv5Kd+7cie9973t46623sH37dkSjUaxatSqhy3njxo245557sGnTJrz77ruorq7GypUrCy7FIcncaMNN9JBlJZNAyu1251XPj54YjUZUVFSkvX06mdQIIWMTAgxK+EBI/lA1IHnxxRfxta99DTNnzsTcuXPxyCOPoK2tDXv27AFw9uJ977334vbbb8fVV1+NWbNmYcuWLfD7/di6dauaRVddd3e37oKyCy+8ECUlJWoXA0D+T0js7+8vmFYVtaX7Pdq7dy/Wrl2b1THy/ftK5DVaogy9i/9NJJvYTr0jRGrUQyIfTf1ShVzqpaWlAIDm5mZ0dnZi1apV4jYWiwXLly/Hrl27VCmjVkQiEd21tBYXFyvaOxG/WNbwi7FQwdPyBStZuT/++GP09fWJjweDwaTPi0QiipSRnOV0OlMGDF6vF83NzQAyH8sfPym3pKQEX//61xX53vr9fgQCAdmPQ3Kn1fOYXIafu5NV2grtPSFEzzTza+V5HrfccgvOP/98zJo1C8DZjCEAUFVVlbBtVVWV+NhwoVAIHo8n4Ua0o7S0NO2JwLlK1aocf0HTYutzspaR1tZW8TvN8zxCodCozw+FQgXTspKJ8ePHY/bs2ZgzZ454rslVfHKBdAjrk2SKYRi4XC78y7/8S8bPzUY4HE4a9BLlGI3GtCrWozWuZPtd05NkAb4Wz+mEkNFpZjD997//fezfvx9vvPHGiMeGn1h4nh/1ZLNhwwasW7dOljKS3M2bNw9WqxWHDh1SpBU/nbkVwkVcqz1OHMdl1dLn8XhQUlKieDIBrfvMZz6DuXPngmEYhEIh3HLLLZKMRU9noTSO4+D1euFyubLaP8/zNG6+wAgNOH6/P6vn5+NQreGGv0ZhUVFCpKbGEKp8b1AQaCIg+cEPfoC///3veO211zBu3Djx/urqagBne0pqamrE+7u6ukb0mghuu+023HLLLeLfHo8H9fX1MpWcZGPatGkoLy/Hyy+/rHZREhiNxrQX2FKKcPKjoQfSeeCBB2TZb2lpKQYGBsZMldvT04MvfOEL+Otf/wqz2azpIYNE27R2rlKSEIAk++1QMEKIPql6JeR5Ht///vfx9NNP45VXXsGECRMSHp8wYQKqq6uxfft28b5wOIydO3diyZIlSfdpsVjgdrsTbkR7ysrKsGbNGrWLMYLW55UAwP79+7F3717xb4/HM6LVXHgNg4ODlApUQcXFxYoMSTQYDHjsscdQW1sr+7EI0SKO4xCNRjXbs03yE01ql4+qta7vfe97+L//+z9s3boVLpcLnZ2d6OzsFCdRMgyDm266CevXr8czzzyDDz/8EF/72tdgt9tx7bXXqll0TfB6vejt7VW7GFkRFvVbvnx5WqtdKyU+GNFSYBIfcHAch87OTrz77rsAErtzBwYG8M477ySUPRwO6y4j2//f3r2HSVXeeQL/nlPXvt+haWjobmgEEVDkIiigKCga1Ghuk9mMk83OmNW44+PMuMm4m5jZjCburJPdNXHGnWeM2X0yMclqkEQNBA0iiAJyFxChL9DQ9LWovtT9nP2j9xzrfj2nzqmq7+d5+oGqOnXOW3Wq6ry/9/J7C1mqseuyLOPb3/42+vr6stq30vpbVVVlms8nGSvR3JFSG95XyNnoPB4PBgcHjS4GkWEMHbL1/PPPAwBuvvnmiPtffPFF/Omf/ikA4PHHH4fH48FDDz2E0dFRrFq1Ctu3b89qDHaxCQaDSSc1F4Lm5mZcddVV6OrqMlVLfvQF3uhx2NEtJH6/H5cvX8aZM2cwd+5ciKKIwcFBXLhwIWKlZmVeQ6lVTIxks9lQVlaWNDvVkSNHMDExobZ+mbkSFQwGMTk5ifLycqOLQnEkW4W8VFpWw+UzSFe+t1q8z8FgkAkkqKQZGpCk8yUWBAFPPvlk1vn7yfyuvvpq+P1+nD9/PuuJm3pSJhMrjLrIR09uDwaDOHHihJpOube3F+fPn495Xvik/UJYFLLQWa3WlAGJQkntnO2Y9/b2dt2zCQYCAbjdbgYkJmJ0AwlNEUWRjT0lhpPa9cP+fsqalj/E1157La6++mrTDkGxWCywWCyGtmQnWr19z5492LVrV9xgJPy5TIGdX6k+K4FAIKvvUPh+/+Zv/garV6/OeB9U2EohlS8RlRZz1v7I9CRJwq9//WtNezTmzp2LjRs3arY/PYiiyF4GSslqtaKpqSnpNn/913+NrVu3Zr1/Kg0ulyvj39lEjRfFzExz/qh4cVK7fvjtJVOpqanBZz7zGQiCYOqLaqFe+FwuV8n8uJkdzwOlMjIykjSNtCAIpp5/RESUrsKsVZHK7/ebMjOHw+HIqhVXEASUlZXpUCJtKa1x+a4MKHMOsmXWAK9YVVdXpxW8ZnNelCGEd955J+65555sikcmJcsyRkdH00ppGy8BB4NdIio0DEgKnCRJmJiYMLoYMSwWS9a9CIIgoKOjw/QrjCsBST6DEi0qGj6fj4FJnjidzpTbZNslr3zu5s+fj6uvvjrj55M5Kb/pHo8n7vc01Xe3lIZ4EOUbh2zphwEJ6SZZOspUz1u+fDkaGhpgtVpNXXk2Ku99Lu/J5OQkFxPLI6vVqutnRFnTZ8aMGbodg/JDkiR4vd6IBBSSJMFms6GyshKVlZWoqKhAVVUVqqqq0gp4iYgKAWdGkm5sNhtEUUwr/Wk869evx/79+9Hd3a1twTSmBF35CpwkSeK48QJSW1uLsbGxrL8HyVgsFkiShOXLl+P73/8+HnjgAc2PQfkzPj4esR6T8l1vb2+P2wvW1dWFI0eO5LOIpsQkD5QvTPurH/aQkK4sFktO6xesWLECK1asYIu+xqIrPkSJOJ1ONDc3G12MknXTTTdh4cKFaW0bCoVKpvJSTAYGBkw5F5Qon9isQDGUln6tMkmJoqgGJT6fL+O1Fwoho1W+e0lkWUYoFIq7oJ4yqTXbIXOkvYqKClgsFl2CQL3PMXvj8mN0dBQ+ny/ivltuuQXV1dVpnwMGI0RUqFhbKQKSJGFoaEizynA6FRxBEHDdddfBbrfHPOZwOOLuUxRF2O32jLvX6+vrsXjxYtOviJvvPPiJKh/KvBYGI+ah92dDEARUVVXhG9/4BoevFCCXyxWRbMJqtWLZsmWoq6uDzWaDxWJBWVlZQWQg1JLyveFvGZkFJ7Xrh9/yIuF2u/P6oVUyYcWr/ChzR+KxWCywWq0ZVZoqKyvR0dGBmTNnZl3efDHLhTOdcsiyzKFwRaS8vBx33nmnaT6DlB6v14vJycmIBiVRFNHR0QFRFGGxWOBwOGIac0ZGRnDlyhUjipwXSq+Q8nlmTx1RceOVi2LoPezIarXCbrdnVHFyOp248cYbC6Kylc8LZ6pzlWxxyUAgwHkkeaRnS68gCOrwPSU7nRaMyiJXKoLBIEZGRiLuE0UxInuW0+mMmwL9zJkzEQk/zJyNMBvhqeP5OSSzYA+Jfsxfu6OiFD6vJBPBYND0F15lwbp8SGdNAjIHu92O+vp6XY8hCAL+5V/+BR0dHZrsr7q6GrW1tZrsi9Izbdo0bNq0KePnmf13MRMcdkhUevitJ0NVVlYCmBq2kM7wofvuuw+///3v4Xa7C6K3xGjxJr1T8bJarRyGVyC8Xi9GR0djKt+JftdCoZChi+Cm08OXrEc2HeE9ffnS2NiIm266Sb29d+9eDAwM5LUMRMQeEjIJu92O8vJylJeXJ524abFYsGrVKrS2thZVi6CeJElKmBBAkqSIRdhIX6Iooq6ujsNPCEB6wzu9Xi/Gx8fh8XjUHs99+/ZhaGgIQPLvtxYymViey7BEI4Zltbe3Y9myZbBYLOrftddei7lz5+a1HFQ4OGRLPwxIiojL5UIgEDDs+KIoxs2wle5zlT9lEmeiC1ttbS3a2toYlGQg2YWeLer5FT0fQBAEzStid955JxYtWpTTPsrLy+POXSDtxPuNGx8fx8cff6zeDoVC6p8kSTh58iSGhoYifuu1rrAoWfqyyXKVTUASPlckG9n0zLS3t2POnDmoqqqKuL+qqgoVFRVZlYOIsseApIhcuXLF0IAEiK1s5bIfJSNXvC785uZmzJkzJ+chAsUg1XuQToWCQYmxtBx+KAgCbr/99rgre2eisrKSAYlOgsFgwu/c5OQkzpw5g8HBwYhAIxAIYGBgAKdPn9b1+6oEyPlKtxudTSsbyu9futeCxsZGdHZ2oqGhIe7j5eXlqKury7o8VLzYQ6IfBiRkWg6HI2GGGWDqQmaz2UwZlJgp01Y627rdbtO9h8VMz89HPpMqUHYmJycxOTmZ8PFAIIA9e/bA4/EgEAggEAhgZGQE7777ru5ly/caRuHZtHIRHpQk+gOmGrtuuummmJ6RcDNnzsSyZctyLlO65S6VCidRMpzUTgWrubkZd955J7Zu3ar56vK5yvfK7eky2/tUqhobGzE6Omp0Mcjktm/fjlAoBFmWIQhCzAR4rRtj8p3dSuvjpXovHA4H7r77bk2PmasLFy6wh5oIDEioAFitVpSXl8dtUXQ6nbjnnnvw+uuvq8PVSrGynW6gIYoibr311oh1DmRZxuuvv46RkRFUVlZGPEaFyWKx4N/8m3+Dzs5OPPXUU0YXh7KUrKKqBCpa0GMek9m0tLRg1apVaW9fU1ODO++8E6+//rqOpaJCY8QQqlLpQWNAQgVBFEWUlZXB4/HEPOZ0OrFmzRocOXIELpdL/fIy5W0sURRht9tjkg+sWbMGsiyjp6cHLpcrqzViyFycTmfWk3Pr6+v5/SkR4XNG8n3MfJk3bx46Ojpgt9vTfo4gCFknaSGizDEgKTITExMxK/0WC4vFArvdHndxxObmZoyPj6OnpyciHWYp9ZbIspzRa1bSXPr9fjQ3NwOYapEVBAFXrlwpys+QmTidTtMuAMdzb7xkw4+0GqZlZDCSr2POmTMHc+bM4SR10kyp9FjkmzmvhpS1sbGxog1IgKn1SpQfg+iL8rx58yCKInw+H8bGxmIeNyI4EQQhbz9emR5HFEXYbDY1pSgAtLa2wuFw4NixY3oUkcKUlZVl1GJLxSFeg0q0RHNDMs0mlUwpBCO1tbVYvHgx0/gSFYDSaT4mTRk5nMPhcCRsWe7o6MCqVasiJpUblcXEiIW+MhEIBODxeFBZWRlRzmnTpmHt2rUcslMElEx0ZB4ulwt+vz/j5yk9oFr2juS7kSYfx1SOYbVasWnTJgYjpCmm/dUPAxLKWHV1tekylYRraGjAZz/72Yj7ZFnWdTVjs5BlOauMLdXV1REBiN1ux5o1a0w7pIhSE0URy5cvx//+3//b6KJQmiRJSqsHpdRkEoi1tbXh85//PO6//35TNwgRUSTWNqggWa1WiKIIr9eb8PHPfOYz2L59u9oaGV5Zz9eiX0YJBoMZv8by8nJ4vV41W5kgCFi2bBlOnjyJsbExvYpa0l5++WV89NFH+Mu//EsA2vY8Kj2Dxfw5LyZaZs1KxYjfP72OKYoi7rzzTvU2ewSJChOvVFSQlItbsiwoFRUVWLFiBerr62MeU3pMSrEl8ujRoxgZGQEw9T4omcuU9zP8PXU6nZicnMxqiAml5vf7cerUKTz33HPqkBwtKqXKfkqlq79QuN3uuOckn8EIoP98OuX3OfpPS0oa31WrVqGiokL903JeliiKWLlyJed6kYpDtvTDgKQI+f3+pKsAFwtlUnayFrFZs2ahra0Nc+bMQUtLi3p/+Bdd76DEyGED8V7bpUuXMDExAWDqffD7/fD7/ZAkCRaLJeY9ra+vh91uV3tOSDtWqxVjY2PYuXMndu7cCb/fn9NFqBQvYoXE6/XGnJd8z3HLx++RlsFI9G/Y7NmzMWfOHLS1taGtrQ2zZ8/OtbhJtba26jZ0NV7yFaJSxSFbRcjj8SAUCuV9LQmjhoY4HA5IkpRwjkhnZyc6OzsxPj4Ol8sVEaxFV9y0fg3K/oxYiVdJARwvFbDf74fP51N7Qzwejzr5UxAEOJ1ONQBZuHAhgsEgzp07h5qaGg4B0oHH48Hf//3fo62tDa2trbDb7RAEIaMhXPHmSfn9fly+fDmjsnDekD7i/QZovdJ6Kpl+powW3tMnCAIqKiqwfPnyohiWJUkSBgcHjS4GZYgLI+qHNQvShM1mMzTVcFlZWcqKVGVlJT7zmc/EzX6lVAyKrbUq0es5duwYTpw4EfFDNzExgbGxsYTzcgKBAIaHh3UpJ035xje+gRMnTqifxWAwmHK1bmWb8GBE6fk7evQoHnnkkbSPb7VaMW3atJxeA8UKBoPqMElFMf7eaC288mez2XDXXXcVRTBCRLHYFEZFQwmIQqFQ3BXdgakWwvvvvx8AcODAAXR1dUU8rlQQiqkXQGlljG4ZPX/+PFwuFzZs2BBxfyAQSDg8S5ZlDA4OorGxkRlsdPLtb38bgiBgwYIFeOaZZwBk3sP2V3/1Vzhz5kzJtKyZmcfjYVKIHE2bNg3r16/XZd9Wq1W9dkxOTjJIJDIIAxIqOhaLBeXl5Qnn0SjBRilVqJXhPOFBiSzLGB8fx86dO3HLLbckDcIsFkvE2i6jo6Ooqqpia6UG7HY76urqMDo6CuDT4OPMmTN47LHH8Oyzz6bcx6FDh/Diiy+qt/v6+koizbXZjY+Px+1xDO8dCf9e6anQGlmUif7z58/H/PnzNS1/voczU/HgkC39MCApUsFgEMPDw2hoaDC6KIYIz8AVPZxF0drailAohJ6enoj7leEuWl4AlbkcRor3oxYMBjE6OoqjR4+qAVpNTQ3a2trUbc6ePYvBwcGI9yMYDGJiYgLl5eXMQKOBeMMNvV4vzpw5g3/8x38EMBUUJgqiL1y4ENPbFz5/yOjPXqlKtKaIMicifKHAfFR0CiEoUd4vJRhpa2vTZHFDi8Wifs8K4X0gKjUMSIqUJElwu92aBySFVLGJbr2PDkqam5thsVjg8XgwMDCg3q9UCrQMSkRRNEXmo0Sv6ZNPPlH/X19fHzEfqLu7O2LIifJaAoEA/H4/AxIdBYNBvPbaawBiV7mOnrMVftvn80WsQWKGz14pUXpFUv1esmL8qfD3SpIkzJgxA/PmzUNVVVXMtsmC80SUDIKptokuC1E49pDohwEJJRSv8hreylQobDYbBEGIm+K3qakJq1atwrZt2yLuV350iq3CkM6FdnR0FPv27Uu6TfTQr1Ia/pZv4e+10qoOIO76OoqhoSEEg0H1c2yxWAzJ9FaKwiewx0t5y8purOhsWk6nEzfeeGPcjGCiKMJut+uSLczhcMDr9caco0T3E5F2CqtmSXkjSVLcSua8efOwZMkSA0qUG2Vl91JYnyUVLS+qXq8Xfr8/aeWYshMvRWtZWRlqa2tTPrexsRHA1PlRKsdWqzXvC/CVGr/fj6GhoaTbKA09+WzsMGJl9kyE/yaVlZVhy5YtCbd1Op15fy0bNmzA/v370dvbm9fjEpUSBiRUMkRRTDrZPZ5gMGj6i7nWZFlGMBhMqydMmZ8wNDSkVoIpd/E+c3V1dSgrKzOoRJTK+Ph4TDatFStWoLm5OeK+HTt2JEytXYrCe+5aWlqwevXquNspv99ERuKQLf2UTi2rBMmyjIsXL+qebcdutxdMtqV4gYXT6UyaZSofq7kXMuV9c7lcJfPDqad4n8P6+no1SUMmHA5HRO9V+JAv0o7L5cL4+Lg6zHP9+vVYv349pk+fDpvNFvFXaENe9RJvMdtEQ4ItFouh61xpLRAIoL+/3+hiEJkKfxmLnNfrzUvmlkLuQRBFMelicHpMci8EmbxeURTVzFtOp5OVrgwJgoDy8nJ1/Zzw972qqgoOhyNhIBHvfuUzKwhCRNIBpUeLgaN2lNS+9fX1aGpqgiiKaGpqAjA1fy1exTvevJJSOSfhjTvhr7mlpQWzZs2K2d5qtapDbouFJEnsJStQ7CHRD2sNVHKsVmvGE3xLMSjJdJFIURTh8XjUALVU3ietVFZWRlRSBEGAzWaLm2VIeRyYet/Dg5JQKKQmcVC2s9vt8Pv9Ec8tlYucHpR1ferq6hAIBGC1WtHW1haRLhuY6j2WJCnhQq0KPXtgzdQjFj55PVxdXR3mzp2LlpaWiPuVHhM2cBAVP37LqeQ4nc6sVuQtxQpcpkGJxWLBxMQEgsEgqqur9SxaUbJYLOrnzGazJZyXE2/Ce/g+woMSQRDQ2NiI/v7+iHVJSvHzrBWfzwev14t169bFfdxMQYAe2aiyFe8312q1YvXq1XEDb4fDwYYNohLBgIQoA5lM+AYiV2QuVNkEJUq2IU50z05lZWXCgC5ZMKJQghLSnsvlQk1NDTZs2BD3cUEQIs5deM9UKYvXKy0IAu677z5TBXBEyXDIln7Y9FAC+vr6MDExYXQxTG/Lli1ppVQFpi6u6fwVejCSLSV4SZUClWLV19cnXAzOarWaqsW71AwNDSUcfuVwOFBVVYXKykoAn2bdSjZXQJKkol8fJtvXWF5ezt4RohLCHpISwLUH0mNEfvt4kpVBzwAn/LjRx1EyjWXy/ihDg1wuF6qrq03x3haC6DkhQHYrU5O2RkdHEQgEYn5LlVS0FotF/YxPTk7G9FBJkoQDBw5ErOJe7L/LyV5jWVkZli9fHvO5FkXR0KFadrsdgiAgEAgYcnwyN/aQ6IcBCeVMGa/OltvcpTMZXM8fRCWIiJ70HH7MTIOSYDCIyclJZt9KQ1lZWcT7q1TWGIwYIxgMwu/3Q5Ik+Hw+ALGpk5Xvi9ILIMtyTGXW7/fj/PnzuHjxYsT9xVzRSDSBHQCqq6sxe/bsmEnsoiga3guY6De4qakJHo8Hg4ODBpSKqPixdkBJpdMqrlx8CykgUV6TmYZUCYKQ8r3Ox4Rk5RhAbIUp0/kkyrbKMBcOw0hMkqSIIYNKpbeQvlfFREnN6na7Yxpcwj/DyYZkeTweBAIBTExM4NixYxH7LmbJgpHy8nLMnj0bixYtinnMYrFEpKk2EyWDWq4BSSgUKvphekTZYEBSIrIZcqNcUIqxAul0OuHz+UxTMcgkTW4+ginlGPEunJkEJeHbeDweBIPBtOfplBJZluFyuVBWVhbR+s5gxDhXrlxRA+no85BusopDhw5hYGAg5n4jhn3kU6L3RhRFXHfddXHXGykV4+PjGB4eNroYlCUO2dIPA5ISMTg4CK/Xqy7YRRQtXqBhtVrjzkFKJygpLy/Hxo0bAQBvv/02RkdHmX2LCsLAwAAkSUo4xHBgYADbtm1LuZ94FYlibh1PFajddddd6pybaBzSSVTa+O2njHR1deHKlStYu3at0UUpGtn0QOVzyJnSYh8vKFHGz0e3ILe0tOCaa65Rn3vDDTcgFAphYGAAhw4dUrNv1dbWshJCphAMBjEyMqL+P525XJkq1VTMoijijjvuQFlZWdzHy8rK2BtIBYE9JPphTYAy4vf7MTk5aXQxioqyeJ1ZhU90jxcAKatWh1corFZrREtoeCYiZbsPP/wQ4+PjKC8vN+248VITCoXgcrlKblid1+vF5OSk2nuh1zDVYq5YpJrbFi+VtcLMv39ElB8MSChj8S4e8SqlZmamVMhKOTKd46NXWeKVI1H2rVTPUygrkJeXl0dMDlWyFwUCAdhsNs1fTymLToDgdDrh9XqT9qrJsozJycmSCkj8fj+8Xq86OV35fdPyu2iWuWp6i/fbYLfbMWPGjITP4feeiAAGJCVFWaAqlyEy8dZIAKAuAphofLDZmGlCO2CeoCRZkJYqw1ey97O8vBx+vx9+v199/ooVKwAAH3/8Mbq7u9UW1GRZi4qVHkN5lKxtyr5ra2sxNDTElcP/P6U3xO12R7wnWjaqhH8nzPR7o7Vkr62yshI33HBDwscdDoceRSLSBYds6YcBSQmZmJiAz+fD7NmzjS4KxZFJVrN0s/xoTckGlmhirlKm6Aq2LMvqpNWJiYmIx+bPn4/58+ert/ft21dSleZAIICxsTGji1FSZFmOyX6lx1pKxZ5NK1wxB1xEpD8GJEQmE17Zj1dJ0jsYkWU5ZU9aouxbit7eXoyOjmLTpk0AplJdlpWVwW63o6amBsBUy3S8569cuRInTpzA6OioBq+G6FPj4+MxwV8mKbczUczZtMIl+x246qqrsGTJkriPiaJYMD3qWrl8+XJMgwwVFvaQ6IcBCWVsfHwcO3fuxK233mp0UTT11ltvwe12Z/Xc8vJy3HTTTUm32bdvX8b7V+bmRN9XiJRhck6nEwBQUVEBAOpQLoUoipg3bx4uXbqECxcuGFLWfPF4POpaF/mUKkvb4OAg6urqCj4DmizLakY3ILbyrNfwyFLJppVqLl6yYK8UJ7IX6m83UT4U34p3lJQkSRgZGcmohT162/D0mNHbFfJQm+Hh4axaNevr63HNNdegrq4u6d/ChQuxdOlSdHR0ZLR/pUUm3y0zqT4jgiAkrVR4vV6cOHEiYn5MIBBQJxArGbfsdjucTqcaqABTaUCbmpowc+ZMbV6MSYVCITVZQHV1tfp+KvM/9JRs/4FAQNdj601ZYf3KlSsIBALqX/j6OYsXL1b/Fi1ahLlz52p2fDNVPPXoUVX2mex1dnR0JJzMbrFYOJmdClL09Thff5l4+umnsWLFClRVVWHatGm49957cfr06ZjX8eSTT6KlpQVlZWW4+eabceLECS3fqowxICkxkiTB5XJl/Jx0L2h+v79kWgeBqYnCbW1taG9vT7ltW1sbFixYgHnz5mHatGl5KF1uUp1zJcFBoqxrgUAAZ86cweXLl9UKrpJVS8msFQwGIQgCHA4HHA5HxPA05ce00CvHiQSDQXXOkCAIqKysjHjc6BZkv99fcPMClGB3cnIS4+PjMSnKKysrMW3aNDQ3N+Oqq65CZ2en+qfF3Dqj5nYlo0cwks5+W1tbEy7EK4piwfe+EZnVrl278PDDD2Pfvn3YsWMHgsEgNm3aFDFc8JlnnsGzzz6L5557Dvv370dzczM2btxo6HxG/iJQxpRueK/XG9GqrfB4PKZe6CrehTSbnh273Y7FixejpaUlo+fV1dXhpptuwm9+8xvT9yilyvqVKPNW+NyXffv2Ye3ataitrY34TCiVRYfDoa5DUl5ejomJiYhKz8TEBGpqagyvoGtJlmW43W7Ds6ol43K5UF1djYqKCtO/90or4ujoaMRnURCEiJb4jo6OiAQK4ZRtcw2AzRaQaKVUMoZlgu8DmdGbb74ZcfvFF1/EtGnTcPDgQaxbtw6yLOOHP/whnnjiCdx3330AgJdeegnTp0/Hz372Mzz44INGFJs9JJQdSZKwdevWhBP0PB6PaVu2JycnIy4kk5OTePXVVzO+uNxxxx0ZByMKm82Gz372s6ZfEFCrFt/du3ejp6cn7mM+nw9jY2PqX0VFhRq41NTU4K677sLQ0FBRXfyHhoYKYtKz2+3OuEc13zweDy5duoT+/v6YwNjhcODuu+9W/xIFIwBQXV2NzZs3611cQ2jxPVb2UUzfw3i8Xi98Pl9a25ZSFjWaYuSQLbfbHfGX7uf0ypUrAKaGlwNAV1cX+vv71aQzwNRv5fr167F3716N37H0sYeEcrJ9+3YsX74cra2tMY8pk5iLLc+81WrFli1b4gYTybLG+Hy+ghzOFj7uPp5UE6QVx48fx6lTp+BwOJImRBgfH497kR8eHkZVVVXcXrlCoczhSqcSY5aFRr1eLwYHBxMOvzHK4OBg3EnV69evVzO5xWOxWNTvafhn7cqVK9i1a1fEau2Z9F6ZvbKu9GSm+5nS6/Uo6b9Lzfnz503bSEeFIbqe9Z3vfAdPPvlk0ufIsozHHnsMN910E6655hoAQH9/PwBg+vTpEdtOnz49YcNhPpTerwIBmEo/2NjYmNPEQlEU4ff7cfz4cUxMTGDBggUx24RCIfh8PtMGJQMDAzh+/HjGz4sORkRRhMPhMO3wG70pQ7eSUZIeBINB7NmzR71/9uzZET+0iSrryirihbQAZ7hAIIDx8fG475MkSRgaGkJDQwMEQYAsy6YZJqWkgR4aGkJjY6OhZQkGg2qPjTIHZ9q0abjqqqvUbWpra+P+rpWVlanzdURRjAl8ZVmG3+/P6jts9mBEEZ21Lzp5QvhjbPnXViF8Pig1I9P+nj9/HtXV1er96dSrvvGNb+Do0aN49913Yx6LvsYYfd1hQFKiPB5Pzl8qpRLqdrvR29sLu90ek0FKuVArFz2LxWJYpV2SpJgeCqX1N11lZWUxr9FiscBqtZqiNVsvsiynPZ8kFUmSIt5zQRCSrs4ePkclGAzC6/VCFMWC6inx+/3wer1JW0i1nk+knLN4Mq0cKZX18fFxlJeX5/077PV6EQwGEQwG1fepo6MDNpsNtbW1MS19CkEQ1MYDq9WqBlfBYDDit8DlcqG3tzeiVyT6tyuZQqq8C4KgBnCCIGBoaEhNjVxIr4Oo1FRXV0cEJKk88sgjeO211/DOO+9g1qxZ6v3Nzc0ApnpKwjPhDQwMJPwtzQcGJJS2eBdnpTV3dHQUx48fR11dHYCpL054BV0Z66hUIvNZoQmfIB0+5lLJxJOJsrIytdtTIYpi0aewDE/dm2zoVjatRwMDAzGrZkdTWm2UCuX4+Lg67MPswz+CwSAmJyfznsAgOtlAIBCIuJ1Ni63b7VY/74Ig6PbeK+dZMT4+HvP+XXXVVepaNuGiGwaU76YSVEXvZ2xsDOfPn8e5c+ey7h0xs7q6uohWT7vdHrFYYXd3d8R77XK5TP+aiCgxWZbxyCOP4NVXX8Uf/vCHmCyg7e3taG5uxo4dO3DdddcBmGoQ27VrF37wgx8YUWQADEhKWjqt3opE21ksFnUct8fjwfbt2wEAGzdujLkQAlMtnXa7Pa+TuZVhQuEkScLRo0c1GS+ZbRdnoc0nUYKNZJ8X5fOgNaWSqXzWlLkYwNREPTMHJWap4A0PD2tSDmXIlN1u120Il8/ni7vWUSLh38HwDH+SJCVMY6l8To8dOxY3IE43ODHDuU3EarXitttuS/pa2tra0NbWpt7+7W9/i4mJCfaWEMVh5JCtdD388MP42c9+hq1bt6KqqkqdM1JTU4OysjIIgoBHH30UTz31lJr2/KmnnkJ5eTm+/OUv6/ES0mLeqzjprq+vD42NjRl1AaZrx44dWLZsGTo7O2MeC2+ljF57QUvJej/efPNNTfJtpztBM7osfr8fr776as7HN0IwGMx4wq9WLBZLzHh9peJqt9tRW1ub9zLFk2jx0GLi9/tx8eJF9XZ9fX3Gw+i8Xm/O75PdbkdZWZl6e3x8PGVQHAgE8Nvf/jan45p93kh5eTm2bNmS8fPuuusu7NmzBxcuXNChVESkt+effx4AcPPNN0fc/+KLL+JP//RPAQCPP/44PB4PHnroIYyOjmLVqlXYvn07qqqq8lzaTzEgKXFaRPrK4njRF+fjx4/D5XJhxYoVCZ8bvhZFrnMw0sliJcsyfvOb38Dj8eR0rFKXKvOWEQKBgDoW3qjJ12NjY2mnYkwmfB2XQpGv9MCiKOKOO+5QgxBlgU1FsiDhzJkzOHfunO5lLHQrVqxAfX09jh49anRRCp4kSbhw4ULB9YhTfIXQQ5LO9oIg4Mknn0yZpSufGJBQzpTJzNGTmv1+Py5duoT33nsPALBy5cqYSpayfXR2m0yycikVwFAolLAy0t/fj66uLjVTU75IkhQzkdnlcuHYsWN5K4Nekg35y2Q4YDbiTaAPv1C4XC712KIo6toTB0zNrQCyX91cEATU1tZqluHEiMpPPnsLlGEHQPIKQiAQwOHDh9Xbbrdbk8aIeIuBmkljYyOuvvrqmPstFkvMfLdQKBTzG6XFsNqTJ0/C7/dj9uzZOe2nGBTCmkNERmNAUuJ8Pp+6snouEmVY8ng86O3tBTB1kWxtbY07rCMUCkVUotJtHZZlOWVu9/7+fnR3d6vlyBclq1d4+UZGRtDb2xsx1EUv8VL6aSl8ons8egUk6WTzCp+4LIpixLA6u92ec7nCAw9ZlpNmCUuHIAg5fwejmbnCnKvu7m7MmjUrbjKJixcvqt85v9+Pvr4+Xcpg5ve3oqIiInsO8Gk2wOghpkpiEq0rzQMDA2hoaIgJSEKhkGFDPnMxNDSE4eFh9bbZg1KiQsOApMQp6yJoXRmK58MPP4TNZkNjYyMsFkvSY+ZawQM+nbdx8uTJlFmcspUstaqyBku4Cxcu4PTp07qUJVr4GgN6XTwLYaViJTW1oqqqKuesaBMTE5otcqYEbsFgULfJ+cqaHcrxCpkkSTh48CAqKiri/oZ89NFHGWfPy/T4hchqtcb93FssFnXIrdavze/3Y3JyMmLdIOV7Y9a1qRLp6uqKaNQq1M8B5c7s17xCxYCENKO0eiVraXv//fcBTPWWJFutO1daTFpNR6bzBfS8iCVrdUzn3BSSXF6PFskMtCJJkloZHBgYwIwZM3IethW9ermy7+jjFrp33nkn4nY+M62Z/f3LtMIkiiLKy8tjAjml9yRbZ8+exejoKDZu3Jj1PoioNBRWnykZKt0WNKvVmrJSNTQ0pFuWqeHhYfz617/WZd+52LlzJz7++GNd9p3uEIh8p8ctlgCIzEvP9VAK1fnz53NukJk7dy42bdqkUYkiBQIBUyYWmZyc5G8WJaWMCsj3XyngrzjB4/Hg4sWLaGlp0Wyf4RNOEwkEAnjjjTfU221tbVi4cGHWx/zggw8wPDyMYDCoWwumy+XCjh070m7x83q9ePvttwFMDSHT44cl0/HY2S5gaEbprg5fiEKhUMaZtqJ7R/x+f0T2q+jHC12+5yEUyvsXbyhpIBCAJEkZDZXS4v3N9DfTKB6PJ+Fvye7duzE6OprxPn0+n27DhYmKDQMSSmtieKaUimKqnpLwIQIXLlzIKWXq5cuX1bkn4RfSdCvf6V583W43Dh06hMWLFydsmT1//jyGh4cRCAQiXqPWi61lMzlUFMW8ZmEKhUIR81m0VEzBVThZlrMauhX9PkiSFNHim05DQSEI/ywV2uRoo+iZ9S7VceOlhJZlGX6/P6+L5EZTkqnIshz3N1GWZRw5cgQjIyNZ9ZzEy7JIRPExICHdZHrxc7vdEZOPtTpmqsq3UknLpLznzp1DXV1dwotpd3e32jKWTSUgnUq2XpV8rSmVa70qRLmOcy8W6Qbd8dIlFxKjPve5JoZIFmDqcQ6CwSD6+vowc+bMiONk0/OmhQsXLqClpSUi0YaS7j3fQ+6Ua0IwGEwYMAQCAVy+fBlnz57NZ9HI5AphHZJCxYCEMqL3+hLJ6DE0J5cF6A4ePKhxaT5lsViSDg8ptIXzwtep0fqzE17BKVXxWngTXTij3/9CGYYEGPu5z+Xzlarcesxb8Pv92LNnDz73uc+p51zJ/Bcv9bqev+mSJGHPnj3YtGkTampqIo7l9XpRVlaWt/MqSVLK+SuhUAjDw8NqEhYi0h8DEsqIURUXrVNSFkIefIvFEvd1F0LZ40lnCF+2ijEoSbc1O1FAMTY2llb621TBr1kU6uc+HUoPgR6pd+MN44teHDZVGnatbN++HWvWrEFra2vE/R6PB06nU/eeknivPZ6zZ8/i8OHDBdXoQ/nBHhL9FOevO2UsFAqhq6vLlBU6LS7SSiYe5a8QKjbJVp7Pdb9G/cDpsQCbwowVVmU4Srqfvf7+/ohFHZOJXky0mBl5bqPn4WQqk14d5fOi1WuVZRm//vWv487hCBcKhXRduyXcvn378OGHH8bc7/V6c5pDmIzf78f4+HhawciePXtw7NixnIOR0dFR9Pf357QPolLCHhJSmTkKT+cCne4QlUKQKGjQ4rWY4TwbNY49HwRBSDovKV7CBeX9iD43iSbbKo8l4nK5MlpcVCmzGRskCvH7G06vXsF0BYNBvP/++1i4cGHMqunJ9PX14cSJE5qXR5IknD9/Hj6fD6tXr454LBQKxQynstlsGfecSJIUEdyk+s0LBoPYtWsXgKm5jMmGlqY7l6gYk20Q6YkBCWUs3/NIMslMlejiX0iVmlQXPCPed60vrtFpSbV6TUYO3cok81N0OcPf24mJCUiSpI7zz/R9Hxsbg9frzeg9CJ/fY6agxOheL6PeC60/xy6XC2fPno1IGnL11VcnfG97e3vR3d2dVarbdHi9XvT39+PEiRNYtGiRen+815ttoJxO72F/fz+GhoYQDAYxNDSkHi9VEMlAo3RxyJZ+GJBQBK/XC6fTmbISoEdAoufF30yVrFTMUtbw8xuv4pwrZZ9aZ+AyMhVwNimYlfdBeQ88Hg8sFkvcicepKENTsnntZgtGjM4iF35ejJDu5zjdHpjBwUEMDg6qtxsaGmCz2eJue/bsWQwODqa170w/a8o+A4EAPvroI0ybNg2CIKCysjLuZ16PIZ7Dw8OQZVkNvMLLZfTnjqhUMSChCP39/WhpacmqMqQFM1WIzCree6RcQNNZtTqbOSRKZVWPSr6yTy2DkkKZqA18+t4q51WZR+BwODIaUx/eylvozJBFzgxDblJ9jnPpQdq7d2/KY6cj0+9ZdIX/nXfeAQAsXboU7e3tup/3QCCAd955R/2+6XE8vX4ryXjsIdEPAxIyjWLMlKS1ZO+NKIpoamrC2rVrk+5j27ZtaU3ujLf/VGXIlvIjr2XLZKIsZXrINTtQdMVyzZo1WLFiBf7X//pfaT3f4/HoNrymFOmVeEFLRg9nU8Sr0Cd6/5KV+ciRIxgaGsINN9ygafnCeb1e/Pa3v9Vt/4r+/v6M5nAREQMSiqO/vx+1tbWora1Nul0wGNT9orhs2bKYFJHJfPzxxzh58mTWx1u/fn3K152uixcvYv/+/ZrsSxAEbNmyJeUQinRa+26//XZIkoTe3l4cOnQoo3LoOa8EyM9nysz+7u/+DuXl5bDb7bDZbPiP//E/AgB+/OMfY2xsLO5z3G43JiYm8llM3ZjhvDMYyV2isqUq86VLl7Bt2zYAwJ133qlZ78Xx48fR1dWlyb7SUSot2qWIPST6YUBCMTLpbtZigvvKlSsjxjKHH7u2tjaj4WMdHR1oampCKBTCe++9l9ZznE4nli9fDgBobGzULBd+S0sL1q1bl9a2Fy5cQHd3NywWS0zmGWAqINFqnQBldfnW1lZYrdaMg6ZkE7K1oHXSBD3nRqQzATYTVVVVKC8vV29XVFQAAO6///6EFeU333wTR48e1awMRlHOt5Hpfc144Vc+X0rZzB6MANmfQ2X1dmBqSFn4fiwWS8rek48//jhinozC7XannU47VfnM+BkhKgYMSCgur9eLiYkJtUKUiCzLWVfIRFHE3LlzMWPGDM2CgMrKSlRWViIUCqV1UayqqkJraytmzJihyfHDOZ3OtPerzBkQRVGXssRTVlaGadOmZf18paKvnH+tKv1azikJL6NeFQktK4cXLlyAw+GIud9isSRsLTZ6roUWjK5k6xVcK/vM5bMcPn/L6PdJC+FzpZIZGBiIuC2KIj755JOkzzl//nzKNVdyxYCESB8MSCguj8cDWZZTBiSKbC64oihiyZIl2RQvLbW1tbhy5UrS9I+1tbVYuHChbmVIV1NTE5qamrJ+viiKSQPD6DS76UjnnOo1hEvroAQwdkHIVARBQE1NDc6ePZsw81Eifr8ftbW1kCQpIq1roTA6q5He2bS0+swZ/T5pJfy7rUjndUmShCNHjuhWLq34/X7T/s5Q7jhkSz8MSCipdCqEydb/MIrFYsGtt96K1157DYFAIO42Zl0ILhupUjVLkpTxRPZMgwGLxaLp+Hutf4S1Lp+WnE4n7rjjjqyeu3jxYixevBiTk5P4zW9+U1CfaWbTSk8xBCLh4r3nxfIa+/r6TP95IjIjBiSUkNfrRW9vL9ra2lJuK8sygsFgzkOvLBZL1nMlvF5vRhXO5cuXo6OjI+V25eXlGV8sfT5fwkAoEzabLe4QnlKh1edKYbVaTddT0tzcjPXr1+e8n7KyMnzuc5/Dr371q4IISsww/ChfAarWn+NCls/sd1oy2+8GGYM9JPrhryNpKt2Lbl1dXcwERZvNlvFwlVxE9+qETyYOl02lKZ3XkqjHIlE59FBRUYG77roLb7zxRtwKQiZZr/Ss3GmZfUuZiG6mCpEWPYzK69q8eTN2795t6uFbRgciQGaf10TlNdNnqJBEv59m+DwQkbEYkFBSkiRhYGAAjY2NaV80lDkbycY8T0xM4NSpU1i2bFnEsZReBSN6BbS8KKazr0TZw/J5cfb5fDh+/HjWLTD5zDqjVfYtLbNuybKMUCiU9bCj2bNnp9VLl4mqqipTt8SbPZtWvHKlGg6ZrlAoVDRzQXJVSO9BOr9zkiRhaGioZFqzibRm3qsWmcb4+DgcDgcqKirSquiE/yAnqkD6/X6cP38+IiAJhUJqMGOxWNKuVCktneHHlWUZPT09eZ8zkOp44a38Zqg0BoNB9PT0pNwuUaUrn93XWq/orlXmrVz2UV9fj+bm5pzLEG3GjBkIBoOm6yUxujKeKptWpuUTRTGt74Ddblez51kslqQ9Yh6PJybDFBkn3eFlsixjfHw8DyUiI3HIln6MrxFRQRgeHoYgCKioqEi7NTg6m0q8C32i1MJerxdlZWVpDWWJtyJuKBTC+++/n/A55eXlEQFBrpUk5TWmWp3XbrcnDUTM2GpopmEpWgUl4alUjVJWVqbbEMXFixfDarXi9OnTplkx2ugJ7KmyaWVbPovFknR+gc1mQ0NDQ9orkA8NDUVUbL1er6m+g3qJ99uY6bzAZHJZG4WI9MeAhNI2NDQEv9+PxsbGtJ8T3ZoQflEIBoN48803cdddd8UdvuTxeHIrcBK33HILKisr1TLlMm8jEAjA5/Olta3f70+4QFcuE/pLifKZ0mLoliiKhmXeWrt2Lerr63Xb/8KFC1FbW4tdu3bpdox0GR2MAMlbNvWcYN/W1hbRE5xKY2MjNm/erN7evXt3SfSYXHPNNWhvb4+4b+fOnRgaGsp53/lo6CmVVuxSxx4S/TAgoYy43W6MjY1BEIS0sm+FC+/6Dq+gvPHGGwCAuXPnarIuyfDwMN56662c95MOrbJpAVO9OuzyTx+zFlG69A469cwcddNNN+HkyZM4efKk5vs2i1tvvRW1tbUx92/YsAFHjhzB6dOnc9q/0huaSVCcyfmcnJxEf39/tsUjIjAgoSwoLQQXLlxAc3NzVpVCZTIw8Okk+LNnz2J8fBxr1qzJqlz79+/HyMgIQqFQRhUDSZLg8Xgy7p3QcjiBEfr6+nD06FGji5ETLSYJaznJPV0bN25EVVVVXo9pFKPnjBjZurhs2TLMmjUrp30IgoCOjg60tLRAkiS8/fbbGpXOPBIFCoIgYMGCBWhra0MgEMhbQ1MmwYjL5TLdXC2iQsSAhLLm9/sxOjoKURThcDjUIVDpiq4o+P1+XL58GYcOHQIALFq0CHa7PeHz3W43zp49q97u7+/PePE/RSgUSjiUKpFCDkaAqfe70C+kWswpMSIgqa6uTjp/JN4QIiVwF0VR/X86x1m8eDGOHTuWfWHjUFIMh4v3HhqZUUsJRtIJSLTK4BatoqIibkOHzWZLmko4utfV6XTC6XRClmUsXboUJ06cKPjfn3Qprz3Xnuh0z3GmQWwwGCyZc0EcsqUnBiSUk7GxMQCRK4XnMh/D7/fj448/BgDU1tYmTI0LACMjI+q2mRoYGIiZs5FpQFKoBgYGEAqF4HK5jC6KJmRZznkdD60ybqUiiiKam5uTVoqUHp9krcbRmewSqaiowLx58zQNSJRgJN5rSBaU5FOqbFrRks1105rVaoXVak14fqMrxNFDXefNm4fR0VH09/eXzG+WVpIlWFEez6TCWei95ERmwoCENOH1etUxtLNnz05aaUnXBx98oFXxYuzfvx/XXXcdOjo6dJuHIEmSKSsM+/bt0zVhgFFyaeFOlSlJq2M7nU6sW7cu6TZWqzXuvsIrsNHJIZL1mAiCALvdrtlnMdH3WrnP6KxEmVYqo58bLZ3zmslrttvtSfcpimJEQ0y8LFsrVqzAgQMH0NfXVxQVYr/fr0sPVTzJzlWma8oMDg5qNoeQCgN7SPTDgIQ019vbCwBoaGhATU2NwaVJ7NChQxgZGUk7HWem+vr6sHfvXl32TZEkSdJktfNcjg/k3rKezYKgSgCTqGJkt9tx33334ZVXXtE9QA4PSoxKOKBFhSG8RyJfx8zU8uXLUV9frw5xLWR/+MMfsGzZspgsW3rJNWiWJCmt9ZuIKH0MSEg3IyMjGB0dVW/Pnj3bdOtsnD9/HqOjoxFpNjOxb98+XLx4Me5j6Y7zJ3PQM1MS5YfWvQWyLJu6B6K9vR21tbUFPdFdeX8PHDiAy5cvx20gGhwcxO7du/NdtLgCgQD6+vqMLgZR0WFAQrqJbjWMTosoCIK6erFRJEnC+Ph41hd0l8vFLnuTUDK35breRbZDj3KdGJ1sknsgEFC/S4IgxGwriiJsNlvSz+LatWvVXkE9GTmBvdQIglCQqa/jnS9JknDp0iXs3btXzbR48OBBuN1u+P1+U/zOTk5OYnR0lI0WJYxDtvRTeL9kVLDirRgd3oMCTLVSV1dX56tIAKYuhKWw8FgpyPWHO7wynU1AkmyIT3V1NTo6OpI+P1GvWnhZEm2X6rU3NTXlrfKaz6Ak0wnshSQQCCT9HLpcLnR3d+evQDkKP1fxzpff70d/fz8++ugjAMDFixezzpyopStXrkCSJPh8vrQXwSWizDAgIUNFByRWqzVuql9BELIaX09kFrW1tViwYEHCxzMZGmTmYURGMGuLtdvtRk1NDSoqKtT70p04r1SAkxkZGYlIfW5WymtO5zwFAgHN01Tnwuv1wuVycQguAWAPiZ4YkJCpBIPBuHMyrFZrWguMmW2OChGVriNHjmBychLLli1T74vXU1xeXh7TO5eqZ6BQ5jsVSjnDhffkJJojSETaYkBCBSEYDKYcmjBjxoyMV1un4hMMBuMuLJgpq9WacSpgZWx8rvNYKD25pGo2k2yGJb3//vumqywXYvARz9jYGIaHh40uBlFJYUBCRePy5cvqGH5RFNHa2mpwiajQKZ+nYqj0AsCDDz6IgwcP4vXXX896H1qlOM5VoQQjXV1duHDhAgRBwJYtWzTZ586dO+F2uzXZVzaKJfCIduHCBYRCoaJ8baQNDtnSDwMSKhrhF5FQKIT+/v6IScZWqxUNDQ1GFK0oKItdZqqQL+6iKGacwSnXbFvZqKmpwZw5c2LuHxsbQ1dXFwBg0aJFaGhoKIpexEIJRoCpHrtgMAhBELBnzx4AwPz589HU1JTxviRJwnvvvRcxpyHXBWjDleI8Cb/fr85lNONCtkSlggEJFa3oYRAWiyUiXarT6Yw7gb7UpBtk5FLxUSqPhVKJzFW811leXo4ZM2bg0qVL6n01NTWoqqrK+XjV1dWYNm1azP0VFRVqutTp06cb3quRq0LOqCXLMi5cuABg6rfoypUrcberqKiISIcenmlKkiR1H+H71bKMpSC8dykQCGBiYsLA0lAhYQ+JfhiQUMkIhUIYGhpSb9fV1cVso6x6XWySBR16z3dItK5HIfzIZru+hdJLEq66uhrz5s2LqAzNmjVL17V4KioqYjJ7VVZWoqmpCYODgxH3m70nK5NMTWbX09OTcKXvpqamiKxcJ06cSLl2TDG8J3qTJEnNTjc8PFwQvz9EpYQBCZWs0dHRmLTDLS0tMb0mhR6gaDHBW6tyhNMzda1W8xwyaQ0TRTFi++hKYigUgsPhwOrVq3MqUzLJyqoEpWvXrsWsWbPwd3/3dzHbhJdZee+MaBFMpBQq3oODg3jjjTeMLkZRCP+8TE5Ocr0pIhMzNCB555138F//63/FwYMHcenSJbz66qu499571cdlWcZ3v/tdvPDCCxgdHcWqVavwox/9CIsWLTKu0FTU4mWtaWtrM0WFvthkk8XKzO655x7s378/ZkhNvni9XnWOQjzr169XF0aUZTllQHjnnXeiuroa58+fx+7duzUtK5HeJElCT09P0fy+kDlwyJZ+DA1IJiYmsHTpUnz1q1/F/fffH/P4M888g2effRY/+clPMH/+fHzve9/Dxo0bcfr0aU3GXROlQ8mQoxAEIa01UcxCyYgjCILp0tGGT5Q3W+t3usGS1WrFnXfeibKyMqxcuRJ1dXVpLex25MgRTceup7pQvv/++xAEATNnzky6H0EQcNddd6GqqgqiKKKlpQUbN27Ejh07UpZBr7THxRS4kn6Gh4cj5g7yM0NUOAwNSDZv3ozNmzfHfUyWZfzwhz/EE088gfvuuw8A8NJLL2H69On42c9+hgcffDCfRaUSFq8lOXz8vdPpLIgAWZbliCw6WmbnyVb4/BIlo5VZpFOZqaqqwuLFi1FdXQ1gauJ6sixWgUAAZ86cAQC4XC51wnk+eDweAMClS5cgCAJWrlyJDz74ANdddx16enowMjKCsrIyLFmyBDU1NWqgaLPZYj7fyTKJZZOJLZls5/FQcZucnIwJ6D0ej65DQYkABrp6Me0ckq6uLvT392PTpk3qfQ6HA+vXr8fevXsZkJChxsbG1P8rKT3DORyOiIxeZhH9Q5oqAMhXwBI+XyH831xlm3433cDI6XSio6Mj5Xb19fWoqqrC5cuXDV/Mbnx8HAAwd+5cXL58GfPmzVPfo2AwiM7OzpjnWCwWzJkzB729vUnnyQDaf2bMFKQaJZMgr9gqS36/P2463snJSfWzTESFz7QBSX9/P4CpVJXhpk+fnjA7CQD4fD74fD71tpGLR1Fp8Hg8auuzor6+HpWVlRH3mWVyuSKXsbB6vQ5lqI8WQ3RyCUbSqQTbbDY4nc609tnZ2Ynm5macOnUq4/LoxWKxYO3atQCAhQsXwmq14uTJk3G3dTgcuOmmm/Dyyy+rLdDxPj8MRvSRbm9moaZFTtar4Xa7eR0n0+AcEv2YNiBRRLcMybKctLXo6aefxne/+129i0WU1MjISEyqzpkzZ8LhcBhUouwkqxDqGVxZLBbTrwa9YMECLF26NK1t8xmIJvqMhTfU6EHrgNvs5z8bSlIBvRTq+3Xp0qW8Dl8kIvMxbUDS3NwMYKqnJDxP/8DAQEyvSbhvfetbeOyxx9Tbbrcbra2t+hWUKE3xhurMmTPHVL0m6QqvLJpxsryZ3HXXXaipqYm532azGRKoBIPBklyRW29G9IAWatA2NjYWsSZUqbQAE1Fipq0Jtbe3o7m5OSKzi9/vx65du7BmzZqEz3M4HKiuro74IzIDpas3/O/ixYvo6+tDX19fwebIVybLh0Ih01WOJEnKuPKtdcXSYrHE7C/fwUg4URRjWuoPHTqEY8eOYXx8HG+++Wbc8ygIAjZu3Ija2lrdy2j2iezKEKrwv3wy6/ctEUmS1N+5vr4+jI6ORvwOEhWKeNfxfPyVAkN7SMbHx/HJJ5+ot7u6unD48GHU19dj9uzZePTRR/HUU0+hs7MTnZ2deOqpp1BeXo4vf/nLBpaaSDvhkzWDwSCGh4cjHnc6nRGrNptV+A9mdCWpEHuA9Mz4ZbVaDX1P4r22xsZGjI2Nobe3N+Gq4LIs4/z58wmHfiXLvJUJJRgx20U4+nXl8xxGny+zvTfRRkdHY8qs95BBIipshgYkBw4cwC233KLeVoZaPfDAA/jJT36Cxx9/HB6PBw899JC6MOL27dsLIsUqUaZCoRCuXLkScZ/f74+o+DgcDlNX8NOpSJq5/NEaGxvVbGmhUChlL9bo6GjEOWxubo7ojTDr8LbW1lZ4vV41i1Z/fz8EQUBNTQ3Ky8vV1/7RRx8lDNRSze/LhJkq3MprytfnNt77a8YATeHz+WLK7HK5TFteolxwUrt+DA1Ibr755qRvtCAIePLJJ/Hkk0/mr1BEJhKdwaulpSUmnbAZK7jhwisrgiCotwshMFm5ciXq6+sBTK2E/tprr6m9WsFgMKbV99ChQ+pcIVEUsWHDhoJ4neFkWcZbb70FALj++uvR0dEBj8eDnTt36n5ssw1B0jOATPZazfY+KOINpRscHIyblpeIKBOmndRORLHiTYxva2srmEpveOtSqjKHL5poBKvVGtHi73Q68YUvfAG/+MUv4Pf7cfLkyYRpcovFwYMHcfDgwbwdz8w9AVoq1JXnh4eHI9ZgIiLSCgMSogLX29ur/t/pdKoZ6swu2doD0ZOFcwlKZFlGMBjUPeVqIRFFETabzVSpVhN9HuIFrnoGqeHHyzXQL9TAA5jKhBU9p82sPTdE+cIhW/rhFZqowIVXErxeLy5duhTxeEVFRcFlm1Myd8UjCILac2FEBWndunU4ePAgRkdHM3qeKIqmGl5n5l618HOsd0Ci1WT1eMOZCqUi4ff7Y4KPYDDIAISI8oYBCVERkSQpZtV4JfuRorq62tSVUSCyIieKYsLJ0srryGerVXNzc8w8nnSZ/X03SvQ8o3RXJs+FFj0h4eUulOFmkiTFrHweDAZjfjeIKBZ7SPTDgISoyHm9Xni9XvW23W5XW+oFQYDdbjeqaCnFq5iGp4UttAp+LmlxM2mtLqT3JXpxP72DkVyzZoWXtRB6EKITL0iSlDC1MxGRURiQEJWY/v5+9f9WqxWzZs2KeNwsldlEC84p62hE9/zoIRgMRsyzUHpGlLVEUh0/FAqpLf5KmbMNADNZCM/IhRfNTItFDM3cExLv83Hx4kXTlpeISMGAhKiEBYNBdHd3q7cbGxsNn2+STqVR2SbZxPhowWAw4wrpzp071RZ8p9OJz33ucwCAW265BcePH8eRI0cSPleSJLz88su4++67035PtVo8TgmilMnr8cpmhgnt0b0jeq16nst+o8toZn19faY4r0TFikO29MOAhIhUIyMjcLlc6u1Zs2bltaU904qjKIqGXCCyXQBQlmX4/X61lyQUCmUUVGVKkiQ1yHE4HLoeM9fPiZbBSDYT1Qsp8FCMjY1FJFfQ87NERKQnBiREpIqulA0ODqr/t9vtqKur0/yYqTIqJSOKYsJsXFrz+/3YvXs3brjhhqwntQNTQYnSip3PQCofx8zk/EVnpUo3aEhU/nSDkGT7MLNAIBAz9yMQCDAIIcoj9pDohwEJESU0MTGh/t/v90es5VFWVqbJ2h65TmJWghk9f7SVOSA9PT2YNm0aZs+enfZze3p6MGfOnIhhW1q2xNfW1kIQhJRpiJMds7+/PyL4zIfwC3smPU7h51l5XqrPUPRrL5QLfPgihIFAIOL7SERUTBiQEFFaAoFARKW1sbERZWVl6u1ceg1yET7JPR/7379/P2w2W0TmsmSUeSbz5s2LeL+Ssdlsab+fs2fPhsViUYdmTU5OpvW8cB9//HHEApv5lu36LOkEs4U0FCs6ccHQ0FDBBE9ERLlgQEJEWRkaGlL/LwgC5syZo97Od4YnLVZ0z8TevXsz2v7IkSNwuVy48cYb03pvZs6ciblz58bcn6w3aPXq1QCAt99+u2Aq4LlIZ85JIQQj4eVzuVy4cuWKgaUhomQ4ZEs/DEiIKGeyLEdk66qqqkJTU1Naz1V6H/KxirlS+TMiJW5vby9GRkZwzz33pNy2u7sbPT09MffPmjULs2bNwr59+xI+14iLV6YT0kOhUMblVD4n6Q4TLIRgJBgMGtozRURkFgxIiEhzExMT8Hq9EAQBLS0tKSursiyrk9PzsUq3EWbMmIHly5envX28CvulS5c0H8bz+9//HsPDw5rtLxPhCQ3S3T4V5XNk5lbFkZERzgchKkDsIdEPAxIi0lx46/Tw8LBakbTb7QnX5Aj/0c12RfN0FivUUmVlJa6++moAwKFDh+KuAbFkyRI4nU5UVVXlvMZLMBjUPKuS2+2OW+7Fixerc158Pl/SNVdyke55zjULl5GCwWBEOm2Px8P1QoiIwjAgISJdhWcKcjgc6tCssrKyuJVMWZbVrFZAZhVWPYKR6upqVFVVxd13dXU15s+fDwA4duxYRCVTEATMnDkT8+bNQ3l5uebl0kpLSwv6+vrg8Xgi7m9ra0NNTQ2AqYAkOuUsMNUbcfHixbyUMxWzDdGanJxUg6NgMAi3221wiYiIzIsBCRHljc/nw+XLlwEAra2tEa3Z4XNIlIqlEpgYNYTLbrejs7MTnZ2dKbd1Op3w+XyQJAmCIKCsrAzr1q0z/fCzG264Ae+8807SuQwOhwPr16+Pud/v92Pbtm2Gt/abJRgJ76EZGBgwRZmISDscsqUfBiREZIjz58+r/3c6nWhpaYnZRvnxz6SXRBRFdVhTrtm3brnllrQXg7zrrruwa9cunD9/HjU1NfjMZz6TcFtl1fREMllN3Wq1pp0QIBAIaFpJttvtuP/++/HKK6/A7/dn9FytVmY3SzACTAVoZukxIiIqJAxIiMhwXq8XXV1dAD5dVyNcMBjMuAIbvb1ZKq2pghFgqrdIEISUPQ82my2j98RmsyEYDMasbr969WpMmzYNBw4cSHtf4bZs2YLdu3djYGAg5bZKoJXJZPZEssnWpbWenh7TfLaISF/sIdEPAxIiMgXlR7e/vx/A1IRxZQ6D8rgWk93T3c8tt9yCysrKjI/V2tqKxYsXR9xnsVh0GboVDAZhtVqzDtSUgMdms2HOnDlwOBzYu3cvbrvttozmvdhsNlx//fX4+OOPcfbs2cxeRJaMCkauXLmC8fHxiHIQEVFuGJAQkakoK44DU63oSmYqpfKZTjCR7HFl0nwqjY2NMT01ydbACAaDaGtrg9VqRX19vXq/EoykGzQoFe10KttKuuTwFvpU63SEl8Nqtao9FmVlZaivr4cgCGhqalLLHK/c8YKBkZGRiAQGegh/nfkORkZHRyHLMjweT8RnlIiIcseAhIhMyefzQZbliFS56VZC41WiBUHIqBI7ODiIhoYG2Gy2iH0kCiyCwWDEavXhZUk3ZS2Qect/9HCh6GMlO7bFYomYq2K1WjFt2rSI58abnxK+boyip6cnrSFb2VJeZ76GR/n9/ojXODo6mpfjEpF5cciWfhiQEFFJiK58p/KHP/wBGzZsQENDg1opDwQCac0ByZQkSZplqgrfT6bzbioqKnDbbbdldcx8BAp6HyN8/6Ojo1y8kIgoTxiQEFFBkWVZnTuhtx07dmDZsmVYuHBh0u2yHcITb4K5liRJUsumVSDl9/tjWuxeffVVNfGAHvIxX8Tn86Gvr0/XYxBRYWMPiX4YkBARxaH0phw5cgRDQ0NYu3YtgKmKa6bZraLFq9TrTQlMwtME5zoXwu/347XXXkMgEEg5Lyc8PW82c2r0cP78+YhkB0REZAwGJEREYcIXtwOmApPLly9jz549uPHGG5M+99ixYxgcHERlZSVWrlwJ4NMsTOGZrYys/CY69sWLF3Hq1Cn19qpVqyKynAGflv3s2bPo6emBLMvwer3q43osYqnlezU+Ph6RIcvoBR2JqLCwh0Q/DEiIiP6/RK3lXq8X58+fx6lTp9DZ2QmLxRL3IjEyMoKLFy+irKwMNTU1mD9/fsQ+lZTDRoo3Ib2vrw/nzp2LWNQvGAzGlFW57Xa7YxYATJYFzejX7Ha7IcsyJicn4fF4DC0LERHFYkBCRBQmUWtUMBjEgQMHUF9fj7q6uojsW9E8Hg8OHjyIxsZG1NbWAjC+Uq6IXtnc5XLh1KlTuHTpUsptganKfXivSLhkLXn5fP3Rc3OGh4dLppWRiKgQ6TMDkYioAImimHIuxPbt2zE4OBhTwY6e6yBJEt544w3TZ2p666234gYj8YRCIbz77rsJFz9MlCY4X5QAyuVyoa+vT/1jMEJEWlB6uvP9l6l33nkHW7ZsQUtLCwRBwK9//euY1/Hkk0+ipaUFZWVluPnmm3HixAmN3qXsMCAhIgojimLKDF5vv/02jh07FnHf1q1bceHCBT2LZihZlvGrX/0KIyMjMY8p75leWbbSIUkSenp60N3dDbfbbVg5iIiMNjExgaVLl+K5556L+/gzzzyDZ599Fs899xz279+P5uZmbNy4UffFbZPhkC0iogzFa7FK1Ir1+9//Htdff33cRRONFAgE8PrrryecU7Fz504sW7YMbW1t6n2yLMcNOvQIROINF4tnYmICw8PDavmIiPRSKJPaN2/ejM2bNyfc3w9/+EM88cQTuO+++wAAL730EqZPn46f/exnePDBB3Mqb7bYQ0JEJSedym4maWn37t2bMIXu5OQk/H5/xmXMh7GxsYQXu8nJSTULlc/nw969e9XHlLS96aTvjc5apmXw4na74XK5EAwGM1r0koio0Ljd7oi/bNO2d3V1ob+/H5s2bVLvczgcWL9+fcTvfL4xICGikpSq1UmZTxL+F250dBRdXV04d+4czp07Z5pJ63oIhULo6uoCkHlAEd2imOnq8cl4vd6c11IhIioEra2tqKmpUf+efvrprPbT398PAJg+fXrE/dOnT1cfMwKHbBERJSEIAkRRjGnpv3DhQtpzRvx+P7xeL5xOp3qfw+FAKBTKS8u+0+mMyIwVCoXSmmzv9XoxNjaWMKsWEVEpMXLI1vnz51FdXa3e73A4ctpvdCObLMspk7roiQEJEVEC4Rmjwlv2M109/NChQxgZGVFXe5dlGUuWLMHly5fR29urbaGj2Gw23HTTTdi1a5e6sOHIyAh+97vfpXzukSNHcOTIEV3LR0REqVVXV0cEJNlqbm4GMNVTMmPGDPX+gYGBmF6TfOKQLSKiDFkslqyHHcmyjF/+8pf4y7/8S2zfvl3jkiW2du1a1NfX4+TJk9ixY0fGz0+VeYyIqBSYPeVvKu3t7Whubo64Dvj9fuzatQtr1qzR/Hjp4hWGiEwrEAigr68PM2fONLoocdXV1WH16tUptzt69CguXbqE119/HbIsIxAIYM2aNRGtU3oJBoN4//33sWPHDly5cgXj4+N5m++SaU8SERHlbnx8HJ988ol6u6urC4cPH0Z9fT1mz56NRx99FE899RQ6OzvR2dmJp556CuXl5fjyl79sWJkZkBCRacmyrEuGqniT1DPV3NyMjo4O1NfXp9z2qquuiukKb25ujphTohdZljE2Nobe3t68Z/vSOxiprKyELMumX3ySiCifDhw4gFtuuUW9/dhjjwEAHnjgAfzkJz/B448/Do/Hg4ceegijo6NYtWoVtm/fjqqqKqOKDEEu8uYrt9uNmpoao4tBRFkSBAHt7e0x92c6hCg81a8WmZ4WLVqExYsX57SPfNq9ezcCgQDGx8cjFg5sbm6GKIqYnJyEy+VK+PxshmzF6yFJZz/KeUrVk+Pz+TA+Po4rV65kXDYiMqcrV65oMldCS0pdsr29Pe8LwEqShK6uLlO+L1piDwkRlYRiTsubDmVC/alTp3D8+HH1/jVr1qC8vBznzp3Dhx9+qN7v8/ly7uGwWCxZD9tK53wpQ9CIiKiwMSAhItKAzWaDKIp5S+WbrQULFmDBggUx93d0dKCjo0O9/dprr0X0pGTLYrGovVOcGE9EhaxQVmovRLw6EBFpQFnVHJgaZma321M+JxgMIhQK6VmsrG3evBkHDhzA2bNnAUyVlQEFERHpgWl/iaioSZKU90q/kkkrGSOCEavVGrG2SjI2mw2LFi3CkiVLdC4VERGVOjZ3EVHR07rLO3rV9kTb5PK4HqKzi6UKiKqrqzF79mz4fD6cPn1a7+IREZkah2zphz0kRGRagiDA4XAYXYwYsixjfHwco6OjWT3fyAn2oijCarXCarXGTX8cfbu2thaLFi3KZxGJiKjEMCAhItOy2WxoaWnRdJ9apPwFgHPnzuGDDz7I6rmBQMAUrV52ux12u119P9Kd+0JEVIryvUq7ET0yRuGQLSIqGVoFI8XE5/Op/5dlOeI2ERFRPvDKTERFK3wxRD0MDw9j69atabVgSZIEn89naIU/EAjA5/OlnHBPRESUT+whISLKQPhCf5Ikwev1Jtw2vOJvpm53SZLSDkoGBwdx7NgxnUtERGR+nNSuHwYkRFQSoidrZyuTi0N478zY2BguXboEAJg3b57hQ8fS6Tm6fPkyurq6cPHixaT7Mfq1EBFRYWNAQkQFKdOKcLrrb+hleHgYH3zwAQRBQHt7e0FU4ru7u/HJJ58kfDx8SFwhvB4iolywh0Q/DEiIqCAVUst89FwW5aKWSa9NvG1TXahSXTyTvX/prLVCRESkBQYkREQ6++CDD3D27FkAU0HCL3/5S9xxxx1oaGhIex/XXHMNpk2bFnHfe++9h8nJyYTP6e/vx1tvvRX3MavVii984QsJg6LXX38dLpcraZmYtYyIiLTAgISISGfRPRWyLGPXrl1YunQp5s6dm9Y+Pv74YzWoUXg8noTbf/TRRzh16lTCXo5AIIBt27Zh48aNKCsri3ncyMUbiYjMiEO29MOmLSIiHR0+fBiDg4Mx93d0dKC+vj7t/fh8PkxOTkb8JbpQtbe3o7KyMmnvCQC43W4cOHAAw8PDKY+fbU9I+NCvUCiU1T6IiKi4MSAhoqKUrzkQoVAIvb29MZVtWZbR09ODs2fPwu12xzyvtbUVdXV1upTJYrFgzpw5uOqqq1Ju29PTg+7u7pRBSbYBSXiLYibnI9X8msnJSQSDwazKRESUDa7Urh8O2SKiopSvH3JJkrB7925s3rwZTqcz4v533303bhnKy8t1nXvxySefoK2tDfX19Th9+nTK7U+ePInJyUksW7ZMvc/Ii6AoiikDyqGhIQYkRERFggEJEZEG3njjjbS2EwQBW7Zsgc1m07U83d3dSdcPidbT04Oenh4dS0REVNg4h0Q/DEiIiLLU2NiIW2+9Nek2r7/+OsbGxvJUokgzZszAZz/7Wbz66quGHJ+IiCgdDEiIyJQqKiqSzrHId7rZ8CFEK1euRF1dHex2e8qejnXr1sUMLbJa8/PTKwhC3o5FRESULV6piMiUrFYr7HZ7wseNWP9CCUiqq6vR1NSU1nP0mrieLlEUcd111wGYmlsyNjaG8vLyiAnvXV1dKdcc0QpXdieiQsUhW/phQEJElIFp06bB4XBk/XylIp6vdT5sNhsWLVoEAPD7/RgZGUF1dbV6n1IWZS0SWZbR39+vW3kkSWIwQkREERiQEBFl4Oabb07ac5OMIAjqEC+/35/3li+lpyTa4sWL1f9LkoRXXnkFPp+vZFrmiIjSwR4S/bCZiogoBUmS0urRsNlscDgcCf+yDWRylUmPjiiK+NznPoeqqiodS0RERPQpBiRERHmWr94HURTVYCQ8MEon5fAdd9yBjo4OvYtIRETEgISISkMoFMpq3kb0An3vvPMORkdHI7bRe02RbEmShEAgEHO/KIqw2WxJy22327Fo0aKI4Vz5JIoi55oQkalwpXb98NeeiEynoqIiYtVzLeTyox7+3P7+fvh8PvW2xWKBKIqwWCyQZTll0BMKhbIuRzYkSYp7zEQVfovFov6/pqZGzRKm9ST8dPbHgISIqDRwUjsRmU5VVRXKy8uNLkZKgiBAFEU1c1QoFFLvi0eSpJg1SfIhGAxGBBqJKGVXApiJiQl1Ucd42bGyzZjFTFtEVIg4qV0/DEiIiLIky3LMkKhEFf9QKGRIMKJIJwiIfj3Hjx/HmTNnEu6PgQUREWmBAQkRUQKhUCjn1ikj0vvGowQaFotFXb09fOgZERGRUdi0RUSUoXfffRcfffRRyu3MEoyEC4VCcSe6ExFRcpzUrh/2kBARZcjr9Sas1IdntlIuJGfOnEEoFMKCBQvyVsZkEmXfCnfw4EFdV2wnIiJSMCAhIoojOt1vuqJbtHp6etDV1QVJklBWVoY5c+ZoWcysJcpyJcsyzp07h+7ubng8nrSekw0lIxnnoBBRoeCkdv0wICEiU7FarRAEwehi5Fz5lmUZY2NjOHjwICYnJwEABw4cQF1dHaqqqhK+RofDkVZGLADweDwJL1Zerxd+vz/uYxaLBRUVFQnL/f7778d9/VoHJPHU1tbC6XSyd4aIqIQwICEiU5k5c2baFXKjpBMwSZKEbdu2RVS8PR4Ptm3bhi984QvqaxQEIWJ/ixYtQl1dXVrHeO+999RgJ3oNlI8++ihinkv4/urq6nDHHXeYsnfiq1/9KpYtW4YHHnhA87VPiIjInBiQEBGFUdLZJrNhwwZMnz49q/3Lsoxf/vKX6u3m5mZs2LBBvX3o0CEAU4sSLl++POm+brjhBvX/w8PDePzxxyOOE+6+++6D3W7HRx99hCNHjuBXv/oVPv/5z+fcGxUMBjVfVX3FihXYs2cPVq9enXCbWbNmYXBwEBMTE5odl4goGQ7Z0g8DEiIifDpnJJ0f/2SLH6Z7LAC4+uqr0dnZGfGYcny3240PPvgAK1eujHjc4/HgyJEjEfc1NDSgoaEhaSClrCbf2dmJ1tZW9XWYkSAIamriRERRNG35iYgoMwxIiIigfcuXKIpYsWJF0n02NTWhqqoq7mOSJEW0/vf19WF8fByBQADj4+MR24ZCIUxOTmLFihU4ePBg0sDE6XTC6XRm+Gr0wYntRFRI2EOiHwYkRERpEEURM2fOBIC0KvSCIGD+/Pk5HVOWZQwODuLixYtwuVwJU/V6PB54PB7Mnz8fAwMDcQMSs1b6owOSTz75BAcPHsTVV1+NW2+9FW+//XbCyflERFQcGJAQEaUgiiKqq6uxfv36vB5XkiQcOXIEr7zyCpYvX47Zs2cn3V4QBKxduzbn4zqdTsiyjEAggGAwmPP+MrFt2zYMDAzgpz/9KX784x9j9erVGBwcLJlWQiKiUsSAhIgoheiJ58VMFEXcd999AID3338fZ86cyWl/FoslrUQBRESFgI0j+jBnHz4RlRxBENDW1qZJyt9gMJj3i4bNZoPD4YDD4YDNZtN031u2bMGsWbM03Wc6li9fjhtvvDHvxw23c+dOw8tARET6Yg8JEZlCupmrotftMIqStSr8dvj/bTZbwjkfmdq7dy8WLFiQdarhcNHlBqYmxcfrwbBYLHlZEyYUCiU8Tnl5ecLH6urqYLVa4XK5dCwdEdEUI3pHSqVHhj0kRFRwzDBBW8m2kmgNDi3LODw8DJ/Pp8m+lMAv/E8JPOIFIDU1NVi4cKEmx04k2wuuzWbTvDeKiIjyz/irOhGRyfn9fgwODkbcJ8syQqFQ0ueZIXBKhyiKsFqt6l+4fAQkqSxYsCDlhH4iIipchXG1JCIy0NDQEHbt2pXx82w2W07Dy2RZht/vN7TLXpIkzYaepTqOIhgMRqy18vjjj+MrX/mK7mUgIkpG6RnP918pYEBCRGRSwWAQv/jFL+DxeCAIQsJhVemy2WxJV0CXJClmaNiFCxewbdu2rI4HRM5PUXpg4gVp4Zm4Dh48iFtuuSXrYxIRUWFhQEJERUOSJN3WzfD5fPjVr34VsUif0oOhh+HhYWzduhUAcPfdd8cMWdJ6snkoFNKtJyQ6YYFZEhMQEWWCPST6YZYtIip5oVAo5Y++LMvwer1x7w8EAppNrj58+DBGRkYQCATU4zkcjoQT5/W8YJ0+fRpnz55NuZ0syzErriuUwCM6C5kkSRAEIe31STZu3AhRFPFf/st/ibi/rKwMjY2NGBoaSms/RERkPgxIiKjkKS326VTuz5w5g/b2dpSXl6v3KT0z8TJuWSyWpAHP2NgY+vr61Ns9PT0YGxtLu9zpBiOJelSUifnRgcG5c+fQ3d2NkZGRlPuWZTlhj0eiif3hQUn4a0gU2LS2tmL58uUx91utVpSVlaUsIxFRrpj2Vz8MSIioKKRqaU82REipAEuSlPLH/9ChQ2hoaIgISICpin10i78oihHBTrTJyUlcunQJBw4cSHrMZNINSsLnjkRPII/n+PHjcLvdGZUlUTCRiBKUKOXP9PlERFQcGJAQUdFIFpRoOedCmYAdXXmOrtw7HI6Eq8aHQiGcOHECp0+fzqksyvyMVAGZUt5QKJRynk2qdMbJ9p8pJeVw+O1k29rtdt3m7RARkTEYkBBRwUi0CGE6Eg2pysbbb7+Na665BkuXLk26XbLFDH/3u9+lNRwqHUoGrmSBRLoT1iVJwssvv5w0wEn0PkqSlHTV9XimTZsWk1L5gw8+wCOPPBKz7dVXX40PP/wQS5YsSXvuCRGRVjhkSz8MSIiIMiTLMk6fPg232421a9fqdhyr1Yq77747b3MkxsbGsHPnzpTBSDLRQ+OUfaXq+Qh33XXX4dVXX83q+EREVHgYkBBRwUtn7ofW/H4/Ll++jL179wIAli9fDrvdnvJ5sizj/fffj1j4L5mqqqq0y5TO0K14Tp8+jeHhYQQCgaTlUoKBZBPVwylD2zJN8etwODBr1qy0t7dYLGhqasLg4GBGxyEiygR7SPTDgISIioJWP9rhledU+/R6vTh37hwAoKGhAXa7HZWVlWhqakr6vHPnzuky5EgQBHR3d2PGjBlwOBwJt/P5fLh48aJ6++zZs2kNH9OydyLb12+xWHDPPffgd7/7HSYnJ9VyVVRUMCAhIipQDEiIqOQkGkYUfTtZul673R7RI3LgwAHIsoz29nY4nU6NS5y+ffv24cYbb0RtbW3CbVwuF/bs2RNzvyiKEdnDfD5fToslKuukAPEnvWcTRFqtVvz93/899u/frwYkRERU2BiQEFFRUiZeK8OGoqVTGVYmZ8fbx1VXXRUxqf2VV16Bx+NBV1cXurq6cix99gRBiJkknu7zKisrcffdd6v3vf/++zhz5gyAyLTBmVLWd4leHDH88UyHdSVLp0xEpAcO2dIPZwcSUUHIJENW+LbRzwuFQjkPl9qwYQMWL14ccd8999yDL33pS3EX78unL33pS6ivr8/oOTfeeCO+9KUv4a677oq4f8WKFbjpdsCJ/gAAEvpJREFUpptyKo/FYkl63gYGBrBkyZKMezu2b98eU14iIipM7CEhooIWPawqXuASvgBftq1N4RPGE63IDgBtbW2YNm1axGNHjx7FhQsXsjpuNuVMFbjV1tZizZo16u3Kysq4qXrD95VpOt9M+P1+/NEf/RGeeeYZXHXVVWk9x26361YeIqJ42EOiHwYkRFQUlCE8ySrjyg+7sm26Msle5XQ6Y+aQzJ8/H9OnT1fLcPjw4byvo7F06VJ12JXT6cy4F0Xvi+KpU6dymhMiiiIaGhrgcrmyWtiRiIiMw4CEiIpGukO6UgUuWmtpaUFLSwuAqYr94OAg+vv7c5owni5RFNHc3IwFCxbAZrPpfrxEBEFIuZr7oUOH0NDQgNmzZ6e1z4ULF+LUqVPqavc1NTUYGxtjQEJEVGA4h4SIKAN2uz2nYEYQBKxfvx6NjY15CYqsVis2bNiQdTAiCIKaTSzbXh1RFNWAJJkf/OAH+NWvfoWRkRGMjo6m3O+f/dmf4Wtf+1pWZSIiypQy7Dfff6WAAQkRFTxRFHWdTxCeZevee++NmSOSjVtvvTXt+RJGam1txWc+8xkA8bONae2f/umfsGrVKmzYsEHX4xARkXkwICEiKlLTpk3Dvffem/N+ysrK8PnPfz7r1L/ZBDJjY2O47rrr0uopCdfS0pLR6vZEROliD4l+OIeEiEwvWeaoTNevSHeY1Pr162PWygByW48j2vz58+F0OnHo0CHN9hnOYrFELN6YLUEQ4HA4cMstt2D//v1wu926DzeTZRlutxsPPvggnnjiiYg1X8KtW7cOzz77LB577DEAnw4PIyKiwsGAhIhML1nlN9OKcapF+Gw2Gzo7OzF9+nTdK91VVVWoq6vT9Rhamj59OubPn4/u7m6MjIxktY/w1MnpOHjwIP7P//k/GBsbi7smSlNTE6699tqsykJERObAgISIClaqrE3RUrWc2+12NDU1YcmSJbkWzRQCgQCGh4fR0NCg2T7nz58PQRAQDAbhdrszfn6m5wwAXnnlFQQCATQ1NRXEvBsiKk5ch0Q/nENCRAUr07kJgiAkXDlcFEXMmjULa9eu1bKISYVCIV1S1CpD3IaGhrBz507N99/Z2Ylly5Zpvt9ktm3bhscffzytbTNdZ4aIiIzFHhIiKhnJhgutWLECbW1teS3P4cOHcfLkSc33u3nzZhw8eBDHjx/XfN+ZCoVCeW/ha2hogNPpxOXLl/N6XCIqbuwh0Q97SIioYCWb7K6QJAnBYDDpNrfeeitmzZpVlK3qgUAAr7zyCvx+v6b7lWVZl96dbDJyzZo1C7t37zZ04UciIspeQQQkP/7xj9He3g6n04nrr78eu3fvNrpIRKQhm82GpqamjJ+X6VyE8O0lSVIr1E6nM++V2Q8//BAXLlzQ/TiyLGNyclLzVra6ujqsXr06aVCSr94Ri8WC6dOn634cIiLSh+kDkpdffhmPPvoonnjiCRw6dAhr167F5s2b0dvba3TRiEgjFosFFRUVcR/TM9NVvnpEZFlWW/4lScKZM2fQ3d2NsbGxhM8pKytDZ2enZmU4e/YsxsbGIsqh/GUTNDidTsycOTPitYVLtd94vSDZlgWY+pz80R/9EWprawFMBbmVlZVZ7YuIKB6uQ6If0wckzz77LL72ta/h3/27f4eFCxfihz/8IVpbW/H8888bXTQi0pkgCJoGJOGV4PDhXmNjY5oOaYq+mCi9Mcrfxx9/DK/Xm3QflZWVWLlypWZlOnPmDCYmJiLKofzleuFT3tfoQCfV9larFdXV1epfZWUlqqqqUF1dHXdxQ6/Xi7Nnz8bdpyAI+M53vqP2lNjtdk2zixERkX5MPand7/fj4MGD+OY3vxlx/6ZNm7B37964z/H5fPD5fOrtK1eu6FpGIsqdUmmPpkUPRrx9R1e8f//732PlypVob2/P+XjK/pPNW1m/fj3efvvtpKuQB4PBlEFLMoFAIOJ1r1u3DjabLeL3MZ5Mhq75fL6IYCQTkiRh2rRpuOGGG+I+HggEsG3btoj7Pv74Y2zevBn79+9P+NkIf92ZlomIjGfmHgFOatePqQOSoaEhhEKhmLHB06dPR39/f9znPP300/jud7+bj+IRkUZ8Ph+6u7sNLYPRx4/W3d2NgwcPara///k//6dm+9JKd3d3wsalZJRhWURUfMbGxlBTU2N0MSjPTB2QKKJbwpKttPytb30Ljz32mHrb5XJhzpw56O3t5Qe8SLndbrS2tuL8+fOorq42ujikA57j4sdzXPx4jotfLudYlmWMjY2hpaVFp9KRmZk6IGlsbITFYonpDRkYGEiYUcXhcMDhcMTcX1NTwx/AIqeMQ6fixXNc/HiOix/PcfHL9hybveGYQ7b0Y+pJ7Xa7Hddffz127NgRcf+OHTuwZs0ag0pFRERERERaMXUPCQA89thj+MpXvoLly5dj9erVeOGFF9Db24uvf/3rRheNiIiIiEoEe0j0Y/qA5Itf/CKGh4fxt3/7t7h06RKuueYavP7665gzZ05az3c4HPjOd74TdxgXFQee4+LHc1z8eI6LH89x8eM5pmwJcqmEXkREREREGXK73er8lnwtqKtQqulXrlwp6rlXpp5DQkREREREmfnxj3+M9vZ2OJ1OXH/99di9e7fRRUqKAQkRERERUZF4+eWX8eijj+KJJ57AoUOHsHbtWmzevBm9vb1GFy0hDtkiIiIiIkogfMiWUTIZsrVq1SosW7YMzz//vHrfwoULce+99+Lpp5/Wq4g5YQ8JEREREZGJud3uiD+fzxd3O7/fj4MHD2LTpk0R92/atAl79+7NR1GzUtQBSaGNn6Pk3nnnHWzZsgUtLS0QBAG//vWvIx6XZRlPPvkkWlpaUFZWhptvvhknTpwwprCUsaeffhorVqxAVVUVpk2bhnvvvRenT5+O2IbnuLA9//zzWLJkibpo2urVq/HGG2+oj/P8Fp+nn34agiDg0UcfVe/jeS58Tz75JARBiPhrbm5WHy+2c2y32yNeX75VVlaitbUVNTU16l+ino6hoSGEQqGYBcSnT58es9C4mRRtQFKI4+couYmJCSxduhTPPfdc3MefeeYZPPvss3juueewf/9+NDc3Y+PGjRgbG8tzSSkbu3btwsMPP4x9+/Zhx44dCAaD2LRpEyYmJtRteI4L26xZs/D9738fBw4cwIEDB7Bhwwbcc889akWF57e47N+/Hy+88AKWLFkScT/Pc3FYtGgRLl26pP4dO3ZMfazYzrHT6URXVxeuXLliyN+FCxdi7vvWt76VtMzR2cBkWc57hrCMyEVq5cqV8te//vWI+xYsWCB/85vfNKhEpCUA8quvvqreliRJbm5ulr///e+r93m9Xrmmpkb+x3/8RwNKSLkaGBiQAci7du2SZZnnuFjV1dXJ//zP/8zzW2TGxsbkzs5OeceOHfL69evlv/iLv5Blmd/jYvGd73xHXrp0adzHeI6N5fP5ZIvFIr/yyisR9/+H//Af5HXr1hlUqtSKsoekUMfPUfa6urrQ398fcc4dDgfWr1/Pc16grly5AgCor68HwHNcbEKhEH7+859jYmICq1ev5vktMg8//DDuuusu3HbbbRH38zwXjzNnzqClpQXt7e340pe+hHPnzgHgOTaa3W7H9ddfjx07dkTcv2PHDqxZs8agUqVm+pXas1Go4+coe8p5jXfOe3p6jCgS5UCWZTz22GO46aabcM011wDgOS4Wx44dw+rVq+H1elFZWYlXX30VV199tVpR4fktfD//+c/x4YcfYv/+/TGP8XtcHFatWoWf/vSnmD9/Pi5fvozvfe97WLNmDU6cOMFzbAKPPfYYvvKVr2D58uVYvXo1XnjhBfT29uLrX/+60UVLqCgDEkXBjZ+jnPGcF4dvfOMbOHr0KN59992Yx3iOC9tVV12Fw4cPw+Vy4f/+3/+LBx54ALt27VIf5/ktbOfPn8df/MVfYPv27XA6nQm343kubJs3b1b/v3jxYqxevRpz587FSy+9hBtuuAEAz7GRvvjFL2J4eBh/+7d/i0uXLuGaa67B66+/jjlz5hhdtISKcshWY2MjLBZLTG/IwMBATMROxUHJfsFzXvgeeeQRvPbaa3j77bcxa9Ys9X6e4+Jgt9sxb948LF++HE8//TSWLl2K//7f/zvPb5E4ePAgBgYGcP3118NqtcJqtWLXrl34H//jf8Bqtarnkue5uFRUVGDx4sU4c+YMv8sm8dBDD6G7uxs+nw8HDx7EunXrjC5SUkUZkBTq+DnKXnt7O5qbmyPOud/vx65du3jOC4Qsy/jGN76BV155BW+99Rba29sjHuc5Lk6yLMPn8/H8Folbb70Vx44dw+HDh9W/5cuX44//+I9x+PBhdHR08DwXIZ/Ph5MnT2LGjBn8LlNWinbIViGOn6PkxsfH8cknn6i3u7q6cPjwYdTX12P27Nl49NFH8dRTT6GzsxOdnZ146qmnUF5eji9/+csGlprS9fDDD+NnP/sZtm7diqqqKrV1raamBmVlZepaBjzHhetv/uZvsHnzZrS2tmJsbAw///nP8Yc//AFvvvkmz2+RqKqqUud9KSoqKtDQ0KDez/Nc+P7qr/4KW7ZswezZszEwMIDvfe97cLvdeOCBB/hdpuwYlt8rD370ox/Jc+bMke12u7xs2TI1fSgVprffflsGEPP3wAMPyLI8lWrwO9/5jtzc3Cw7HA553bp18rFjx4wtNKUt3rkFIL/44ovqNjzHhe3f/tt/q/4mNzU1ybfeequ8fft29XGe3+IUnvZXlnmei8EXv/hFecaMGbLNZpNbWlrk++67Tz5x4oT6OM8xZUqQZVk2KBYiIiIiIqISV5RzSIiIiIiIqDAwICEiIiIiIsMwICEiIiIiIsMwICEiIiIiIsMwICEiIiIiIsMwICEiIiIiIsMwICEiIiIiIsMwICEiIiIiIsMwICEiMqnTp0+jubkZY2Njuh3j2LFjmDVrFiYmJnQ7BhERUTIMSIiIdBIKhbBmzRrcf//9EfdfuXIFra2t+E//6T8lff4TTzyBhx9+GFVVVbqVcfHixVi5ciX+4R/+QbdjEBERJSPIsiwbXQgiomJ15swZXHvttXjhhRfwx3/8xwCAP/mTP8GRI0ewf/9+2O32uM+7cOECOjo6cO7cOcyaNUvXMm7btg1f//rX0dvbC4vFouuxiIiIorGHhIhIR52dnXj66afxyCOP4OLFi9i6dSt+/vOf46WXXkoYjADAL37xCyxdujQmGNmzZw/Wr1+P8vJy1NXV4fbbb8fo6CgA4Oabb8YjjzyCRx99FHV1dZg+fTpeeOEFTExM4Ktf/Sqqqqowd+5cvPHGGxH7vP322zE8PIxdu3Zp/wYQERGlwICEiEhnjzzyCJYuXYo/+ZM/wZ//+Z/j29/+Nq699tqkz3nnnXewfPnyiPsOHz6MW2+9FYsWLcJ7772Hd999F1u2bEEoFFK3eemll9DY2IgPPvgAjzzyCP79v//3+PznP481a9bgww8/xO23346vfOUrmJycVJ9jt9uxdOlS7N69W9PXTURElA4O2SIiyoNTp05h4cKFWLx4MT788ENYrdak21977bW4//778Z//839W7/vyl7+M3t5evPvuu3Gfc/PNNyMUCqmBRSgUQk1NDe677z789Kc/BQD09/djxowZeO+993DDDTeoz73vvvtQU1ODF198MdeXSkRElBH2kBAR5cG//Mu/oLy8HF1dXbhw4ULK7T0eD5xOZ8R9Sg9JMkuWLFH/b7FY0NDQgMWLF6v3TZ8+HQAwMDAQ8byysrKIXhMiIqJ8YUBCRKSz9957D//wD/+ArVu3YvXq1fja176GVJ3TjY2N6twQRVlZWcpj2Wy2iNuCIETcJwgCAECSpIjtRkZG0NTUlHL/REREWmNAQkSkI4/HgwceeAAPPvggbrvtNvzzP/8z9u/fj3/6p39K+rzrrrsOH330UcR9S5Yswc6dO3Up5/Hjx3Hdddfpsm8iIqJkGJAQEenom9/8JiRJwg9+8AMAwOzZs/Hf/tt/w1//9V+ju7s74fNuv/12vPfeexET1r/1rW9h//79eOihh3D06FGcOnUKzz//PIaGhnIqY3d3N/r6+nDbbbfltB8iIqJsMCAhItLJrl278KMf/Qg/+clPUFFRod7/Z3/2Z1izZk3SoVt33nknbDYbfv/736v3zZ8/H9u3b8eRI0ewcuVKrF69Glu3bk05QT6Vf/3Xf8WmTZswZ86cnPZDRESUDWbZIiIyqR//+MfYunUrfve73+l2DJ/Ph87OTvzrv/4rbrzxRt2OQ0RElEhuzWpERKSbP//zP8fo6CjGxsZQVVWlyzF6enrwxBNPMBghIiLDsIeEiIiIiIgMwzkkRERERERkGAYkRERERERkGAYkRERERERkGAYkRERERERkGAYkRERERERkGAYkRERERERkGAYkRERERERkGAYkRERERERkGAYkRERERERkmP8HQPWzX6gUhVsAAAAASUVORK5CYII=", "text/plain": ["<Figure size 1000x1000 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "bb517cd7ad654f7aafce7ce8227790b0", "version_major": 2, "version_minor": 0}, "text/plain": ["VBox(children=(IntSlider(value=256, continuous_update=False, description='Sagittal Slice:', max=511), Output()…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Cell 5: Sagittal View\n", "if phantom_data is not None:\n", "    create_slice_viewer(phantom_data, voxel_dims, 'Sagittal')"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2061f9f20f6b4ce088fc05aa989646b5", "version_major": 2, "version_minor": 0}, "text/plain": ["VBox(children=(IntSlider(value=256, continuous_update=False, description='Coronal Slice:', max=511), Output())…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c26db51d3c1842d2be09f88d181f27f1", "version_major": 2, "version_minor": 0}, "image/png": "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", "text/html": ["\n", "            <div style=\"display: inline-block;\">\n", "                <div class=\"jupyter-widgets widget-label\" style=\"text-align: center;\">\n", "                    Figure\n", "                </div>\n", "                <img src='data:image/png;base64,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' width=1000.0/>\n", "            </div>\n", "        "], "text/plain": ["Canvas(toolbar=Toolbar(toolitems=[('Home', 'Reset original view', 'home', 'home'), ('Back', 'Back to previous …"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Cell 6: Coronal View\n", "if phantom_data is not None:\n", "    create_slice_viewer(phantom_data, voxel_dims,'Coronal')"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Reading BIN file...\n"]}, {"ename": "NameError", "evalue": "name 'read_bin_file' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[8], line 84\u001b[0m\n\u001b[1;32m     81\u001b[0m     compare_data(bin_data, vti_data)\n\u001b[1;32m     83\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;18m__name__\u001b[39m \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m__main__\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[0;32m---> 84\u001b[0m     \u001b[43mmain\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[0;32mIn[8], line 76\u001b[0m, in \u001b[0;36mmain\u001b[0;34m()\u001b[0m\n\u001b[1;32m     73\u001b[0m vti_path \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m/Users/<USER>/Documents/important document backup/Local python code/MRXCAT2/mrxcat-2.0-main/male93/phan93_act_1.vti\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m     75\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mReading BIN file...\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m---> 76\u001b[0m bin_data \u001b[38;5;241m=\u001b[39m \u001b[43mread_bin_file\u001b[49m(bin_path)\n\u001b[1;32m     78\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124mReading VTI file...\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     79\u001b[0m vti_data \u001b[38;5;241m=\u001b[39m read_vti_file(vti_path)\n", "\u001b[0;31mNameError\u001b[0m: name 'read_bin_file' is not defined"]}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import vtk\n", "from vtk.util.numpy_support import vtk_to_numpy\n", "\n", "def read_vti_file(vti_path):\n", "    \"\"\"读取VTI文件数据\"\"\"\n", "    reader = vtk.vtkXMLImageDataReader()\n", "    reader.SetFileName(vti_path)\n", "    reader.Update()\n", "    data = reader.GetOutput()\n", "    dims = data.GetDimensions()\n", "    \n", "    print(f\"VTI dimensions: {dims}\")\n", "    \n", "    point_data = data.GetPointData()\n", "    array_name = point_data.GetArrayName(0)\n", "    vtk_array = point_data.GetArray(array_name)\n", "    numpy_array = vtk_to_numpy(vtk_array)\n", "    \n", "    # 重塑为(490, 256, 256)维度\n", "    reshaped_array = numpy_array.reshape(dims, order='F')\n", "    # 将(256, 256, 490)转换为(490, 256, 256)\n", "    final_array = np.transpose(reshaped_array, (2, 0, 1))\n", "    return final_array\n", "\n", "def read_vti_file(vti_path):\n", "    \"\"\"读取VTI文件数据\"\"\"\n", "    reader = vtk.vtkXMLImageDataReader()\n", "    reader.SetFileName(vti_path)\n", "    reader.Update()\n", "    data = reader.GetOutput()\n", "    dims = data.GetDimensions()\n", "    \n", "    print(f\"VTI dimensions: {dims}\")\n", "    \n", "    point_data = data.GetPointData()\n", "    array_name = point_data.GetArrayName(0)\n", "    vtk_array = point_data.GetArray(array_name)\n", "    numpy_array = vtk_to_numpy(vtk_array)\n", "    \n", "    return numpy_array.reshape(dims, order='F')\n", "\n", "def compare_data(bin_data, vti_data):\n", "    \"\"\"比较两种格式的数据\"\"\"\n", "    print(\"\\nData Shapes:\")\n", "    print(f\"BIN data shape: {bin_data.shape}\")\n", "    print(f\"VTI data shape: {vti_data.shape}\")\n", "    \n", "    # 打印统计信息\n", "    print(\"\\nBIN file statistics:\")\n", "    print(f\"Unique values: {np.unique(bin_data)}\")\n", "    print(f\"Min/Max: {bin_data.min()}/{bin_data.max()}\")\n", "    \n", "    print(\"\\nVTI file statistics:\")\n", "    print(f\"Unique values: {np.unique(vti_data)}\")\n", "    print(f\"Min/Max: {vti_data.min()}/{vti_data.max()}\")\n", "    \n", "    # 显示中间切片的比较图\n", "    mid_slice_bin = bin_data[bin_data.shape[0]//2]\n", "    mid_slice_vti = vti_data[vti_data.shape[0]//2] if vti_data.shape == bin_data.shape \\\n", "                    else vti_data[:,:,vti_data.shape[2]//2]\n", "    \n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(10, 5))\n", "    ax1.imshow(mid_slice_bin)\n", "    ax1.set_title('BIN middle slice')\n", "    ax2.imshow(mid_slice_vti)\n", "    ax2.set_title('VTI middle slice')\n", "    plt.show()\n", "\n", "def main():\n", "    bin_path = \"/Users/<USER>/Documents/important document backup/Local python code/MRXCAT2/mrxcat-2.0-main/male93/phan93_act_1.bin\"\n", "    vti_path = \"/Users/<USER>/Documents/important document backup/Local python code/MRXCAT2/mrxcat-2.0-main/male93/phan93_act_1.vti\"\n", "    \n", "    print(\"Reading BIN file...\")\n", "    bin_data = read_bin_file(bin_path)\n", "    \n", "    print(\"\\nReading VTI file...\")\n", "    vti_data = read_vti_file(vti_path)\n", "    \n", "    compare_data(bin_data, vti_data)\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mrxcat --nrb Xinqi_models/male_pt93.nrb -t1 1000 --t2 50 --pd 0.8 --output outputData/Patient93"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def match_attenuation_to_tissue(pixel_value, attenuation_dict, used_tissues):\n", "    \"\"\"根据精确的衰减系数匹配组织类型，严格按照log顺序\"\"\"\n", "    # 定义特殊匹配规则，按照log文件中的确切值和顺序\n", "    value_to_tissue = {\n", "        0.014285: 'Lung',           # 0.0143\n", "        0.044232: 'Ad<PERSON>ose',        # 0.0442\n", "        0.048075: 'Body',           # 0.0481\n", "        0.049057: 'Red Marrow',     # 0.0491\n", "        0.049223: 'Intestine',      # 0.0492\n", "        0.049683: '<PERSON><PERSON><PERSON>',       # 0.0497\n", "        0.049698: 'Testis',         # 0.0497\n", "        0.049724: 'Bladder',        # 0.0497\n", "        0.050024: 'Muscle',         # 0.0500\n", "        0.050068: '<PERSON><PERSON>',         # 0.0501 (appears before <PERSON> in log)\n", "        0.050104: 'Heart',          # 0.0501\n", "        0.050314: 'Thyroid',        # 0.0503\n", "        0.050503: 'Liver',          # 0.0505 (appears before Blood in log)\n", "        0.050527: 'Blood',          # 0.0505\n", "        0.050555: 'Spleen',         # 0.0506\n", "        0.052237: 'Cartilage',      # 0.0522\n", "        0.057916: 'Spine Bone',     # 0.0579\n", "        0.069917: '<PERSON><PERSON> <PERSON>'        # 0.0699\n", "    }\n", "    \n", "    # 检查是否是已知的精确值\n", "    if pixel_value in value_to_tissue and value_to_tissue[pixel_value] not in used_tissues:\n", "        tissue = value_to_tissue[pixel_value]\n", "        return tissue, abs(pixel_value - attenuation_dict[tissue])\n", "    \n", "    # 如果不是精确值，使用最近似值匹配\n", "    matches = []\n", "    for tissue, coeff in attenuation_dict.items():\n", "        if tissue in used_tissues:\n", "            continue\n", "        diff = abs(pixel_value - coeff)\n", "        matches.append((tissue, coeff, diff))\n", "    \n", "    if not matches:\n", "        return None, float('inf')\n", "    \n", "    matches.sort(key=lambda x: x[2])\n", "    return matches[0][0], matches[0][2]\n", "\n", "# 更新 attenuation_coeffs 字典，确保顺序正确\n", "attenuation_coeffs = {\n", "    'Body': 0.0481,\n", "    'Muscle': 0.0500,\n", "    'Adipose': 0.0442,\n", "    'Lung': 0.0143,\n", "    'Spine Bone': 0.0579,\n", "    'Rib Bone': 0.0699,\n", "    'Liver': 0.0505,    # 在log中先于Blood\n", "    'Blood': 0.0505,\n", "    'Kidney': 0.0501,   # 在log中先于Heart\n", "    'Heart': 0.0501,\n", "    'Lymph': 0.0493,\n", "    'Pancreas': 0.0497,\n", "    'Spleen': 0.0506,\n", "    'Intestine': 0.0492,\n", "    'Skull': 0.0625,\n", "    'Cartilage': 0.0522,\n", "    'Brain': 0.0498,\n", "    'Iodine Blood': 0.0538,\n", "    'Skin': 0.0520,\n", "    'Eye Lens': 0.0506,\n", "    'Ovary': 0.0502,\n", "    'Red Marrow': 0.0491,\n", "    'Yellow Marrow': 0.0469,\n", "    'Testis': 0.0497,\n", "    'Thyroid': 0.0503,\n", "    'Bladder': 0.0497\n", "}\n", "\n", "# Get unique values and their counts\n", "unique_vals = np.unique(data_input)\n", "value_counts = {val: np.sum(data_input == val) for val in unique_vals if val != 0}\n", "\n", "print(\"\\nMatching tissues based on attenuation coefficients:\")\n", "print(\"-\" * 100)\n", "print(f\"{'Value':<15} {'Count':<15} {'Matched Tissue':<20} {'Coeff':<15} {'Diff':<15}\")\n", "print(\"-\" * 100)\n", "\n", "# Sort values by their actual value for precise matching\n", "sorted_values = sorted(value_counts.keys())\n", "used_tissues = set()\n", "tissue_matches = {}\n", "\n", "for value in sorted_values:\n", "    tissue, diff = match_attenuation_to_tissue(value, attenuation_coeffs, used_tissues)\n", "    if tissue:\n", "        tissue_matches[value] = {\n", "            'tissue': tissue,\n", "            'count': value_counts[value],\n", "            'coefficient': attenuation_coeffs[tissue],\n", "            'difference': diff\n", "        }\n", "        used_tissues.add(tissue)\n", "        print(f\"{value:<15.6f} {value_counts[value]:<15,d} {tissue:<20} \"\n", "              f\"{attenuation_coeffs[tissue]:<15.6f} {diff:<15.6f}\")\n", "\n", "# Create tissue labels class with unique matches\n", "class TissueLabels:\n", "    def __init__(self, matches):\n", "        for value, info in matches.items():\n", "            # Convert tissue name to valid Python identifier\n", "            attr_name = info['tissue'].lower().replace(' ', '_').replace('(', '').replace(')', '')\n", "            setattr(self, attr_name, value)\n", "\n", "# Create instance with identified labels\n", "tissue_labels = TissueLabels(tissue_matches)\n", "\n", "print(\"\\nIdentified Tissue Labels:\")\n", "for attr in dir(tissue_labels):\n", "    if not attr.startswith('__'):\n", "        print(f\"{attr}: {getattr(tissue_labels, attr)}\")\n", "\n", "# 添加打印函数来显示完整的匹配结果\n", "def print_tissue_matches(tissue_matches):\n", "    \"\"\"Print detailed information about tissue matches\"\"\"\n", "    print(\"\\nComplete Tissue Matching Results:\")\n", "    print(\"-\" * 120)\n", "    print(f\"{'Value':<15} {'Count':<12} {'Tissue':<20} {'Coeff':<12} {'Diff':<12} {'Volume (ml)':<15}\")\n", "    print(\"-\" * 120)\n", "    \n", "    for value, info in sorted(tissue_matches.items()):\n", "        volume = info['count'] * 0.030518  # ml per voxel\n", "        print(f\"{value:<15.8f} {info['count']:<12,d} {info['tissue']:<20} \"\n", "              f\"{info['coefficient']:<12.8f} {info['difference']:<12.8f} {volume:<15.2f}\")\n", "    \n", "    print(\"\\nIdentified Tissue Labels (Complete List):\")\n", "    print(\"-\" * 80)\n", "    for value, info in sorted(tissue_matches.items()):\n", "        print(f\"{info['tissue']:<20}: {value:.8f}\")\n", "\n", "# 在主处理流程中调用打印函数\n", "tissue_matches = {}\n", "used_tissues = set()\n", "\n", "for value in sorted(value_counts.keys()):\n", "    tissue, diff = match_attenuation_to_tissue(value, attenuation_coeffs, used_tissues)\n", "    if tissue:\n", "        tissue_matches[value] = {\n", "            'tissue': tissue,\n", "            'count': value_counts[value],\n", "            'coefficient': attenuation_coeffs[tissue],\n", "            'difference': diff\n", "        }\n", "        used_tissues.add(tissue)\n", "\n", "# 打印详细结果\n", "print_tissue_matches(tissue_matches)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import matplotlib.colors\n", "from ipywidgets import widgets\n", "from IPython.display import display\n", "from ipywidgets import interact\n", "\n", "# Part 1: Visualization Setup\n", "def create_tissue_visualizations(data_input, tissue_matches):\n", "    \"\"\"Create comprehensive tissue visualizations\"\"\"\n", "    # 为所有可能的组织定义独特的颜色\n", "    tissue_colors = {\n", "        'Lung': '#FF0000',           # 红色\n", "        'Adipose': '#00FF00',        # 绿色\n", "        'Body': '#0000FF',           # 蓝色\n", "        'Muscle': '#FFFF00',         # 黄色\n", "        'Heart': '#FF00FF',          # 洋红\n", "        'Kidney': '#800000',         # 栗色\n", "        'Blood': '#00FFFF',          # 青色\n", "        'Liver': '#FFA500',          # 橙色\n", "        'Lymph': '#98FB98',          # 淡绿色\n", "        'Pancreas': '#DDA0DD',       # 梅红色\n", "        'Spleen': '#800080',         # 紫色\n", "        'Intestine': '#F0E68C',      # 卡其色\n", "        'Skull': '#A0522D',          # 赭色\n", "        'Cartilage': '#008000',      # 深绿色\n", "        'Brain': '#4B0082',          # 靛青色\n", "        'Iodine Blood': '#DC143C',   # 深红色\n", "        'Skin': '#FFB6C1',           # 粉色\n", "        'Eye Lens': '#00CED1',       # 深青色\n", "        'Ovary': '#9400D3',          # 深紫色\n", "        'Red Marrow': '#8B4513',     # 马鞍棕色\n", "        'Yellow Marrow': '#DAA520',   # 金菊色\n", "        'Testis': '#556B2F',         # 橄榄绿\n", "        'Thyroid': '#FF69B4',        # 热粉色\n", "        'Bladder': '#4682B4',        # 钢青色\n", "        'Spine Bone': '#CD853F',     # 秘鲁色\n", "        'Rib Bone': '#8B008B'        # 深洋红色\n", "    }\n", "\n", "    # 找到最佳切片\n", "    slice_sums = np.sum(data_input > 0, axis=(0,1))\n", "    best_slice = np.argmax(slice_sums)\n", "    print(f\"Best slice index: {best_slice}\")\n", "    \n", "    # 创建自定义颜色映射\n", "    def create_custom_image(slice_data):\n", "        \"\"\"Create RGB image with exact value matching\"\"\"\n", "        rgb_image = np.zeros((*slice_data.shape, 3))\n", "        for value, info in tissue_matches.items():\n", "            # 使用精确匹配\n", "            mask = (slice_data == value)\n", "            color = np.array(matplotlib.colors.to_rgb(tissue_colors.get(info['tissue'], '#FFFFFF')))\n", "            rgb_image[mask] = color\n", "        return rgb_image\n", "\n", "    # Part 2: Individual Tissue Views\n", "    n_tissues = len(tissue_matches)\n", "    n_rows = (n_tissues + 2) // 3  # Ensure enough rows for all tissues\n", "    fig, axes = plt.subplots(n_rows, 3, figsize=(15, 5*n_rows))\n", "    axes = axes.ravel()\n", "    \n", "    for idx, (value, info) in enumerate(tissue_matches.items()):\n", "        if idx < len(axes):\n", "            # 使用精确匹配\n", "            mask = (data_input[:,:,best_slice] == value)\n", "            axes[idx].imshow(mask, cmap='gray')\n", "            axes[idx].set_title(f\"{info['tissue']}\\n(Value: {value:.8f})\")\n", "            axes[idx].axis('off')\n", "    \n", "    # Hide empty subplots\n", "    for idx in range(len(tissue_matches), len(axes)):\n", "        axes[idx].axis('off')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Part 3: Combined View with Custom Colors\n", "    plt.figure(figsize=(12, 8))\n", "    \n", "    # 使用自定义颜色创建图像\n", "    rgb_image = create_custom_image(data_input[:,:,best_slice])\n", "    plt.imshow(rgb_image)\n", "    plt.title(f'Combined Tissue Map (Slice {best_slice})')\n", "    \n", "    # 创建图例\n", "    legend_elements = []\n", "    for value, info in tissue_matches.items():\n", "        color = tissue_colors.get(info['tissue'], '#FFFFFF')\n", "        legend_elements.append(plt.Rectangle((0,0), 1, 1, fc=color,\n", "                                          label=f\"{info['tissue']} ({value:.6f})\"))\n", "    \n", "    plt.legend(handles=legend_elements, bbox_to_anchor=(1.05, 1),\n", "              loc='upper left', borderaxespad=0.)\n", "    plt.axis('off')\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Part 4: Interactive Viewer with Custom Colors\n", "    def create_interactive_viewer():\n", "        fig = plt.figure(figsize=(12, 8))\n", "        \n", "        def update(slice_idx):\n", "            plt.clf()\n", "            rgb_image = create_custom_image(data_input[:,:,slice_idx])\n", "            plt.imshow(rgb_image)\n", "            plt.title(f'Combined Tissue Map (Slice {slice_idx})')\n", "            \n", "            legend_elements = []\n", "            slice_values = np.unique(data_input[:,:,slice_idx])\n", "            \n", "            for value, info in tissue_matches.items():\n", "                if np.any(np.abs(slice_values - value) < 0.0001):\n", "                    color = tissue_colors.get(info['tissue'], '#FFFFFF')\n", "                    legend_elements.append(plt.Rectangle((0,0), 1, 1, fc=color,\n", "                                                      label=f\"{info['tissue']} ({value:.6f})\"))\n", "            \n", "            plt.legend(handles=legend_elements, bbox_to_anchor=(1.05, 1),\n", "                      loc='upper left', borderaxespad=0.)\n", "            plt.axis('off')\n", "            plt.tight_layout()\n", "            plt.draw()\n", "        \n", "        interact(update, \n", "                slice_idx=widgets.IntSlider(\n", "                    min=0,\n", "                    max=data_input.shape[2]-1,\n", "                    step=1,\n", "                    value=best_slice,\n", "                    description='Slice:',\n", "                    continuous_update=False\n", "                ))\n", "    \n", "    create_interactive_viewer()\n", "\n", "# Run the visualizations\n", "create_tissue_visualizations(data_input, tissue_matches)\n", "\n", "# Print tissue statistics with exact value matching\n", "print(\"\\nTissue Statistics:\")\n", "print(\"-\" * 100)\n", "print(f\"{'Tissue':<20} {'Value':<15} {'Voxel Count':<15} {'Volume (ml)':<15} {'Expected Value':<15}\")\n", "print(\"-\" * 100)\n", "\n", "for value, info in sorted(tissue_matches.items()):\n", "    # 使用精确匹配而不是误差范围\n", "    exact_mask = (data_input == value)\n", "    voxel_count = np.sum(exact_mask)\n", "    volume = voxel_count * 0.030518  # ml per voxel\n", "    \n", "    print(f\"{info['tissue']:<20} \"\n", "          f\"{value:<15.8f} \"\n", "          f\"{voxel_count:<15,d} \"\n", "          f\"{volume:<15.2f} \"\n", "          f\"{info['coefficient']:<15.6f}\")\n", "\n", "# 添加总计\n", "total_voxels = sum(np.sum(data_input == value) for value in tissue_matches.keys())\n", "total_volume = total_voxels * 0.030518\n", "print(\"-\" * 100)\n", "print(f\"{'Total':<20} {'':<15} {total_voxels:<15,d} {total_volume:<15.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define tissue colors globally\n", "tissue_colors = {\n", "        'Lung': '#FF0000',           # 红色\n", "        'Adipose': '#00FF00',        # 绿色\n", "        'Body': '#0000FF',           # 蓝色\n", "        'Muscle': '#FFFF00',         # 黄色\n", "        'Heart': '#FF00FF',          # 洋红\n", "        'Kidney': '#800000',         # 栗色\n", "        'Blood': '#00FFFF',          # 青色\n", "        'Liver': '#FFA500',          # 橙色\n", "        'Lymph': '#98FB98',          # 淡绿色\n", "        'Pancreas': '#DDA0DD',       # 梅红色\n", "        'Spleen': '#800080',         # 紫色\n", "        'Intestine': '#F0E68C',      # 卡其色\n", "        'Skull': '#A0522D',          # 赭色\n", "        'Cartilage': '#008000',      # 深绿色\n", "        'Brain': '#4B0082',          # 靛青色\n", "        'Iodine Blood': '#DC143C',   # 深红色\n", "        'Skin': '#FFB6C1',           # 粉色\n", "        'Eye Lens': '#00CED1',       # 深青色\n", "        'Ovary': '#9400D3',          # 深紫色\n", "        'Red Marrow': '#8B4513',     # 马鞍棕色\n", "        'Yellow Marrow': '#DAA520',   # 金菊色\n", "        'Testis': '#556B2F',         # 橄榄绿\n", "        'Thyroid': '#FF69B4',        # 热粉色\n", "        'Bladder': '#4682B4',        # 钢青色\n", "        'Spine Bone': '#CD853F',     # 秘鲁色\n", "        'Rib Bone': '#8B008B'        # 深洋红色\n", "    }\n", "\n", "# Axial View\n", "def create_axial_visualization(data_input, tissue_matches):\n", "    \"\"\"Create axial view visualization\"\"\"\n", "    # Calculate best slice for axial view (middle slice)\n", "    n_slices = data_input.shape[0]  # First dimension for axial\n", "    best_slice = n_slices // 2\n", "    print(f\"Axial View - Middle slice index: {best_slice}\")\n", "    \n", "    # Create custom image for axial view\n", "    rgb_image = np.zeros((256, 256, 3))  # For axial view\n", "    for value, info in tissue_matches.items():\n", "        mask = (data_input[best_slice, :, :] == value)\n", "        color = np.array(matplotlib.colors.to_rgb(tissue_colors.get(info['tissue'], '#FFFFFF')))\n", "        rgb_image[mask] = color\n", "    \n", "    # Display axial view\n", "    plt.figure(figsize=(12, 8))\n", "    plt.imshow(rgb_image)\n", "    plt.title(f'Axial View (Slice {best_slice})')\n", "    \n", "    # Create legend\n", "    legend_elements = []\n", "    slice_values = np.unique(data_input[best_slice, :, :])\n", "    for value, info in tissue_matches.items():\n", "        if value in slice_values:\n", "            color = tissue_colors.get(info['tissue'], '#FFFFFF')\n", "            legend_elements.append(plt.Rectangle((0,0), 1, 1, fc=color,\n", "                                              label=f\"{info['tissue']} ({value:.6f})\"))\n", "    \n", "    plt.legend(handles=legend_elements, bbox_to_anchor=(1.05, 1),\n", "              loc='upper left', borderaxespad=0.)\n", "    plt.axis('off')\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Create interactive axial viewer\n", "def create_interactive_axial_viewer(data_input, tissue_matches):\n", "    fig = plt.figure(figsize=(12, 8))\n", "    \n", "    def update(slice_idx):\n", "        plt.clf()\n", "        rgb_image = np.zeros((256, 256, 3))\n", "        for value, info in tissue_matches.items():\n", "            mask = (data_input[slice_idx, :, :] == value)\n", "            color = np.array(matplotlib.colors.to_rgb(tissue_colors.get(info['tissue'], '#FFFFFF')))\n", "            rgb_image[mask] = color\n", "        \n", "        plt.imshow(rgb_image)\n", "        plt.title(f'Axial View (Slice {slice_idx})')\n", "        \n", "        legend_elements = []\n", "        slice_values = np.unique(data_input[slice_idx, :, :])\n", "        for value, info in tissue_matches.items():\n", "            if value in slice_values:\n", "                color = tissue_colors.get(info['tissue'], '#FFFFFF')\n", "                legend_elements.append(plt.Rectangle((0,0), 1, 1, fc=color,\n", "                                                  label=f\"{info['tissue']} ({value:.6f})\"))\n", "        \n", "        plt.legend(handles=legend_elements, bbox_to_anchor=(1.05, 1),\n", "                  loc='upper left', borderaxespad=0.)\n", "        plt.axis('off')\n", "        plt.tight_layout()\n", "        plt.draw()\n", "    \n", "    interact(update, \n", "            slice_idx=widgets.IntSlider(\n", "                min=0,\n", "                max=data_input.shape[0]-1,\n", "                step=1,\n", "                value=data_input.shape[0]//2,\n", "                description='Axial Slice:',\n", "                continuous_update=False\n", "            ))\n", "\n", "# Run axial visualization\n", "create_axial_visualization(data_input, tissue_matches)\n", "create_interactive_axial_viewer(data_input, tissue_matches)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Coronal View\n", "def create_coronal_visualization(data_input, tissue_matches):\n", "    \"\"\"Create coronal view visualization\"\"\"\n", "    # Calculate best slice for coronal view (middle slice)\n", "    n_slices = data_input.shape[1]  # Second dimension for coronal\n", "    best_slice = n_slices // 2\n", "    print(f\"Coronal View - Middle slice index: {best_slice}\")\n", "    \n", "    # Create custom image for coronal view\n", "    rgb_image = np.zeros((data_input.shape[0], data_input.shape[2], 3))\n", "    for value, info in tissue_matches.items():\n", "        mask = (data_input[:, best_slice, :] == value)\n", "        color = np.array(matplotlib.colors.to_rgb(tissue_colors.get(info['tissue'], '#FFFFFF')))\n", "        rgb_image[mask] = color\n", "    \n", "    # Display coronal view\n", "    plt.figure(figsize=(12, 8))\n", "    plt.imshow(rgb_image)\n", "    plt.title(f'Coronal View (Slice {best_slice})')\n", "    \n", "    # Create legend\n", "    legend_elements = []\n", "    slice_values = np.unique(data_input[:, best_slice, :])\n", "    for value, info in tissue_matches.items():\n", "        if value in slice_values:\n", "            color = tissue_colors.get(info['tissue'], '#FFFFFF')\n", "            legend_elements.append(plt.Rectangle((0,0), 1, 1, fc=color,\n", "                                              label=f\"{info['tissue']} ({value:.6f})\"))\n", "    \n", "    plt.legend(handles=legend_elements, bbox_to_anchor=(1.05, 1),\n", "              loc='upper left', borderaxespad=0.)\n", "    plt.axis('off')\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Create interactive coronal viewer\n", "def create_interactive_coronal_viewer(data_input, tissue_matches):\n", "    fig = plt.figure(figsize=(12, 8))\n", "    \n", "    def update(slice_idx):\n", "        plt.clf()\n", "        rgb_image = np.zeros((data_input.shape[0], data_input.shape[2], 3))\n", "        for value, info in tissue_matches.items():\n", "            mask = (data_input[:, slice_idx, :] == value)\n", "            color = np.array(matplotlib.colors.to_rgb(tissue_colors.get(info['tissue'], '#FFFFFF')))\n", "            rgb_image[mask] = color\n", "        \n", "        plt.imshow(rgb_image)\n", "        plt.title(f'Coronal View (Slice {slice_idx})')\n", "        \n", "        legend_elements = []\n", "        slice_values = np.unique(data_input[:, slice_idx, :])\n", "        for value, info in tissue_matches.items():\n", "            if value in slice_values:\n", "                color = tissue_colors.get(info['tissue'], '#FFFFFF')\n", "                legend_elements.append(plt.Rectangle((0,0), 1, 1, fc=color,\n", "                                                  label=f\"{info['tissue']} ({value:.6f})\"))\n", "        \n", "        plt.legend(handles=legend_elements, bbox_to_anchor=(1.05, 1),\n", "                  loc='upper left', borderaxespad=0.)\n", "        plt.axis('off')\n", "        plt.tight_layout()\n", "        plt.draw()\n", "    \n", "    interact(update, \n", "            slice_idx=widgets.IntSlider(\n", "                min=0,\n", "                max=data_input.shape[1]-1,\n", "                step=1,\n", "                value=data_input.shape[1]//2,\n", "                description='Coronal Slice:',\n", "                continuous_update=False\n", "            ))\n", "\n", "# Run coronal visualization\n", "create_coronal_visualization(data_input, tissue_matches)\n", "create_interactive_coronal_viewer(data_input, tissue_matches)"]}], "metadata": {"kernelspec": {"display_name": "mrxcat20", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 2}