{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# B0 Field Synthesis from Tissue Masks\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Block 1: Initial Setup and Data Loading\n", "This block sets up the basic requirements for tissue mask processing:\n", "- Imports necessary libraries\n", "- Defines tissue label classes\n", "- Loads VTI data from the specified path\n", "- Identifies cardiac and other tissue types\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Available arrays in VTI:\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[0m\u001b[31m2025-04-30 14:56:59.229 ( 506.264s) [          2DFEEB]       vtkXMLParser.cxx:379    ERR| vtkXMLDataParser (0x136178150): Error parsing XML in stream at line 14, column 0, byte index 678: junk after document element\u001b[0m\n", "\u001b[0m\u001b[31m2025-04-30 14:56:59.229 ( 506.264s) [          2DFEEB]       vtkXMLReader.cxx:507    ERR| vtkXMLImageDataReader (0x1361a2a40): Error parsing input file.  ReadXMLInformation aborting.\u001b[0m\n", "\u001b[0m\u001b[31m2025-04-30 14:56:59.229 ( 506.264s) [          2DFEEB]       vtkExecutive.cxx:753    ERR| vtkCompositeDataPipeline (0x1361aac50): Algorithm vtkXMLImageDataReader(0x1361a2a40) returned failure for request: vtkInformation (0x136153410)\n", "  Debug: Off\n", "  Modified Time: 261\n", "  Reference Count: 1\n", "  Registered Events: (none)\n", "  Request: REQUEST_INFORMATION\n", "  ALGORITHM_AFTER_FORWARD: 1\n", "  FORWARD_DIRECTION: 0\n", "\n", "\u001b[0m\n"]}, {"ename": "RuntimeError", "evalue": "Cannot find array 'labels', available: []", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mRuntimeError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[5], line 116\u001b[0m\n\u001b[1;32m    114\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m vtk_arr \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    115\u001b[0m     avail \u001b[38;5;241m=\u001b[39m [point_data\u001b[38;5;241m.\u001b[39mGetArrayName(i) \u001b[38;5;28;01mfor\u001b[39;00m i \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(point_data\u001b[38;5;241m.\u001b[39mGetNumberOfArrays())]\n\u001b[0;32m--> 116\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mCannot find array \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00<PERSON>ray_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m, available: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mavail\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    118\u001b[0m data_input \u001b[38;5;241m=\u001b[39m vtk_to_numpy(vtk_arr) \\\n\u001b[1;32m    119\u001b[0m     \u001b[38;5;241m.\u001b[39mreshape(dims[\u001b[38;5;241m0\u001b[39m], dims[\u001b[38;5;241m1\u001b[39m], dims[\u001b[38;5;241m2\u001b[39m], order\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mF\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    120\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mData type: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mdata_input\u001b[38;5;241m.\u001b[39mdtype\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[0;31mRuntimeError\u001b[0m: Cannot find array 'labels', available: []"]}], "source": ["# Part 1: Setup and Data Loading\n", "import numpy as np\n", "import vtk\n", "from vtk.util.numpy_support import vtk_to_numpy\n", "import matplotlib.pyplot as plt\n", "from ipywidgets import interact\n", "import ipywidgets as widgets\n", "import matplotlib.colors as mcolors\n", "import matplotlib.patches as mpatches\n", "\n", "# Define tissue labels\n", "class myLabels:\n", "    def __init__(self, LV_wall, RV_wall, LA_wall, RA_wall, LV_blood, RV_blood, LA_blood, RA_blood,\n", "                 body, muscle, brain, sinus, liver, gall_bladder, right_lung, left_lung,\n", "                 esophagus, esophagus_contents, laryngopharynx, stomach_wall, stomach_contents,\n", "                 pancreas, right_kidney_cortex, right_kidney_medulla, left_kidney_cortex,\n", "                 left_kidney_medulla, adrenal, right_renal_pelvis, left_renal_pelvis,\n", "                 spleen, ribs, cortical_bone, spine, spinal_cord, bone_marrow, arteries,\n", "                 veins, bladder, prostate, ascending_intestine, transverse_intestine,\n", "                 descending_intestine, small_intestine, rectum, seminal_vesicles,\n", "                 vas_deferens, testes, epididymis, ejaculatory_duct, pericardium,\n", "                 cartilage, intestine_air, ureter, urethra, lymph, lymph_abnormal,\n", "                 trachea_bronchi, airways, thyroid, thymus):\n", "        self.LV_wall = LV_wall\n", "        self.RV_wall = RV_wall\n", "        self.LA_wall = LA_wall\n", "        self.RA_wall = RA_wall\n", "        self.LV_blood = LV_blood\n", "        self.RV_blood = RV_blood\n", "        self.LA_blood = LA_blood\n", "        self.RA_blood = RA_blood\n", "        self.body = body\n", "        self.muscle = muscle\n", "        self.brain = brain\n", "        self.sinus = sinus\n", "        self.liver = liver\n", "        self.gall_bladder = gall_bladder\n", "        self.right_lung = right_lung\n", "        self.left_lung = left_lung\n", "        self.esophagus = esophagus\n", "        self.esophagus_contents = esophagus_contents\n", "        self.laryngopharynx = laryngopharynx\n", "        self.stomach_wall = stomach_wall\n", "        self.stomach_contents = stomach_contents\n", "        self.pancreas = pancreas\n", "        self.right_kidney_cortex = right_kidney_cortex\n", "        self.right_kidney_medulla = right_kidney_medulla\n", "        self.left_kidney_cortex = left_kidney_cortex\n", "        self.left_kidney_medulla = left_kidney_medulla\n", "        self.adrenal = adrenal\n", "        self.right_renal_pelvis = right_renal_pelvis\n", "        self.left_renal_pelvis = left_renal_pelvis\n", "        self.spleen = spleen\n", "        self.ribs = ribs\n", "        self.cortical_bone = cortical_bone\n", "        self.spine = spine\n", "        self.spinal_cord = spinal_cord\n", "        self.bone_marrow = bone_marrow\n", "        self.arteries = arteries\n", "        self.veins = veins\n", "        self.bladder = bladder\n", "        self.prostate = prostate\n", "        self.ascending_intestine = ascending_intestine\n", "        self.transverse_intestine = transverse_intestine\n", "        self.descending_intestine = descending_intestine\n", "        self.small_intestine = small_intestine\n", "        self.rectum = rectum\n", "        self.seminal_vesicles = seminal_vesicles\n", "        self.vas_deferens = vas_deferens\n", "        self.testes = testes\n", "        self.epididymis = epididymis\n", "        self.ejaculatory_duct = ejaculatory_duct\n", "        self.Peri = pericardium\n", "        self.cartilage = cartilage\n", "        self.intestine_air = intestine_air\n", "        self.ureter = ureter\n", "        self.urethra = urethra\n", "        self.lymph = lymph\n", "        self.lymph_abnormal = lymph_abnormal\n", "        self.trachea_bronchi = trachea_bronchi\n", "        self.airways = airways\n", "        self.thyroid = thyroid\n", "        self.thymus = thymus\n", "\n", "maskLabels = myLabels(1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, \n", "                     17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, \n", "                     31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, \n", "                     45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, \n", "                     65, 66)\n", "\n", "# Load data\n", "# Load data\n", "input_vti = \"/Users/<USER>/Documents/important document backup/Local python code/MRXCAT2/mrxcat-2.0-main/female50/singleframe_act_1_fixed.vti\" # Replace with your actual path\n", "\n", "# Create VTK reader and read the file\n", "read_img = vtk.vtkXMLImageDataReader()\n", "read_img.SetFileName(input_vti)\n", "read_img.Update()\n", "img_ref = read_img.GetOutput()\n", "\n", "# Part 2: Process Data and Find Best Slice\n", "dims = img_ref.GetDimensions()\n", "# data_input = vtk_to_numpy(img_ref.GetPointData().GetArray('labels')).reshape(dims[0], dims[1], dims[2], order=\"F\")\n", "\n", "# —— 新增：列出所有可用的 point data arrays —— \n", "point_data = img_ref.GetPointData()\n", "print(\"Available arrays in VTI:\")\n", "for i in range(point_data.GetNumberOfArrays()):\n", "    print(f\"  {i}: {point_data.GetArrayName(i)}\")\n", "\n", "# 然后用正确的数组名（比如实际可能是 'Label' 或者其他）替换下面的 'labels'\n", "array_name = 'labels'  # ← 替换为上面打印出的正确名字\n", "vtk_arr = point_data.GetArray(array_name)\n", "if vtk_arr is None:\n", "    avail = [point_data.GetArrayName(i) for i in range(point_data.GetNumberOfArrays())]\n", "    raise RuntimeError(f\"Cannot find array '{array_name}', available: {avail}\")\n", "\n", "data_input = vtk_to_numpy(vtk_arr) \\\n", "    .reshape(dims[0], dims[1], dims[2], order=\"F\")\n", "print(f\"Data type: {data_input.dtype}\")\n", "\n", "print(f\"Data type: {data_input.dtype}\")\n", "slice_sums = np.sum(data_input > 0, axis=(0,1))\n", "best_slice = np.argmax(slice_sums)\n", "print(f\"Best slice index: {best_slice}\")\n", "\n", "# Define the tissue types to display with their corresponding label values\n", "tissue_types = {\n", "    'LV Wall': maskLabels.LV_wall,\n", "    'RV Wall': maskLabels.RV_wall,\n", "    'LA Wall': mask<PERSON><PERSON><PERSON>.LA_wall,  \n", "    'RA Wall': <PERSON><PERSON><PERSON><PERSON>.RA_wall,\n", "    'LV Blood': maskLabels.LV_blood,\n", "    'RV Blood': maskLabels.RV_blood,\n", "    'LA Blood': mask<PERSON><PERSON><PERSON>.LA_blood,\n", "    'RA Blood': <PERSON><PERSON><PERSON><PERSON>.RA_blood,\n", "    'Pericardium': maskLabels.Peri,\n", "    'Arteries': maskLabels.arteries,\n", "    'veins': mask<PERSON>abels.veins,\n", "    'Right Lung': <PERSON><PERSON><PERSON><PERSON>.right_lung,\n", "    'Left Lung': <PERSON><PERSON><PERSON><PERSON>.left_lung,\n", "    'Liver': maskLabels.liver,\n", "    'Spleen': maskLabels.spleen,\n", "    # 'Stomach Wall': mask<PERSON><PERSON><PERSON>.stomach_wall,\n", "    'Pancreas': maskLabels.pancreas,\n", "    'Spine': maskL<PERSON><PERSON>.spine,\n", "    'Spinal Cord': mask<PERSON><PERSON><PERSON>.spinal_cord,\n", "    'muscle': maskLabels.muscle,\n", "    'body': mask<PERSON><PERSON><PERSON>.body,\n", "    'coritcal bone': maskLabels.cortical_bone,\n", "    'ribs': mask<PERSON><PERSON><PERSON>.ribs,\n", "    'bone marrow': mask<PERSON><PERSON><PERSON>.bone_marrow,\n", "}\n", "\n", "# Part 3: Display Static Best Slice View\n", "n_tissues = len(tissue_types)\n", "n_cols = 3\n", "n_rows = (n_tissues + n_cols - 1) // n_cols  # Ceiling division to ensure enough rows\n", "\n", "fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 5*n_rows))\n", "axes = axes.ravel()\n", "\n", "for idx, (name, label) in enumerate(tissue_types.items()):\n", "    mask = (data_input[:,:,best_slice] == label)\n", "    axes[idx].imshow(mask, cmap='gray')\n", "    axes[idx].set_title(f'{name} (Label {label})\\nSlice {best_slice}')\n", "    axes[idx].axis('off')\n", "\n", "# Hide empty subplots\n", "for idx in range(n_tissues, len(axes)):\n", "    axes[idx].axis('off')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "\n", "# Part 4: Show Single Best Slice\n", "# Modified Part 4: Show Single Best Slice (only listed tissues)\n", "plt.figure(figsize=(8, 8))\n", "\n", "# Create a modified slice where unlisted tissues are set to 0\n", "slice_data = data_input[:,:,best_slice].copy()\n", "listed_labels = list(tissue_types.values())\n", "\n", "# Set all unlisted tissues to 0 (background)\n", "mask = np.isin(slice_data, listed_labels)\n", "filtered_slice = np.where(mask, slice_data, 0)\n", "\n", "# Create a custom colormap with distinct colors\n", "# Get a list of distinct colors from matplotlib\n", "distinct_colors = list(mcolors.TABLEAU_COLORS.values())\n", "# Add more colors if needed\n", "distinct_colors += list(mcolors.CSS4_COLORS.values())[:len(tissue_types) - len(distinct_colors)]\n", "\n", "# Create a mapping from label values to color indices\n", "label_to_idx = {label: i for i, label in enumerate(listed_labels)}\n", "\n", "# Create a custom normalized array where each tissue gets a distinct value \n", "# between 0 and 1 based on its position in the tissue_types dictionary\n", "custom_colored_slice = np.zeros_like(filtered_slice, dtype=float)\n", "for label in listed_labels:\n", "    if np.any(filtered_slice == label):\n", "        custom_colored_slice[filtered_slice == label] = label_to_idx[label] + 1  # +1 to keep 0 as background\n", "\n", "# Normalize to 0-1 range for colormap\n", "if len(label_to_idx) > 0:  # Check to avoid division by zero\n", "    custom_colored_slice = custom_colored_slice / (len(label_to_idx) + 1)\n", "\n", "# Create custom colormap with the distinct colors\n", "n_colors = len(tissue_types) + 1  # +1 for background\n", "cmap_colors = [(0,0,0,0)]  # Start with transparent for background (0)\n", "for i in range(len(distinct_colors[:len(tissue_types)])):\n", "    cmap_colors.append(mcolors.to_rgba(distinct_colors[i]))\n", "custom_cmap = mcolors.ListedColormap(cmap_colors)\n", "\n", "# Plot with the custom colormap\n", "img = plt.imshow(custom_colored_slice, cmap=custom_cmap, vmin=0, vmax=1)\n", "plt.title(f'Combined Tissue Labels (Slice {best_slice}) - Listed Tissues Only')\n", "\n", "# Create legend with the actual distinct colors\n", "legend_patches = []\n", "for i, (name, label) in enumerate(tissue_types.items()):\n", "    if np.any(filtered_slice == label):  # Check if this tissue exists in the slice\n", "        color = distinct_colors[i]\n", "        legend_patches.append(mpatches.Patch(color=color, label=f'{name} ({label})'))\n", "\n", "plt.legend(handles=legend_patches, bbox_to_anchor=(1.05, 1), loc='upper left')\n", "plt.axis('off')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Part 5: Interactive Slice Viewer\n", "#Modified Interactive Slice Viewer (only listed tissues)\n", "def create_filtered_interactive_viewer_with_distinct_colors():\n", "    # Create one large subplot for the combined view\n", "    fig = plt.figure(figsize=(10, 8))\n", "    \n", "    # Predefine distinct colors\n", "    distinct_colors = list(mcolors.TABLEAU_COLORS.values())\n", "    distinct_colors += list(mcolors.CSS4_COLORS.values())[:len(tissue_types) - len(distinct_colors)]\n", "    \n", "    # Create mapping from labels to indices\n", "    listed_labels = list(tissue_types.values())\n", "    label_to_idx = {label: i for i, label in enumerate(listed_labels)}\n", "    \n", "    # Create custom colormap with the distinct colors\n", "    n_colors = len(tissue_types) + 1  # +1 for background\n", "    cmap_colors = [(0,0,0,0)]  # Start with transparent for background (0)\n", "    for i in range(len(distinct_colors[:len(tissue_types)])):\n", "        cmap_colors.append(mcolors.to_rgba(distinct_colors[i]))\n", "    custom_cmap = mcolors.ListedColormap(cmap_colors)\n", "    \n", "    def update(slice_idx):\n", "        plt.clf()  # Clear the current figure\n", "        \n", "        # Get the slice data\n", "        original_slice = data_input[:,:,slice_idx].copy()\n", "        \n", "        # Filter out unlisted tissues (set to 0)\n", "        mask = np.isin(original_slice, listed_labels)\n", "        filtered_slice = np.where(mask, original_slice, 0)\n", "        \n", "        # Create a custom normalized array for distinct colors\n", "        custom_colored_slice = np.zeros_like(filtered_slice, dtype=float)\n", "        for label in listed_labels:\n", "            if np.any(filtered_slice == label):\n", "                custom_colored_slice[filtered_slice == label] = label_to_idx[label] + 1\n", "        \n", "        # Normalize to 0-1 range for colormap\n", "        if len(label_to_idx) > 0:\n", "            custom_colored_slice = custom_colored_slice / (len(label_to_idx) + 1)\n", "        \n", "        # Plot with the custom colormap\n", "        img = plt.imshow(custom_colored_slice, cmap=custom_cmap, vmin=0, vmax=1)\n", "        plt.title(f'Combined Tissue Labels (Slice {slice_idx}) - Listed Tissues Only')\n", "        \n", "        # Create legend with the actual distinct colors\n", "        legend_patches = []\n", "        for i, (name, label) in enumerate(tissue_types.items()):\n", "            if np.any(filtered_slice == label):  # Check if this tissue exists in the slice\n", "                color = distinct_colors[i]\n", "                legend_patches.append(mpatches.Patch(color=color, label=f'{name} ({label})'))\n", "        \n", "        plt.legend(handles=legend_patches, bbox_to_anchor=(1.05, 1), loc='upper left')\n", "        plt.axis('off')\n", "        plt.tight_layout()\n", "        plt.draw()\n", "    \n", "    interact(update, \n", "            slice_idx=widgets.IntSlider(\n", "                min=0, \n", "                max=data_input.shape[2]-1, \n", "                step=1, \n", "                value=best_slice,\n", "                description='Slice:',\n", "                continuous_update=False\n", "            ))\n", "\n", "# Use the modified viewer\n", "create_filtered_interactive_viewer_with_distinct_colors()\n", "\n", "# Part 6: Print Mask Information\n", "print(\"\\nMask information:\")\n", "for name, label in tissue_types.items():\n", "    mask = (data_input == label)\n", "    print(f\"{name}: {mask.sum()} voxels (Label {label})\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import numpy as np\n", "# import vtk\n", "# from vtk.util.numpy_support import vtk_to_numpy\n", "# import matplotlib.pyplot as plt\n", "# from ipywidgets import interact\n", "# import ipywidgets as widgets\n", "# import matplotlib.colors as mcolors\n", "# import matplotlib.patches as mpatches\n", "# import seaborn as sns\n", "# from scipy import ndimage\n", "# import ipywidgets as widgets\n", "# from ipywidgets import interactive_output, VBox, IntSlider\n", "# # The class and label definitions remain the same\n", "# # ...\n", "\n", "# # Let's improve the visualization by:\n", "# # 1. Using a better color palette\n", "# # 2. Adding borders between regions\n", "# # 3. Improving the legend and layout\n", "\n", "# def create_improved_visualization(data_input, tissue_types, best_slice, maskLabels):\n", "#     \"\"\"\n", "#     Create an improved static visualization for a single slice with clearer color distinctions.\n", "#     This version reorders the second dimension (columns) by reversing it (i.e. taking [:, ::-1])\n", "#     before display. An edge overlay is added for tissue boundaries, and a legend is shown.\n", "    \n", "#     Parameters:\n", "#       data_input   : numpy array of shape [X, Y, Z] (3D label image)\n", "#       tissue_types : dict mapping tissue names to integer labels\n", "#       best_slice   : int, index of the slice (third dimension) to display\n", "#       maskLabels   : (unused) additional label definitions\n", "#     \"\"\"\n", "#     import matplotlib.pyplot as plt\n", "#     from matplotlib import gridspec\n", "#     import matplotlib.colors as mcolors\n", "#     import matplotlib.patches as mpatches\n", "#     import seaborn as sns\n", "#     from scipy import ndimage\n", "\n", "#     # Set up figure\n", "#     plt.figure(figsize=(20, 16))\n", "#     gs = gridspec.GridSpec(1, 2, width_ratios=[5, 1])  # 5:1 ratio for image:legend\n", "\n", "#     # MAIN IMAGE AXES\n", "#     ax_img = plt.subplot(gs[0])\n", "    \n", "#     # Extract the slice and copy it\n", "#     slice_data = data_input[:, :, best_slice].copy()\n", "    \n", "#     # Keep only voxels in our tissue list; set others to background (0)\n", "#     listed_labels = list(tissue_types.values())\n", "#     mask = (np.isin(slice_data, listed_labels))\n", "#     filtered_slice = np.where(mask, slice_data, 0)\n", "    \n", "#     # Build a normalized array where each tissue is mapped to a unique value\n", "#     label_to_idx = {label: i for i, label in enumerate(listed_labels)}\n", "#     custom_colored_slice = np.zeros_like(filtered_slice, dtype=float)\n", "#     for label in listed_labels:\n", "#         if (filtered_slice == label).any():\n", "#             custom_colored_slice[filtered_slice == label] = label_to_idx[label] + 1\n", "#     if len(label_to_idx) > 0:\n", "#         custom_colored_slice = custom_colored_slice / (len(label_to_idx) + 1)\n", "    \n", "#     # Reverse the second dimension (columns) by reordering the array:\n", "#     custom_colored_slice = custom_colored_slice[:, ::-1]\n", "    \n", "#     # Build custom colormap using a high-contrast palette from seaborn.\n", "#     n_tissues = len(tissue_types)\n", "#     base_colors = sns.color_palette(\"bright\", 10)\n", "#     extended_colors = sns.color_palette(\"husl\", n_tissues - 10 if n_tissues > 10 else 0)\n", "#     distinct_colors = base_colors + extended_colors\n", "#     # First color is transparent for background.\n", "#     cmap_colors = [(0, 0, 0, 0)]\n", "#     for i in range(n_tissues):\n", "#         cmap_colors.append(distinct_colors[i])\n", "#     custom_cmap = mcolors.ListedColormap(cmap_colors)\n", "    \n", "#     # Display the image with custom colormap.\n", "#     ax_img.imshow(custom_colored_slice, cmap=custom_cmap, vmin=0, vmax=1)\n", "#     ax_img.set_title(f'Combined Tissue Labels (Slice {best_slice})', fontsize=18)\n", "    \n", "#     # Edge detection for tissue boundaries.\n", "#     edge_slice = np.zeros_like(filtered_slice)\n", "#     for label in listed_labels:\n", "#         if (filtered_slice == label).any():\n", "#             tissue_mask = (filtered_slice == label).astype(np.uint8)\n", "#             edges = ndimage.sobel(tissue_mask, axis=0)**2 + ndimage.sobel(tissue_mask, axis=1)**2\n", "#             edge_slice[edges > 0] = 1\n", "#     # Reverse edges just like the colored image.\n", "#     edge_slice = edge_slice[:, ::-1]\n", "#     ax_img.contour(edge_slice, levels=[0.5], colors='black', linewidths=0.5, alpha=0.7)\n", "#     ax_img.axis('off')\n", "    \n", "#     # Optionally, zoom into the nonzero (tissue) region with padding.\n", "#     rows = filtered_slice.max(axis=1) > 0\n", "#     cols = filtered_slice.max(axis=0) > 0\n", "#     if rows.sum() and cols.sum():\n", "#         r_idx = np.where(rows)[0]\n", "#         c_idx = np.where(cols)[0]\n", "#         rmin, rmax = r_idx[0], r_idx[-1]\n", "#         cmin, cmax = c_idx[0], c_idx[-1]\n", "#         padding = 20\n", "#         rmin = max(0, rmin - padding)\n", "#         rmax = min(filtered_slice.shape[0] - 1, rmax + padding)\n", "#         cmin = max(0, cmin - padding)\n", "#         cmax = min(filtered_slice.shape[1] - 1, cmax + padding)\n", "#         ax_img.set_xlim(cmin, cmax)\n", "#         ax_img.set_ylim(rmin, rmax)\n", "    \n", "#     # LEGEND AXES\n", "#     ax_legend = plt.subplot(gs[1])\n", "#     ax_legend.axis('off')\n", "    \n", "#     # Organize tissue types into groups (e.g., cardiac, lung, etc.). Adjust grouping as needed.\n", "#     heart_tissues = {k: v for k, v in tissue_types.items() if any(x in k for x in ['LV', 'RV', 'LA', 'RA', 'Blood', 'Peri'])}\n", "#     lung_tissues = {k: v for k, v in tissue_types.items() if 'Lung' in k}\n", "#     other_organs = {k: v for k, v in tissue_types.items() if k not in heart_tissues and k not in lung_tissues and k not in ['body', 'muscle', 'coritcal bone', 'ribs', 'bone marrow']}\n", "#     structural_tissues = {k: v for k, v in tissue_types.items() if k in ['body', 'muscle', 'coritcal bone', 'ribs', 'bone marrow']}\n", "    \n", "#     legend_entries = []\n", "#     def add_tissue_group(tissue_dict, group_name):\n", "#         if tissue_dict:\n", "#             legend_entries.append((mpatches.Patch(color='white', alpha=0), f'\\n{group_name}:'))\n", "#             for name, label in tissue_dict.items():\n", "#                 if (filtered_slice == label).any():\n", "#                     color = distinct_colors[list(tissue_types.keys()).index(name)]\n", "#                     legend_entries.append((mpatches.Patch(color=color), f'{name}'))\n", "#     add_tissue_group(heart_tissues, \"Cardiac Tissues\")\n", "#     add_tissue_group(lung_tissues, \"Respiratory Tissues\")\n", "#     add_tissue_group(other_organs, \"Other Organs\")\n", "#     add_tissue_group(structural_tissues, \"Structural Tissues\")\n", "    \n", "#     if legend_entries:\n", "#         patches, leg_labels = zip(*legend_entries)\n", "#         ax_legend.legend(patches, leg_labels, loc='center', fontsize=14,\n", "#                          frameon=True, fancybox=True, shadow=True,\n", "#                          title=\"Tissue Types\", title_fontsize=16)\n", "    \n", "#     plt.tight_layout()\n", "#     plt.show()\n", "# def create_improved_interactive_viewer(data_input, tissue_types, best_slice, maskLabels):\n", "#     \"\"\"\n", "#     Create an interactive figure that displays the colored label image with a fixed relative \n", "#     size between image and legend. The second dimension of data_input is reordered (from last \n", "#     to first) and edge contours are overlaid with thin, semi-transparent lines.\n", "    \n", "#     Parameters:\n", "#       data_input   : numpy array, the 3D data (assumed shape [X, Y, Z])\n", "#       tissue_types : dict mapping tissue names to integer labels\n", "#       best_slice   : int, initial slice index (along the third dimension)\n", "#       maskLabels   : (unused) can contain label definitions\n", "#     \"\"\"\n", "#     # Prepare label mapping and color palette\n", "#     listed_labels = list(tissue_types.values())\n", "#     label_to_idx = {label: i for i, label in enumerate(listed_labels)}\n", "#     n_tissues = len(tissue_types)\n", "    \n", "#     # Base palette (using seaborn for high contrast colors)\n", "#     base_colors = sns.color_palette(\"bright\", 10)\n", "#     extended_colors = sns.color_palette(\"husl\", max(0, n_tissues-10))\n", "#     distinct_colors = base_colors + extended_colors\n", "#     # Build custom colormap: first color is transparent\n", "#     cmap_colors = [(0, 0, 0, 0)]\n", "#     for i in range(n_tissues):\n", "#         cmap_colors.append(distinct_colors[i])\n", "#     custom_cmap = mcolors.ListedColormap(cmap_colors)\n", "    \n", "#     def update(slice_idx):\n", "#         # Create a new figure with subplots: left (image) and right (legend)\n", "#         fig, (ax_img, ax_legend) = plt.subplots(1, 2, figsize=(20, 16),\n", "#                                                   gridspec_kw={'width_ratios': [5, 1]})\n", "#         ## Process the slice\n", "#         # Get the slice (assumed along third dimension)\n", "#         original_slice = data_input[:, :, slice_idx].copy()\n", "#         # Zero out tissues not in our list\n", "#         mask = np.isin(original_slice, listed_labels)\n", "#         filtered_slice = np.where(mask, original_slice, 0)\n", "        \n", "#         # Create a numeric array where each tissue gets a specific normalized value\n", "#         custom_colored_slice = np.zeros_like(filtered_slice, dtype=float)\n", "#         for label in listed_labels:\n", "#             if np.any(filtered_slice == label):\n", "#                 custom_colored_slice[filtered_slice == label] = label_to_idx[label] + 1\n", "#         if len(label_to_idx) > 0:\n", "#             custom_colored_slice = custom_colored_slice / (len(label_to_idx) + 1)\n", "        \n", "#         # Reorder the second dimension (columns) from last to first\n", "#         custom_colored_slice = custom_colored_slice[:, ::-1]\n", "        \n", "#         # Show the colored image on ax_img\n", "#         im = ax_img.imshow(custom_colored_slice, cmap=custom_cmap, vmin=0, vmax=1)\n", "#         ax_img.set_title(f'Combined Tissue Labels (Slice {slice_idx})', fontsize=18)\n", "#         ax_img.axis('off')\n", "        \n", "#         # Compute edge overlay (for tissue boundaries)\n", "#         edge_slice = np.zeros_like(filtered_slice)\n", "#         for label in listed_labels:\n", "#             if np.any(filtered_slice == label):\n", "#                 tissue_mask = (filtered_slice == label).astype(np.uint8)\n", "#                 # Simple Sobel-based edge detection:\n", "#                 edges = ndimage.sobel(tissue_mask, axis=0)**2 + ndimage.sobel(tissue_mask, axis=1)**2\n", "#                 edge_slice[edges > 0] = 1\n", "#         # Reverse second dimension for edge slice too\n", "#         edge_slice = edge_slice[:, ::-1]\n", "#         # Overlay edges as thin, semi-transparent lines\n", "#         ax_img.contour(edge_slice, levels=[0.5], colors='black', linewidths=0.2, alpha=0.4)\n", "        \n", "#         # Optionally zoom into the nonzero region with some padding\n", "#         rows = np.any(filtered_slice > 0, axis=1)\n", "#         cols = np.any(filtered_slice > 0, axis=0)\n", "#         if np.any(rows) and np.any(cols):\n", "#             rmin, rmax = np.where(rows)[0][[0, -1]]\n", "#             cmin, cmax = np.where(cols)[0][[0, -1]]\n", "#             padding = 20\n", "#             rmin = max(0, rmin - padding)\n", "#             rmax = min(filtered_slice.shape[0] - 1, rmax + padding)\n", "#             cmin = max(0, cmin - padding)\n", "#             cmax = min(filtered_slice.shape[1] - 1, cmax + padding)\n", "#             ax_img.set_xlim(cmin, cmax)\n", "#             ax_img.set_ylim(rmin, rmax)\n", "        \n", "#         # Build legend entries by grouping tissues (example grouping)\n", "#         heart_tissues = {k: v for k, v in tissue_types.items() if any(x in k for x in ['LV','RV','LA','RA','Blood','Peri'])}\n", "#         lung_tissues = {k: v for k, v in tissue_types.items() if 'Lung' in k}\n", "#         other_organs = {k: v for k, v in tissue_types.items() \n", "#                         if k not in heart_tissues and k not in lung_tissues and \n", "#                            k not in ['body','muscle','coritcal bone','ribs','bone marrow']}\n", "#         structural_tissues = {k: v for k, v in tissue_types.items() if k in ['body','muscle','coritcal bone','ribs','bone marrow']}\n", "        \n", "#         legend_entries = []\n", "#         def add_tissue_group(tissue_dict, group_name):\n", "#             if tissue_dict:\n", "#                 legend_entries.append((mpatches.Patch(color='white', alpha=0), f'\\n{group_name}:'))\n", "#                 for name, label in tissue_dict.items():\n", "#                     if np.any(filtered_slice == label):\n", "#                         color = distinct_colors[list(tissue_types.keys()).index(name)]\n", "#                         legend_entries.append((mpatches.Patch(color=color), f'{name}'))\n", "                        \n", "#         add_tissue_group(heart_tissues, \"Cardiac Tissues\")\n", "#         add_tissue_group(lung_tissues, \"Respiratory Tissues\")\n", "#         add_tissue_group(other_organs, \"Other Organs\")\n", "#         add_tissue_group(structural_tissues, \"Structural Tissues\")\n", "#         if legend_entries:\n", "#             patches, leg_labels = zip(*legend_entries)\n", "#             ax_legend.legend(patches, leg_labels, loc='center', fontsize=14, frameon=True,\n", "#                              fancybox=True, shadow=True, title=\"Tissue Types\", title_fontsize=16)\n", "#         ax_legend.axis('off')\n", "#         plt.tight_layout()\n", "#         plt.show()\n", "    \n", "#     # Create an interactive slider for the slice index\n", "#     slider = IntSlider(min=0, max=data_input.shape[2]-1, value=best_slice,\n", "#                          description='Slice:', continuous_update=False)\n", "#     ui = VBox([slider])\n", "#     out = interactive_output(update, {'slice_idx': slider})\n", "#     display(ui, out)\n", "\n", "# # Example usage (assuming the data and tissue types are already defined):\n", "# create_improved_visualization(data_input, tissue_types, 267, maskLabels)\n", "# create_improved_interactive_viewer(data_input, tissue_types, best_slice, maskLabels)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# def create_improved_interactive_viewer_axial(data_input, tissue_types, best_slice, maskLabels):\n", "#     \"\"\"\n", "#     Create an interactive axial viewer that displays the colored label image with a fixed layout.\n", "#     For axial view, the slice is extracted as data_input[slice_idx, :, :]. Before plotting,\n", "#     the second dimension (columns) is reversed (i.e. [:, ::-1]) and edge contours are overlaid.\n", "    \n", "#     Parameters:\n", "#       data_input   : numpy array, the 3D data (assumed shape [X, Y, Z])\n", "#       tissue_types : dict mapping tissue names to integer labels\n", "#       best_slice   : int, initial axial slice index (along the first axis)\n", "#       maskLabels   : (unused) additional label definitions\n", "#     \"\"\"\n", "#     import matplotlib.pyplot as plt\n", "#     import matplotlib.colors as mcolors\n", "#     import matplotlib.patches as mpatches\n", "#     import seaborn as sns\n", "#     from scipy import ndimage\n", "#     import ipywidgets as widgets\n", "#     from ipywidgets import interactive_output, VBox, IntSlider\n", "\n", "#     # Prepare label mapping and color palette\n", "#     listed_labels = list(tissue_types.values())\n", "#     label_to_idx = {label: i for i, label in enumerate(listed_labels)}\n", "#     n_tissues = len(tissue_types)\n", "    \n", "#     # Base palette (using seaborn for high contrast colors)\n", "#     base_colors = sns.color_palette(\"bright\", 10)\n", "#     extended_colors = sns.color_palette(\"husl\", max(0, n_tissues-10))\n", "#     distinct_colors = base_colors + extended_colors\n", "#     # Build custom colormap: first color is transparent for background\n", "#     cmap_colors = [(0, 0, 0, 0)]\n", "#     for i in range(n_tissues):\n", "#         cmap_colors.append(distinct_colors[i])\n", "#     custom_cmap = mcolors.ListedColormap(cmap_colors)\n", "    \n", "#     def update(slice_idx):\n", "#         # Create a new figure with two subplots: left for image, right for legend\n", "#         fig, (ax_img, ax_legend) = plt.subplots(1, 2, figsize=(20, 16),\n", "#                                                   gridspec_kw={'width_ratios': [5, 1]})\n", "#         # Extract the axial slice (data_input[slice_idx, :, :])\n", "#         original_slice = data_input[slice_idx, :, :].copy()\n", "#         # Keep only voxels with labels in tissue_types; else set to background (0)\n", "#         mask = np.isin(original_slice, listed_labels)\n", "#         filtered_slice = np.where(mask, original_slice, 0)\n", "        \n", "#         # Create a normalized array mapping each label to a unique value\n", "#         custom_colored_slice = np.zeros_like(filtered_slice, dtype=float)\n", "#         for label in listed_labels:\n", "#             if (filtered_slice == label).any():\n", "#                 custom_colored_slice[filtered_slice == label] = label_to_idx[label] + 1\n", "#         if len(label_to_idx) > 0:\n", "#             custom_colored_slice = custom_colored_slice / (len(label_to_idx) + 1)\n", "        \n", "#         # Reverse the second dimension (columns)\n", "#         custom_colored_slice = custom_colored_slice[:, ::-1]\n", "        \n", "#         # Display image using the custom colormap\n", "#         ax_img.imshow(custom_colored_slice, cmap=custom_cmap, vmin=0, vmax=1)\n", "#         ax_img.set_title(f'Combined Tissue Labels (Axial Slice {slice_idx})', fontsize=18)\n", "#         ax_img.axis('off')\n", "        \n", "#         # Compute edge overlay (for tissue boundaries)\n", "#         edge_slice = np.zeros_like(filtered_slice)\n", "#         for label in listed_labels:\n", "#             if (filtered_slice == label).any():\n", "#                 tissue_mask = (filtered_slice == label).astype(np.uint8)\n", "#                 edges = ndimage.sobel(tissue_mask, axis=0)**2 + ndimage.sobel(tissue_mask, axis=1)**2\n", "#                 edge_slice[edges > 0] = 1\n", "#         # Reverse the edges as well\n", "#         edge_slice = edge_slice[:, ::-1]\n", "#         ax_img.contour(edge_slice, levels=[0.5], colors='black', linewidths=0.2, alpha=0.4)\n", "        \n", "#         # Optionally zoom in on the region where tissue exists\n", "#         rows = filtered_slice.max(axis=1) > 0\n", "#         cols = filtered_slice.max(axis=0) > 0\n", "#         if rows.sum() and cols.sum():\n", "#             r_idx = np.where(rows)[0]\n", "#             c_idx = np.where(cols)[0]\n", "#             rmin, rmax = r_idx[0], r_idx[-1]\n", "#             cmin, cmax = c_idx[0], c_idx[-1]\n", "#             padding = 20\n", "#             rmin = max(0, rmin - padding)\n", "#             rmax = min(filtered_slice.shape[0] - 1, rmax + padding)\n", "#             cmin = max(0, cmin - padding)\n", "#             cmax = min(filtered_slice.shape[1] - 1, cmax + padding)\n", "#             ax_img.set_xlim(cmin, cmax)\n", "#             ax_img.set_ylim(rmin, rmax)\n", "\n", "#         # Build legend entries for tissue groups\n", "#         heart_tissues = {k: v for k, v in tissue_types.items() if any(x in k for x in ['LV', 'RV', 'LA', 'RA', 'Blood', 'Peri'])}\n", "#         lung_tissues = {k: v for k, v in tissue_types.items() if 'Lung' in k}\n", "#         other_organs = {k: v for k, v in tissue_types.items() if k not in heart_tissues and k not in lung_tissues \n", "#                         and k not in ['body','muscle','coritcal bone','ribs','bone marrow']}\n", "#         structural_tissues = {k: v for k, v in tissue_types.items() if k in ['body','muscle','coritcal bone','ribs','bone marrow']}\n", "        \n", "#         legend_entries = []\n", "#         def add_tissue_group(tissue_dict, group_name):\n", "#             if tissue_dict:\n", "#                 legend_entries.append((mpatches.Patch(color='white', alpha=0), f'\\n{group_name}:'))\n", "#                 for name, label in tissue_dict.items():\n", "#                     if (filtered_slice == label).any():\n", "#                         color = distinct_colors[list(tissue_types.keys()).index(name)]\n", "#                         legend_entries.append((mpatches.Patch(color=color), f'{name}'))\n", "#         add_tissue_group(heart_tissues, \"Cardiac Tissues\")\n", "#         add_tissue_group(lung_tissues, \"Respiratory Tissues\")\n", "#         add_tissue_group(other_organs, \"Other Organs\")\n", "#         add_tissue_group(structural_tissues, \"Structural Tissues\")\n", "#         if legend_entries:\n", "#             patches, leg_labels = zip(*legend_entries)\n", "#             ax_legend.legend(patches, leg_labels, loc='center', fontsize=14,\n", "#                              frameon=True, fancybox=True, shadow=True,\n", "#                              title=\"Tissue Types\", title_fontsize=16)\n", "#         ax_legend.axis('off')\n", "#         plt.tight_layout()\n", "#         plt.show()\n", "    \n", "#     slider = IntSlider(min=0, max=data_input.shape[0]-1, value=best_slice,\n", "#                        description='Axial Slice:', continuous_update=False)\n", "#     ui = VBox([slider])\n", "#     out = interactive_output(update, {'slice_idx': slider})\n", "#     display(ui, out)\n", "    \n", "# # Call the axial viewer (assuming data_input, tissue_types, best_slice, and maskLabels are defined):\n", "# create_improved_interactive_viewer_axial(data_input, tissue_types, best_slice, maskLabels)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# def create_interactive_oblique_viewer_3D(data_input, tissue_types, best_slice, maskLabels, Lu=200, Lv=100,theta = -45):\n", "#     \"\"\"\n", "#     Interactive viewer that re-slices the 3D volume along an oblique plane.\n", "    \n", "#     The new slicing plane is defined as follows:\n", "#       • Center at:  center = (best_slice, Y/2, X/2)  [using volume order (z,y,x)]\n", "#       • new_x axis: 45° direction in the axial (y–x) plane:\n", "#                      new_x = (0, cos45, sin45)\n", "#       • new_y axis: Along the original z-axis:\n", "#                      new_y = (1, 0, 0)\n", "#       • new_z axis: Perpendicular, computed as:\n", "#                      new_z = cross(new_x, new_y) = (0, cos45, -sin45)\n", "    \n", "#     The 2D oblique slice is then extracted from the volume via:\n", "#          original_coord = center + u * new_x + v * new_y + offset * new_z,\n", "#     where u ∈ [–Lu/2, Lu/2], v ∈ [–Lv/2, Lv/2].\n", "    \n", "#     The interactive slider controls offset (displacement along new_z).\n", "    \n", "#     Parameters:\n", "#       data_input : 3D numpy array with shape (Z, Y, X)\n", "#       tissue_types : dict (unused here; for consistency)\n", "#       best_slice : int, the axial slice index to serve as center (e.g. 604)\n", "#       maskLabels : unused here\n", "#       Lu : length (in pixels) along new_x (the oblique direction in the axial plane)\n", "#       Lv : length (in pixels) along new_y (the original z direction)\n", "#     \"\"\"\n", "#     import numpy as np\n", "#     import matplotlib.pyplot as plt\n", "#     from scipy import ndimage\n", "#     import ipywidgets as widgets\n", "#     from ipywidgets import interactive_output, VBox, IntSlider\n", "\n", "#     # Determine volume dimensions and center point.\n", "#     Z, Y, X = data_input.shape\n", "#     center = np.array([best_slice, Y/2, X/2])  # center = (z, y, x)\n", "    \n", "#     # Define new coordinate directions.\n", "#     # new_x: 45° in the axial (y,x) plane, no z component.\n", "#     # theta = -45\n", "#     theta_rad = np.deg2rad(theta)\n", "#     new_x = np.array([0, np.cos(theta_rad), np.sin(theta_rad)])  # e.g. (0, 0.7071, 0.7071)\n", "#     # new_y: along the original z-axis.\n", "#     new_y = np.array([1, 0, 0])\n", "#     # new_z: perpendicular to both.\n", "#     new_z = np.cross(new_x, new_y)  # = (0, cos45, -sin45) ≈ (0, 0.7071, -0.7071)\n", "    \n", "#     def extract_oblique_slice(offset):\n", "#         \"\"\"\n", "#         Extract a 2D oblique slice from the 3D volume.\n", "        \n", "#         For pixel coordinates (u, v) in the new plane, mapping is given by:\n", "#            original = center + u * new_x + v * new_y + offset * new_z.\n", "#         u is in [-Lu/2, Lu/2] and v in [-Lv/2, Lv/2].\n", "#         Out-of-bound values are padded with 0.\n", "#         \"\"\"\n", "#         Lu_pixels = int(Lu)\n", "#         Lv_pixels = int(Lv)\n", "#         # Create grid for new slice coordinates.\n", "#         u = np.linspace(-Lu/2, Lu/2, Lu_pixels)\n", "#         v = np.linspace(-Lv/2, Lv/2, Lv_pixels)\n", "#         U, V = np.meshgrid(u, v)  # shape (Lv_pixels, Lu_pixels)\n", "#         # Compute original coordinates.\n", "#         # Note: Original coordinate order: (z, y, x).\n", "#         Z_coord = center[0] + V * new_y[0] + offset * new_z[0]  # new_y contributes to z; new_z[0] is 0.\n", "#         Y_coord = center[1] + U * new_x[1] + offset * new_z[1]  # new_x[1] = cos45; new_z[1] = cos45.\n", "#         X_coord = center[2] + U * new_x[2] + offset * new_z[2]  # new_x[2] = sin45; new_z[2] = -sin45.\n", "#         coords = [Z_coord, Y_coord, X_coord]\n", "#         oblique_slice = ndimage.map_coordinates(data_input, coords, order=1, mode='constant', cval=0)\n", "#         return oblique_slice\n", "\n", "#     def update(offset):\n", "#         plt.clf()\n", "#         fig, (ax_left, ax_right) = plt.subplots(1, 2, figsize=(20, 10))\n", "        \n", "#         # Left panel: show the original axial slice (at best_slice).\n", "#         axial = data_input[best_slice, :, :]\n", "#         ax_left.imshow(axial, cmap='gray')\n", "#         ax_left.set_title(f'Axial Slice {best_slice}', fontsize=16)\n", "#         ax_left.axis('off')\n", "#         # Compute intersection (line) of the oblique plane with axial plane (z = best_slice).\n", "#         # The intersection occurs when: center + u*new_x + v*new_y + offset*new_z has z = best_slice.\n", "#         # Since center_z = best_slice and new_y = (1,0,0), we have: best_slice + v = best_slice  => v = 0.\n", "#         # Thus, for varying u with v=0:\n", "#         u_line = np.linspace(-Lu/2, Lu/2, 100)\n", "#         v_line = np.zeros_like(u_line)  # v = 0\n", "#         # Then:\n", "#         y_line = center[1] + u_line * new_x[1] + offset * new_z[1]\n", "#         x_line = center[2] + u_line * new_x[2] + offset * new_z[2]\n", "#         ax_left.plot(x_line, y_line, 'r--', linewidth=2)\n", "        \n", "#         # Right panel: oblique slice extracted from the volume.\n", "#         obslice = extract_oblique_slice(offset)\n", "#         ax_right.imshow(obslice, cmap='gray')\n", "#         ax_right.set_title(f'Oblique Slice (offset = {offset}px)', fontsize=16)\n", "#         ax_right.axis('off')\n", "        \n", "#         plt.tight_layout()\n", "#         plt.show()\n", "    \n", "#     slider = IntSlider(min=-50, max=50, step=1, value=0, description='Offset (px):', continuous_update=False)\n", "#     ui = VBox([slider])\n", "#     out = interactive_output(update, {'offset': slider})\n", "#     display(ui, out)\n", "\n", "# # Example usage:\n", "# # Here we choose slice 604 as the reference axial slice.\n", "# create_interactive_oblique_viewer_3D(data_input, tissue_types, 604, maskLabels, Lu=200, Lv=100,theta = -45)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import numpy as np\n", "# from scipy import ndimage\n", "\n", "# # Parameters for re-slicing\n", "# best_slice = 604         # axial slice used as center\n", "# Lu = 200                 # length along new_x (in pixels)\n", "# Lv = 100                 # length along new_y (in pixels)\n", "# Lz = 101                 # number of slices along new_z (choose an odd number for a central slice)\n", "# # d will range from -D/2 to D/2, so that the central slice (d=0) becomes the reference.\n", "# D = 100                   # total span in new_z (in pixels), for example 6 pixels -> d from -3 to 3\n", "\n", "# # Volume dimensions and center (data_input is assumed defined)\n", "# Z, Y, X = data_input.shape\n", "# center = np.array([best_slice, Y/2, X/2])  # order: (z, y, x)\n", "\n", "# # Define new coordinate axes.\n", "# # For an oblique plane with new_x along -45° in axial (y,x) plane:\n", "# theta = -45\n", "# theta_rad = np.deg2rad(theta)\n", "# new_x = np.array([0, np.cos(theta_rad), np.sin(theta_rad)])  # (0, cosθ, sinθ)\n", "# new_y = np.array([1, 0, 0])  # along original z-axis\n", "# # We want new_z = (0, cosθ, -sinθ) as specified.\n", "# new_z = -np.cross(new_x, new_y)  # ensures new_z = (0, cosθ, -sinθ)\n", "\n", "# # Set up new resliced volume dimensions (new volume coordinate grid):\n", "# Lu_pixels = int(Lu)\n", "# Lv_pixels = int(Lv)\n", "# Lz_pixels = int(Lz)\n", "\n", "# # Create grids for new coordinates:\n", "# # u: along new_x, v: along new_y, d: along new_z.\n", "# u = np.linspace(-Lu/2, Lu/2, Lu_pixels)\n", "# v = np.linspace(-Lv/2, Lv/2, Lv_pixels)\n", "# d = np.linspace(-D/2, D/2, Lz_pixels)  # d coordinate\n", "\n", "# # Create a 3D meshgrid over (d, v, u)\n", "# # The order of axes for the new volume will be: new_z (d), new_y (v), new_x (u)\n", "# D_grid, V_grid, U_grid = np.meshgrid(d, v, u, indexing='ij')  # shape (Lz, Lv, Lu)\n", "\n", "# # Map new coordinates (d, v, u) to original coordinates:\n", "# # original = center + d*new_z + v*new_y + u*new_x\n", "# orig_Z = center[0] + D_grid * new_z[0] + V_grid * new_y[0] + U_grid * new_x[0]\n", "# orig_Y = center[1] + D_grid * new_z[1] + V_grid * new_y[1] + U_grid * new_x[1]\n", "# orig_X = center[2] + D_grid * new_z[2] + V_grid * new_y[2] + U_grid * new_x[2]\n", "\n", "# coords = [orig_Z, orig_Y, orig_X]\n", "\n", "# # Reslice the original volume using linear interpolation and zero-padding for out-of-bound values.\n", "# ref_volume = ndimage.map_coordinates(data_input, coords, order=1, mode='constant', cval=0)\n", "\n", "# # Determine the central slice index in the new volume (along d)\n", "# ref_slice_index = Lz_pixels // 2\n", "\n", "# print(\"Oblique 3D volume stored in ref_volume with shape:\", ref_volume.shape)\n", "# print(\"Central oblique slice index:\", ref_slice_index)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# create_interactive_oblique_viewer_3D(ref_volume, tissue_types, 51, maskLabels,  Lu=200, Lv=100,theta = -18)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import numpy as np\n", "# from scipy import ndimage\n", "\n", "# # --- Parameters for new re-slicing from the current ref_volume ---\n", "# # Here ref_volume is your previously stored 3D oblique volume.\n", "# # Its shape is assumed to be (Lz, Lv, Lu) with axes:\n", "# #  - Axis0: along the original new_z (d)\n", "# #  - Axis1: along new_y (v)\n", "# #  - Axis2: along new_x (u)\n", "# #\n", "# # We want to re-slice ref_volume with new parameters:\n", "# new_best_slice = 51       # center slice index in ref_volume along its first dimension\n", "# new_theta = -18           # new angle in degrees for new_x (in the (v,u) plane of ref_volume)\n", "# fixed_offset = 31         # fixed offset (in pixels) that we now want to incorporate\n", "# Lu_new = 200              # new length along new_x (u axis)\n", "# Lv_new = 100              # new length along new_y (v axis)\n", "# Lz_new = 51         # desired number of slices along new (re-sliced) z axis\n", "# D_new = 200              # total span in new z (in pixels)\n", "\n", "# # Determine dimensions of the current ref_volume\n", "# Lz_old, Lv_old, Lu_old = ref_volume.shape\n", "# # Define new center in ref_volume coordinates (order: (d, v, u)):\n", "# center_new = np.array([new_best_slice, Lv_old/2, Lu_old/2])\n", "\n", "# # Define new coordinate axes for the re-slicing.\n", "# # Here we interpret the in-plane (v,u) axes as follows:\n", "# #   new_x_re: in-plane direction at new_theta (relative to horizontal axis in ref_volume)\n", "# #            (Note: In our convention, the ref_volume image is indexed as [v, u])\n", "# #   new_y_re: taken along the original d-axis.\n", "# #   new_z_re: defined as perpendicular (we want to shift around the plane normal).\n", "# #\n", "# # We embed the fixed_offset by shifting the center along new_z_re.\n", "# new_theta_rad = np.deg2rad(new_theta)\n", "# new_x_re = np.array([0, np.cos(new_theta_rad), np.sin(new_theta_rad)])   # no d component\n", "# new_y_re = np.array([1, 0, 0])  # along the original d-axis (slice axis of ref_volume)\n", "# # Define new_z_re as perpendicular to both:\n", "# new_z_re = -np.cross(new_x_re, new_y_re)  # This yields new_z_re = (0, cos(new_theta), -sin(new_theta))\n", "\n", "# # Now, incorporate the fixed offset by shifting the center.\n", "# shifted_center = center_new + fixed_offset * new_z_re\n", "\n", "# # Set up new re-sliced volume coordinate grid.\n", "# Lu_pixels = int(Lu_new)\n", "# Lv_pixels = int(Lv_new)\n", "# Lz_pixels = int(Lz_new)\n", "\n", "# # Create grids for local coordinates:\n", "# # u: along new_x_re, v: along new_y_re, d: along new_z_re.\n", "# u = np.linspace(-Lu_new/2, Lu_new/2, Lu_pixels)\n", "# v = np.linspace(-Lv_new/2, Lv_new/2, Lv_pixels)\n", "# d = np.linspace(-D_new/2, D_new/2, Lz_pixels)  # d coordinate in the new system\n", "\n", "# # Meshgrid: order (d, v, u)\n", "# D_grid, V_grid, U_grid = np.meshgrid(d, v, u, indexing='ij')\n", "\n", "# # Map new coordinates to ref_volume coordinates:\n", "# # For any point (d, v, u) in the new coordinate system:\n", "# #    coord = shifted_center + u * new_x_re + v * new_y_re + d * new_z_re\n", "# orig_d = shifted_center[0] + D_grid * new_z_re[0] + V_grid * new_y_re[0] + U_grid * new_x_re[0]\n", "# orig_v = shifted_center[1] + D_grid * new_z_re[1] + V_grid * new_y_re[1] + U_grid * new_x_re[1]\n", "# orig_u = shifted_center[2] + D_grid * new_z_re[2] + V_grid * new_y_re[2] + U_grid * new_x_re[2]\n", "\n", "# coords_new = [orig_d, orig_v, orig_u]\n", "\n", "# # Resample ref_volume using zero-padding (mode='constant', cval=0)\n", "# new_ref_volume = ndimage.map_coordinates(ref_volume, coords_new, order=1, mode='constant', cval=0)\n", "\n", "# # Determine the central slice index in new_ref_volume (expected to be at d=0)\n", "# central_slice_index = Lz_pixels // 2\n", "# print(\"New re-sliced 3D volume shape:\", new_ref_volume.shape)\n", "# print(\"Central slice index in new volume:\", central_slice_index)\n", "\n", "# # Now, use create_improved_interactive_viewer to demo new_ref_volume.\n", "# # (Assuming create_improved_interactive_viewer is already defined to accept a 3D array.)\n", "# # create_improved_interactive_viewer(new_ref_volume, tissue_types, central_slice_index, maskLabels)\n", "# # create_improved_visualization(new_ref_volume, tissue_types, 3, maskLabels)\n", "# import matplotlib.pyplot as plt\n", "\n", "# # Extract the central slice from the new 3D oblique volume.\n", "# central_slice = new_ref_volume[3, :, :]\n", "\n", "# # Display the central slice.\n", "# plt.figure(figsize=(10, 8))\n", "# plt.imshow(central_slice, cmap='gray')\n", "# plt.title(f'Central Oblique Slice (index {central_slice_index})')\n", "# plt.axis('off')\n", "# plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import matplotlib.pyplot as plt\n", "# import ipywidgets as widgets\n", "# from ipywidgets import interactive_output, VBox, HBox, IntSlider, Dropdown\n", "# from IPython.display import display\n", "# import numpy as np\n", "\n", "# def create_interactive_viewer(volume_data, colormap='gray'):\n", "#     \"\"\"\n", "#     Create an interactive viewer for a 3D volume dataset.\n", "    \n", "#     Parameters:\n", "#     -----------\n", "#     volume_data : numpy.ndarray\n", "#         3D array with dimensions [depth, height, width]\n", "#     colormap : str\n", "#         Matplotlib colormap name to use for display (default: 'gray')\n", "#     \"\"\"\n", "#     # Get the dimensions of the volume\n", "#     depth, height, width = volume_data.shape\n", "    \n", "#     # Create widgets for user interaction\n", "#     view_axis_dropdown = Dropdown(\n", "#         options=[('Axial (Depth)', 0), ('Coronal (Height)', 1), ('Sagittal (Width)', 2)],\n", "#         value=0,\n", "#         description='View Axis:',\n", "#         style={'description_width': 'initial'}\n", "#     )\n", "    \n", "#     slice_slider = IntSlider(\n", "#         min=0,\n", "#         max=depth-1,\n", "#         value=depth//2,\n", "#         description='Slice:',\n", "#         continuous_update=False,\n", "#         style={'description_width': 'initial'}\n", "#     )\n", "    \n", "#     # Function to update the range of the slice slider when view axis changes\n", "#     def update_slider_range(change):\n", "#         axis = change['new']\n", "#         if axis == 0:  # Axial (Depth)\n", "#             slice_slider.max = depth-1\n", "#             slice_slider.value = min(slice_slider.value, depth-1)\n", "#         elif axis == 1:  # <PERSON>ronal (Height)\n", "#             slice_slider.max = height-1\n", "#             slice_slider.value = min(slice_slider.value, height-1)\n", "#         else:  # <PERSON>git<PERSON> (Width)\n", "#             slice_slider.max = width-1\n", "#             slice_slider.value = min(slice_slider.value, width-1)\n", "    \n", "#     view_axis_dropdown.observe(update_slider_range, names='value')\n", "    \n", "#     # Function to display the slice\n", "#     def update_display(view_axis, slice_index):\n", "#         plt.figure(figsize=(10, 8))\n", "        \n", "#         # Get the appropriate slice based on the selected view axis\n", "#         if view_axis == 0:  # Axial (Depth)\n", "#             slice_data = volume_data[slice_index, :, :]\n", "#             view_name = 'Axial'\n", "#         elif view_axis == 1:  # Coronal (Height)\n", "#             slice_data = volume_data[:, slice_index, :]\n", "#             view_name = 'Coronal'\n", "#         else:  # <PERSON>git<PERSON> (Width)\n", "#             slice_data = volume_data[:, :, slice_index]\n", "#             view_name = '<PERSON><PERSON><PERSON>'\n", "        \n", "#         # Display the slice\n", "#         plt.imshow(slice_data, cmap=colormap)\n", "#         plt.title(f'{view_name} Slice (index {slice_index})')\n", "#         plt.axis('off')\n", "#         plt.colorbar(label='Intensity')\n", "#         plt.tight_layout()\n", "#         plt.show()\n", "    \n", "#     # Create the interactive output\n", "#     out = interactive_output(update_display, {'view_axis': view_axis_dropdown, 'slice_index': slice_slider})\n", "    \n", "#     # Create the UI layout\n", "#     ui = VBox([\n", "#         HBox([view_axis_dropdown, slice_slider]),\n", "#         out\n", "#     ])\n", "    \n", "#     # Display the UI\n", "#     display(ui)\n", "\n", "# # Example usage:\n", "# create_interactive_viewer(new_ref_volume)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# def demo_central_slice_with_vertical_flip_fixed(central_slice, tissue_types, central_slice_index, maskLabels):\n", "#     \"\"\"\n", "#     Fixed version with consistent color mapping between labels and tissue types\n", "#     \"\"\"\n", "#     import matplotlib.pyplot as plt\n", "#     from matplotlib import gridspec\n", "#     import matplotlib.colors as mcolors\n", "#     import matplotlib.patches as mpatches\n", "#     import seaborn as sns\n", "#     from scipy import ndimage\n", "#     import numpy as np\n", "\n", "#     # Round label values to the nearest integer to handle floating-point issues\n", "#     central_slice_rounded = np.round(central_slice)\n", "    \n", "#     # 诊断信息：查看切片中存在的标签\n", "#     unique_labels = np.unique(central_slice_rounded)\n", "#     print(f\"Unique labels in slice (after rounding): {unique_labels}\")\n", "#     print(f\"Labels in tissue_types: {sorted(tissue_types.values())}\")\n", "#     missing_labels = [label for label in unique_labels if label not in tissue_types.values() and label != 0]\n", "#     if missing_labels:\n", "#         print(f\"WARNING: These labels are in the slice but not in tissue_types: {missing_labels}\")\n", "\n", "#     # 设置图形\n", "#     plt.figure(figsize=(20, 16))\n", "#     gs = gridspec.GridSpec(1, 2, width_ratios=[5, 1])\n", "#     ax_img = plt.subplot(gs[0])\n", "    \n", "#     # 处理切片 - 仅保留组织列表中的像素 (using rounded values)\n", "#     listed_labels = list(tissue_types.values())\n", "#     mask = np.isin(central_slice_rounded, listed_labels)\n", "#     filtered_slice = np.where(mask, central_slice_rounded, 0)\n", "    \n", "#     # 修改：创建直接映射每个标签到其颜色的字典\n", "#     # 这确保每个组织类型始终获得相同的颜色，不受顺序影响\n", "#     n_tissues = len(tissue_types)\n", "#     base_colors = sns.color_palette(\"bright\", 10)\n", "#     extended_colors = sns.color_palette(\"husl\", max(0, n_tissues - 10))\n", "#     distinct_colors = base_colors + extended_colors\n", "    \n", "#     # 为每个标签创建固定的颜色映射\n", "#     # 使用tissue_types的键顺序来分配颜色\n", "#     tissue_to_color = {}\n", "#     for i, (tissue_name, label) in enumerate(tissue_types.items()):\n", "#         tissue_to_color[label] = i % len(distinct_colors)  # 防止索引超出范围\n", "    \n", "#     # 创建一个用于可视化的新数组\n", "#     custom_colored_slice = np.zeros_like(filtered_slice, dtype=float)\n", "#     for label in np.unique(filtered_slice):\n", "#         if label > 0:  # 跳过背景(0)\n", "#             # 直接将标签映射到其固定颜色索引\n", "#             color_index = tissue_to_color.get(label, 0) + 1  # +1因为第一个颜色是透明的\n", "#             custom_colored_slice[filtered_slice == label] = color_index\n", "    \n", "#     # 归一化颜色索引\n", "#     if np.max(custom_colored_slice) > 0:\n", "#         custom_colored_slice = custom_colored_slice / (n_tissues + 1)\n", "    \n", "#     # 垂直翻转图像\n", "#     custom_colored_slice = custom_colored_slice[::-1, :]\n", "    \n", "#     # 构建自定义颜色映射\n", "#     cmap_colors = [(0, 0, 0, 0)]  # 第一个颜色透明用于背景\n", "#     for i in range(n_tissues):\n", "#         cmap_colors.append(distinct_colors[i])\n", "#     custom_cmap = mcolors.ListedColormap(cmap_colors)\n", "    \n", "#     # 显示图像\n", "#     ax_img.imshow(custom_colored_slice, cmap=custom_cmap, vmin=0, vmax=1)\n", "#     ax_img.set_title(f'Oblique Slice (index {central_slice_index})', fontsize=18)\n", "    \n", "#     # 边缘检测\n", "#     edge_slice = np.zeros_like(filtered_slice)\n", "#     for label in listed_labels:\n", "#         if (filtered_slice == label).any():\n", "#             tissue_mask = (filtered_slice == label).astype(np.uint8)\n", "#             edges = ndimage.sobel(tissue_mask, axis=0)**2 + ndimage.sobel(tissue_mask, axis=1)**2\n", "#             edge_slice[edges > 0] = 1\n", "    \n", "#     # 翻转边缘\n", "#     edge_slice = edge_slice[::-1, :]\n", "    \n", "#     # 添加边缘覆盖\n", "#     ax_img.contour(edge_slice, levels=[0.5], colors='black', linewidths=0.3, alpha=0.4)\n", "#     ax_img.axis('off')\n", "    \n", "#     # 自动缩放至非零区域\n", "#     rows = np.any(filtered_slice > 0, axis=1)\n", "#     cols = np.any(filtered_slice > 0, axis=0)\n", "#     if np.any(rows) and np.any(cols):\n", "#         r_idx = np.where(rows)[0]\n", "#         c_idx = np.where(cols)[0]\n", "#         if len(r_idx) > 0 and len(c_idx) > 0:\n", "#             rmin, rmax = r_idx[0], r_idx[-1]\n", "#             cmin, cmax = c_idx[0], c_idx[-1]\n", "#             padding = 20\n", "#             rmin = max(0, rmin - padding)\n", "#             rmax = min(filtered_slice.shape[0] - 1, rmax + padding)\n", "#             cmin = max(0, cmin - padding)\n", "#             cmax = min(filtered_slice.shape[1] - 1, cmax + padding)\n", "            \n", "#             # 调整翻转后的坐标\n", "#             flipped_rmin = filtered_slice.shape[0] - 1 - rmax\n", "#             flipped_rmax = filtered_slice.shape[0] - 1 - rmin\n", "            \n", "#             ax_img.set_xlim(cmin, cmax)\n", "#             ax_img.set_ylim(flipped_rmin, flipped_rmax)\n", "    \n", "#     # 图例面板\n", "#     ax_legend = plt.subplot(gs[1])\n", "#     ax_legend.axis('off')\n", "    \n", "#     # 将组织类型分组\n", "#     heart_tissues = {k: v for k, v in tissue_types.items() if any(x in k for x in ['LV', 'RV', 'LA', 'RA', 'Blood', 'Peri'])}\n", "#     lung_tissues = {k: v for k, v in tissue_types.items() if 'Lung' in k}\n", "#     other_organs = {k: v for k, v in tissue_types.items() if k not in heart_tissues and k not in lung_tissues and k not in ['body', 'muscle', 'coritcal bone', 'ribs', 'bone marrow']}\n", "#     structural_tissues = {k: v for k, v in tissue_types.items() if k in ['body', 'muscle', 'coritcal bone', 'ribs', 'bone marrow']}\n", "    \n", "#     # 构建图例\n", "#     legend_entries = []\n", "#     def add_tissue_group(tissue_dict, group_name):\n", "#         if tissue_dict:\n", "#             legend_entries.append((mpatches.Patch(color='white', alpha=0), f'\\n{group_name}:'))\n", "#             for name, label in tissue_dict.items():\n", "#                 if (filtered_slice == label).any():\n", "#                     # 使用与图像相同的颜色映射逻辑\n", "#                     color_index = tissue_to_color.get(label, 0)\n", "#                     color = distinct_colors[color_index]\n", "#                     legend_entries.append((mpatches.Patch(color=color), f'{name}'))\n", "    \n", "#     add_tissue_group(heart_tissues, \"Cardiac Tissues\")\n", "#     add_tissue_group(lung_tissues, \"Respiratory Tissues\")\n", "#     add_tissue_group(other_organs, \"Other Organs\")\n", "#     add_tissue_group(structural_tissues, \"Structural Tissues\")\n", "    \n", "#     if legend_entries:\n", "#         patches, leg_labels = zip(*legend_entries)\n", "#         ax_legend.legend(patches, leg_labels, loc='center', fontsize=14,\n", "#                          frameon=True, fancybox=True, shadow=True,\n", "#                          title=\"Tissue Types\", title_fontsize=16)\n", "    \n", "#     plt.tight_layout()\n", "#     plt.show()\n", "\n", "# # 提取切片并显示\n", "# central_slice = new_ref_volume[10, :, :]\n", "# demo_central_slice_with_vertical_flip_fixed(central_slice, tissue_types, 10, maskLabels)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Part 8: Coronal View Interactive Viewer\n", "def create_coronal_viewer():\n", "    fig = plt.figure(figsize=(10, 8))\n", "    \n", "    def update(slice_idx):\n", "        plt.clf()\n", "        # For coronal view, we take slices along the second dimension\n", "        img = plt.imshow(data_input[:,slice_idx,:], cmap='nipy_spectral')\n", "        plt.title(f'Coronal View - Slice {slice_idx}')\n", "        \n", "        legend_elements = []\n", "        unique_labels = np.unique(data_input[:,slice_idx,:])\n", "        unique_labels = unique_labels[unique_labels != 0]\n", "        \n", "        for name, label in tissue_types.items():\n", "            if label in unique_labels:\n", "                color = img.cmap(img.norm(label))\n", "                legend_elements.append(plt.Rectangle((0,0), 1, 1, fc=color, label=f'{name} ({label})'))\n", "        \n", "        plt.legend(handles=legend_elements, bbox_to_anchor=(1.05, 1), loc='upper left')\n", "        plt.axis('off')\n", "        plt.tight_layout()\n", "        plt.draw()\n", "    \n", "    interact(update, \n", "            slice_idx=widgets.IntSlider(\n", "                min=0,\n", "                max=data_input.shape[1]-1,\n", "                step=1,\n", "                value=data_input.shape[1]//2,\n", "                description='Coronal Slice:',\n", "                continuous_update=False\n", "            ))\n", "\n", "create_coronal_viewer()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # Read resolution information\n", "# def get_dataset_resolution():\n", "#     # 1. First try to read from VTI file\n", "#     vti_path = 'inpuData/Background/XCAT_vti/Image_01.vti'\n", "#     img_ref = vtk.vtkXMLImageDataReader()\n", "#     img_ref.SetFileName(vti_path)\n", "#     img_ref.Update()\n", "#     spacing = img_ref.GetOutput().GetSpacing()\n", "    \n", "#     # 2. Also read parameters from XCAT par file\n", "#     par_file_path = 'inpuData/XCAT_par_file.par'\n", "#     try:\n", "#         with open(par_file_path, 'r') as f:\n", "#             par_content = f.readlines()\n", "#             # Look for resolution related parameters in par file\n", "#             for line in par_content:\n", "#                 if 'pixel_width' in line or 'pixel_height' in line:\n", "#                     print(f\"XCAT parameter: {line.strip()}\")\n", "#     except FileNotFoundError:\n", "#         print(\"PAR file not found\")\n", "    \n", "#     print(f\"VTI resolution (mm/pixel): {spacing}\")\n", "#     return spacing\n", "\n", "# def get_image_orientation():\n", "#     \"\"\"Get image orientation information\"\"\"\n", "#     vti_path = 'inpuData/Background/XCAT_vti/Image_01.vti'\n", "#     img_ref = vtk.vtkXMLImageDataReader()\n", "#     img_ref.SetFileName(vti_path)\n", "#     img_ref.Update()\n", "    \n", "#     # Get image information\n", "#     image_data = img_ref.GetOutput()\n", "    \n", "#     # Get orientation matrix\n", "#     matrix = image_data.GetDirectionMatrix()\n", "#     origin = image_data.GetOrigin()\n", "#     spacing = image_data.GetSpacing()\n", "#     dimensions = image_data.GetDimensions()\n", "    \n", "#     print(\"\\nImage Orientation Information:\")\n", "#     print(f\"Dimensions: {dimensions}\")\n", "#     print(f\"Spacing: {spacing} mm\")\n", "#     print(f\"Origin: {origin}\")\n", "#     print(\"\\nDirection Matrix:\")\n", "#     for i in range(3):\n", "#         row = [matrix.GetElement(i,j) for j in range(3)]\n", "#         print(f\"  {row}\")\n", "        \n", "#     # Try to determine primary orientation\n", "#     # This is a simplified approach - real medical images would have more detailed orientation info\n", "#     max_dim = max(dimensions)\n", "#     max_dim_idx = dimensions.index(max_dim)\n", "#     orientations = ['Sagittal', 'Coronal', 'Axial']\n", "#     primary_orientation = orientations[max_dim_idx]\n", "    \n", "#     print(f\"\\nPrimary orientation appears to be: {primary_orientation}\")\n", "#     print(\"Note: This is an estimate based on image dimensions\")\n", "    \n", "#     return {\n", "#         'dimensions': dimensions,\n", "#         'spacing': spacing,\n", "#         'origin': origin,\n", "#         'matrix': matrix,\n", "#         'primary_orientation': primary_orientation\n", "#     }\n", "\n", "# # Get orientation information\n", "# orientation_info = get_image_orientation()\n", "# # Get resolution\n", "# voxel_size = get_dataset_resolution()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Block 3: Tissue Group Classification\n", "Creates four main tissue groups for B0 field synthesis:\n", "- Cardiac tissue (labels 1,2,5,6)\n", "- Lung tissue (label 16)\n", "- Other tissues (remaining labels)\n", "- Air (unlabeled regions)\n", "Includes visualization and volume statistics for each group.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def find_connected_components(binary_mask):\n", "    \"\"\"\n", "    Find connected components in a 3D binary mask using NumPy\n", "    \"\"\"\n", "    # Initialize labels array\n", "    labels = np.zeros_like(binary_mask, dtype=int)\n", "    current_label = 1\n", "    \n", "    def flood_fill(pos, label):\n", "        stack = [pos]\n", "        while stack:\n", "            x, y, z = stack.pop()\n", "            if not (0 <= x < binary_mask.shape[0] and \n", "                   0 <= y < binary_mask.shape[1] and \n", "                   0 <= z < binary_mask.shape[2]):\n", "                continue\n", "            if not binary_mask[x,y,z] or labels[x,y,z]:\n", "                continue\n", "            labels[x,y,z] = label\n", "            # Add neighbors to stack\n", "            for dx, dy, dz in [(0,0,1), (0,0,-1), (0,1,0), (0,-1,0), (1,0,0), (-1,0,0)]:\n", "                stack.append((x+dx, y+dy, z+dz))\n", "    \n", "    # Find connected components\n", "    for x in range(binary_mask.shape[0]):\n", "        for y in range(binary_mask.shape[1]):\n", "            for z in range(binary_mask.shape[2]):\n", "                if binary_mask[x,y,z] and not labels[x,y,z]:\n", "                    flood_fill((x,y,z), current_label)\n", "                    current_label += 1\n", "    \n", "    return labels\n", "\n", "def clean_rv_wall_mask(data):\n", "    \"\"\"\n", "    Filter RV wall mask to keep connected components that are adjacent to RV blood\n", "    \n", "    Parameters:\n", "    -----------\n", "    data : ndarray\n", "        3D array with tissue labels where 2=RV wall and 6=RV blood\n", "    \n", "    Returns:\n", "    --------\n", "    n<PERSON><PERSON>\n", "        Binary mask of cleaned RV wall\n", "    \"\"\"\n", "    # Create masks for RV wall and blood\n", "    rv_wall = (data == 2)\n", "    rv_blood = (data == 6)\n", "    \n", "    # Find connected components in RV wall\n", "    wall_components = find_connected_components(rv_wall)\n", "    num_components = wall_components.max()\n", "    \n", "    # Create cleaned mask\n", "    cleaned_rv_wall = np.zeros_like(rv_wall)\n", "    \n", "    # Check each component for adjacency to blood\n", "    for label in range(1, num_components + 1):\n", "        component = (wall_components == label)\n", "        \n", "        # Check if any part of this component is adjacent to blood\n", "        is_adjacent = False\n", "        comp_coords = np.where(component)\n", "        \n", "        for i, j, k in zip(*comp_coords):\n", "            # Check 6-connected neighborhood\n", "            for di, dj, dk in [(0,0,1), (0,0,-1), (0,1,0), (0,-1,0), (1,0,0), (-1,0,0)]:\n", "                ni, nj, nk = i+di, j+dj, k+dk\n", "                if (0 <= ni < data.shape[0] and \n", "                    0 <= nj < data.shape[1] and \n", "                    0 <= nk < data.shape[2] and \n", "                    rv_blood[ni,nj,nk]):\n", "                    is_adjacent = True\n", "                    break\n", "            if is_adjacent:\n", "                break\n", "        \n", "        # If component is adjacent to blood, keep it\n", "        if is_adjacent:\n", "            cleaned_rv_wall |= component\n", "    \n", "    return cleaned_rv_wall\n", "\n", "def create_tissue_groups(data):\n", "    # Initialize masks for each group\n", "    cardiac_mask = np.zeros_like(data, dtype=bool)\n", "    lung_mask = np.zeros_like(data, dtype=bool)\n", "    tissue_mask = np.zeros_like(data, dtype=bool)\n", "    air_mask = np.zeros_like(data, dtype=bool)\n", "    \n", "    # Clean RV wall mask\n", "    cleaned_rv_wall = clean_rv_wall_mask(data)\n", "    \n", "    # Cardiac tissues (labels 1, 5, 6 and cleaned label 2)\n", "    cardiac_labels = [1, 3, 4, 5, 6, 7, 8, 50] #include pericardiac region\n", "    for label in cardiac_labels:\n", "        cardiac_mask |= (data == label)\n", "    # Add cleaned RV wall\n", "    cardiac_mask |= cleaned_rv_wall\n", "\n", "    # FIXED: Add removed parts of RV wall to tissue mask\n", "    removed_rv_wall = (data == 2) & ~cleaned_rv_wall\n", "    tissue_mask |= removed_rv_wall\n", "    \n", "    # Lung (label 15,16)\n", "    lung_labels = [15, 16]\n", "    for label in lung_labels:\n", "        lung_mask |= (data == label)\n", "    \n", "    # All other labeled tissues\n", "    all_labels = np.unique(data)\n", "    other_tissue_labels = [label for label in all_labels \n", "                          if label not in cardiac_labels + [0, 2, 15, 16]]\n", "    for label in other_tissue_labels:\n", "        tissue_mask |= (data == label)\n", "    \n", "    # Air (unlabeled regions, label 0)\n", "    air_mask = (data == 0)\n", "    \n", "    return cardiac_mask, lung_mask, tissue_mask, air_mask\n", "\n", "\n", "# First, compute the cleaned RV wall mask using your existing function:\n", "cleaned_rv = clean_rv_wall_mask(data_input)\n", "\n", "# Create a copy of the original data_input:\n", "updated_data_input = data_input.copy()\n", "\n", "# Identify the voxels that were originally RV wall (label 2) but are removed in the cleaning process.\n", "removed_rv_wall = (data_input == 2) & (~cleaned_rv)\n", "\n", "# Determine the body label from tissue_types; change the key to match your definition.\n", "body_label = tissue_types.get('body', 9)  # fallback to 99 if 'body' is not defined\n", "\n", "# Update these voxels to body\n", "updated_data_input[removed_rv_wall] = body_label\n", "\n", "# Create the tissue group masks\n", "cardiac_mask, lung_mask, tissue_mask, air_mask = create_tissue_groups(data_input)\n", "\n", "# Visualization\n", "plt.figure(figsize=(15, 10))\n", "\n", "# Plot all four masks\n", "plt.subplot(221)\n", "plt.imshow(cardiac_mask[:,:,best_slice], cmap='gray')\n", "plt.title('Cardiac Tissues\\n(Labels 1,2,5,6)')\n", "plt.axis('off')\n", "\n", "plt.subplot(222)\n", "plt.imshow(lung_mask[:,:,best_slice], cmap='gray')\n", "plt.title('Lung Tissue\\n(Label 16)')\n", "plt.axis('off')\n", "\n", "plt.subplot(223)\n", "plt.imshow(tissue_mask[:,:,best_slice], cmap='gray')\n", "plt.title('Other Tissues\\n(Remaining Labels)')\n", "plt.axis('off')\n", "\n", "plt.subplot(224)\n", "plt.imshow(air_mask[:,:,best_slice], cmap='gray')\n", "plt.title('Air\\n(Label 0)')\n", "plt.axis('off')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Print volume statistics\n", "print(\"\\nVolume statistics:\")\n", "print(f\"Cardiac tissues: {cardiac_mask.sum()} voxels\")\n", "print(f\"Lung tissue: {lung_mask.sum()} voxels\")\n", "print(f\"Other tissues: {tissue_mask.sum()} voxels\")\n", "print(f\"Air: {air_mask.sum()} voxels\")\n", "\n", "# Interactive slice viewer for all masks\n", "def update_group_viewer(slice_idx):\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "    \n", "    # Plot cardiac mask\n", "    axes[0,0].imshow(cardiac_mask[:,:,slice_idx], cmap='gray')\n", "    axes[0,0].set_title('Cardiac Tissues\\n(Labels 1,2,5,6)')\n", "    axes[0,0].axis('off')\n", "    \n", "    # Plot lung mask\n", "    axes[0,1].imshow(lung_mask[:,:,slice_idx], cmap='gray')\n", "    axes[0,1].set_title('Lung Tissue\\n(Label 16)')\n", "    axes[0,1].axis('off')\n", "    \n", "    # Plot other tissues mask\n", "    axes[1,0].imshow(tissue_mask[:,:,slice_idx], cmap='gray')\n", "    axes[1,0].set_title('Other Tissues\\n(Remaining Labels)')\n", "    axes[1,0].axis('off')\n", "    \n", "    # Plot air mask\n", "    axes[1,1].imshow(air_mask[:,:,slice_idx], cmap='gray')\n", "    axes[1,1].set_title('Air\\n(Label 0)')\n", "    axes[1,1].axis('off')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Create interactive slider\n", "interact(update_group_viewer, \n", "        slice_idx=widgets.IntSlider(\n", "            min=0, \n", "            max=data_input.shape[2]-1, \n", "            step=1, \n", "            value=best_slice,\n", "            description='Slice:',\n", "            continuous_update=False\n", "        ))\n", "\n", "def compare_rv_wall_masks(data):\n", "    \"\"\"Create interactive viewer to compare original and cleaned RV wall masks\"\"\"\n", "    original_rv_wall = (data == 2)\n", "    cleaned_rv_wall = clean_rv_wall_mask(data)\n", "    \n", "    def update_comparison(slice_idx):\n", "        fig, axes = plt.subplots(1, 3, figsize=(15, 5))\n", "        \n", "        # Plot original RV wall mask\n", "        axes[0].imshow(original_rv_wall[:,:,slice_idx], cmap='gray')\n", "        axes[0].set_title(f'Original RV Wall\\nSlice {slice_idx}\\n{original_rv_wall[:,:,slice_idx].sum()} pixels')\n", "        axes[0].axis('off')\n", "        \n", "        # Plot RV blood for reference\n", "        axes[1].imshow((data[:,:,slice_idx] == 6), cmap='gray')\n", "        axes[1].set_title(f'RV Blood\\nSlice {slice_idx}')\n", "        axes[1].axis('off')\n", "        \n", "        # Plot cleaned RV wall mask\n", "        axes[2].imshow(cleaned_rv_wall[:,:,slice_idx], cmap='gray')\n", "        axes[2].set_title(f'Cleaned RV Wall\\nSlice {slice_idx}\\n{cleaned_rv_wall[:,:,slice_idx].sum()} pixels')\n", "        axes[2].axis('off')\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "        # Print statistics for this slice\n", "        print(f\"\\nSlice {slice_idx} statistics:\")\n", "        print(f\"Original RV wall pixels: {original_rv_wall[:,:,slice_idx].sum()}\")\n", "        print(f\"Cleaned RV wall pixels: {cleaned_rv_wall[:,:,slice_idx].sum()}\")\n", "        print(f\"Removed pixels: {original_rv_wall[:,:,slice_idx].sum() - cleaned_rv_wall[:,:,slice_idx].sum()}\")\n", "    \n", "    interact(update_comparison, \n", "            slice_idx=widgets.IntSlider(\n", "                min=0, \n", "                max=data_input.shape[2]-1, \n", "                step=1, \n", "                value=best_slice,\n", "                description='Slice:',\n", "                continuous_update=False\n", "            ))\n", "    \n", "    # Print overall statistics\n", "    print(\"\\nOverall statistics:\")\n", "    print(f\"Total original RV wall voxels: {original_rv_wall.sum()}\")\n", "    print(f\"Total cleaned RV wall voxels: {cleaned_rv_wall.sum()}\")\n", "    print(f\"Total removed voxels: {original_rv_wall.sum() - cleaned_rv_wall.sum()}\")\n", "    print(f\"Percentage of RV wall removed: {(1 - cleaned_rv_wall.sum()/original_rv_wall.sum())*100:.1f}%\")\n", "\n", "# Run the comparison\n", "# compare_rv_wall_masks(data_input)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_cardiac_box_mask(cardiac_mask):\n", "    \"\"\"\n", "    Create a bounding box mask around the cardiac tissues\n", "    \n", "    Parameters:\n", "    -----------\n", "    cardiac_mask : n<PERSON><PERSON>\n", "        3D binary mask of cardiac tissues\n", "    \n", "    Returns:\n", "    --------\n", "    n<PERSON><PERSON>\n", "        3D binary mask with a box surrounding all cardiac tissues\n", "    \"\"\"\n", "    # Find the coordinates of non-zero elements (cardiac tissues)\n", "    coords = np.where(cardiac_mask)\n", "    \n", "    if len(coords[0]) == 0:  # No cardiac tissues found\n", "        return np.zeros_like(cardiac_mask), None, None, None, None, None, None\n", "    \n", "    # Get the min and max coordinates to create bounding box\n", "    min_x, max_x = np.min(coords[0]), np.max(coords[0])\n", "    min_y, max_y = np.min(coords[1]), np.max(coords[1])\n", "    min_z, max_z = np.min(coords[2]), np.max(coords[2])\n", "    \n", "    # Create box mask\n", "    box_mask = np.zeros_like(cardiac_mask)\n", "    box_mask[min_x:max_x+1, min_y:max_y+1, min_z:max_z+1] = 1\n", "    \n", "    return box_mask, min_x, max_x, min_y, max_y, min_z, max_z\n", "\n", "# Create the cardiac box mask and get the box coordinates\n", "cardiac_box_mask, min_x, max_x, min_y, max_y, min_z, max_z = create_cardiac_box_mask(cardiac_mask)\n", "\n", "# Visualization of the cardiac box mask\n", "def compare_cardiac_and_box(data, min_x, max_x, min_y, max_y, min_z, max_z):\n", "    \"\"\"Create interactive viewer to compare cardiac mask and cardiac box mask\"\"\"\n", "    \n", "    def update_comparison(slice_idx):\n", "        fig, axes = plt.subplots(1, 3, figsize=(15, 5))\n", "        \n", "        # Plot original data\n", "        axes[0].imshow(data[:,:,slice_idx], cmap='gray')\n", "        axes[0].set_title(f'Original Data\\nSlice {slice_idx}')\n", "        axes[0].axis('off')\n", "        \n", "        # Plot cardiac mask\n", "        axes[1].imshow(cardiac_mask[:,:,slice_idx], cmap='gray')\n", "        axes[1].set_title(f'Cardiac Tissues\\nSlice {slice_idx}')\n", "        axes[1].axis('off')\n", "        \n", "        # Plot cardiac box mask\n", "        axes[2].imshow(cardiac_box_mask[:,:,slice_idx], cmap='gray')\n", "        axes[2].set_title(f'Cardiac Box Mask\\nSlice {slice_idx}')\n", "        axes[2].axis('off')\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "        # Print statistics for this slice\n", "        print(f\"\\nSlice {slice_idx} statistics:\")\n", "        print(f\"Cardiac mask pixels: {cardiac_mask[:,:,slice_idx].sum()}\")\n", "        print(f\"Cardiac box mask pixels: {cardiac_box_mask[:,:,slice_idx].sum()}\")\n", "        print(f\"Box to cardiac ratio: {cardiac_box_mask[:,:,slice_idx].sum() / max(1, cardiac_mask[:,:,slice_idx].sum()):.2f}x\")\n", "    \n", "    interact(update_comparison, \n", "            slice_idx=widgets.IntSlider(\n", "                min=0, \n", "                max=data.shape[2]-1, \n", "                step=1, \n", "                value=best_slice,\n", "                description='Slice:',\n", "                continuous_update=False\n", "            ))\n", "    \n", "    # Print overall statistics\n", "    print(\"\\nOverall statistics:\")\n", "    print(f\"Total cardiac voxels: {cardiac_mask.sum()}\")\n", "    print(f\"Total cardiac box voxels: {cardiac_box_mask.sum()}\")\n", "    print(f\"Box to cardiac ratio: {cardiac_box_mask.sum() / cardiac_mask.sum():.2f}x\")\n", "    if min_x is not None:  # Check if coordinates were found\n", "        print(f\"Box dimensions: {max_x-min_x+1} x {max_y-min_y+1} x {max_z-min_z+1} voxels\")\n", "    else:\n", "        print(\"No cardiac tissues found.\")\n", "\n", "# Run the comparison\n", "compare_cardiac_and_box(data_input, min_x, max_x, min_y, max_y, min_z, max_z)\n", "\n", "# Optional: Save the cardiac box mask for later use\n", "# np.save('cardiac_box_mask.npy', cardiac_box_mask)\n", "\n", "# If you need to visualize the box boundaries on the original data\n", "def visualize_box_boundaries(data, box_mask, min_x, max_x, min_y, max_y, min_z, max_z):\n", "    \"\"\"Visualize the box boundaries overlaid on the original data\"\"\"\n", "    \n", "    def update_view(slice_idx):\n", "        fig, ax = plt.subplots(1, 1, figsize=(8, 8))\n", "        \n", "        # Plot original data\n", "        ax.imshow(data[:,:,slice_idx], cmap='gray')\n", "        \n", "        # Only add box if this slice is within the box range and coordinates were found\n", "        if min_z is not None and min_z <= slice_idx <= max_z:\n", "            # Add box outline\n", "            rect = patches.Rectangle((min_y, min_x), max_y-min_y, max_x-min_x, \n", "                                    linewidth=2, edgecolor='r', facecolor='none')\n", "            ax.add_patch(rect)\n", "        \n", "        ax.set_title(f'Cardiac Box Overlay\\nSlice {slice_idx}')\n", "        ax.axis('off')\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "    \n", "    interact(update_view, \n", "            slice_idx=widgets.IntSlider(\n", "                min=0, \n", "                max=data.shape[2]-1, \n", "                step=1, \n", "                value=best_slice,\n", "                description='Slice:',\n", "                continuous_update=False\n", "            ))\n", "\n", "# Import the necessary module for rectangles\n", "from matplotlib import patches\n", "\n", "# Run the box boundary visualization\n", "visualize_box_boundaries(data_input, cardiac_box_mask, min_x, max_x, min_y, max_y, min_z, max_z)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Visualizing and Adjusting the Pericardium"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import numpy as np\n", "# import matplotlib.pyplot as plt\n", "# from scipy import ndimage\n", "# import ipywidgets as widgets\n", "# from ipywidgets import interact, interactive_output, HBox, VBox, Layout\n", "# from IPython.display import display\n", "\n", "# # Function to create the pericardium mask from label 50\n", "# def create_pericardium_mask(data):\n", "#     \"\"\"Extract the existing pericardium mask (label 50)\"\"\"\n", "#     return data == 50\n", "\n", "# # Create adaptive thinning function that preserves 3D closure\n", "# def create_thin_pericardium_preserving_closure(data, thinning_factor=0.2):\n", "#     \"\"\"\n", "#     Create a thinner pericardium while preserving its closed 3D structure\n", "    \n", "#     Parameters:\n", "#     -----------\n", "#     data : ndarray\n", "#         3D array with tissue labels\n", "#     thinning_factor : float\n", "#         Factor to control thinning (0-1), higher = thinner\n", "    \n", "#     Returns:\n", "#     --------\n", "#     modified_data : ndarray\n", "#         Modified data with thinned pericardium\n", "#     thinned_mask : n<PERSON>ray\n", "#         Binary mask of the thinned pericardium\n", "#     \"\"\"\n", "#     # Make a copy of the original data\n", "#     modified_data = data.copy()\n", "    \n", "#     # Get original pericardium mask\n", "#     pericardium = (data == 50)\n", "    \n", "#     # Calculate distance transform from inside and outside\n", "#     # This gives us a measure of \"thickness\" at each point\n", "#     dist_inside = ndimage.distance_transform_edt(pericardium)\n", "#     dist_outside = ndimage.distance_transform_edt(~pericardium)\n", "    \n", "#     # The true \"thickness\" at any point is approximately the sum of \n", "#     # distance from inside + distance from outside edges\n", "#     thickness = dist_inside + dist_outside\n", "    \n", "#     # Get the maximum thickness as reference\n", "#     max_thickness = np.max(dist_inside)\n", "    \n", "#     # Create a mask of voxels to keep\n", "#     # Higher values of thinning_factor remove more voxels\n", "#     keep_mask = dist_inside >= (max_thickness * thinning_factor)\n", "    \n", "#     # Co<PERSON><PERSON> with original mask to get thinned pericardium\n", "#     # This ensures we only remove voxels from inside toward the middle\n", "#     thinned_pericardium = pericardium & keep_mask\n", "    \n", "#     # Make sure it's still a connected component\n", "#     labeled, num_features = ndimage.label(thinned_pericardium)\n", "#     if num_features > 1:\n", "#         # Keep only the largest component\n", "#         sizes = ndimage.sum(thinned_pericardium, labeled, range(1, num_features + 1))\n", "#         largest_label = np.argmax(sizes) + 1\n", "#         thinned_pericardium = (labeled == largest_label)\n", "    \n", "#     # Get voxels to remove\n", "#     voxels_to_remove = pericardium & ~thinned_pericardium\n", "    \n", "#     # Update the data\n", "#     modified_data[voxels_to_remove] = 0\n", "    \n", "#     # Calculate reduction stats\n", "#     original_volume = np.sum(pericardium)\n", "#     new_volume = np.sum(thinned_pericardium)\n", "#     reduction_percent = (1 - new_volume/original_volume) * 100\n", "    \n", "#     print(f\"Pericardium thinned by {reduction_percent:.1f}% ({original_volume} → {new_volume} voxels)\")\n", "#     print(f\"Connected components in final mask: {num_features}\")\n", "    \n", "#     return modified_data, thinned_pericardium\n", "\n", "# # Function to verify 3D closure\n", "# def verify_pericardium_closure(thinning_factor=0.4):\n", "#     \"\"\"Verify that the pericardium remains a closed mask in 3D\"\"\"\n", "#     _, thinned_pericardium = create_thin_pericardium_preserving_closure(data_input, thinning_factor)\n", "    \n", "#     # Check if there are any gaps between cardiac tissue and outside\n", "#     # First get cardiac tissues and pericardium\n", "#     cardiac = cardiac_mask\n", "    \n", "#     # Dilate cardiac tissues slightly to ensure they touch the pericardium\n", "#     cardiac_dilated = ndimage.binary_dilation(cardiac, iterations=2)\n", "    \n", "#     # Label regions outside both cardiac and pericardium\n", "#     outside_mask = ~(cardiac_dilated | thinned_pericardium)\n", "#     labeled_outside, num_outside = ndimage.label(outside_mask)\n", "    \n", "#     # Get region sizes\n", "#     outside_sizes = ndimage.sum(outside_mask, labeled_outside, range(1, num_outside + 1))\n", "    \n", "#     # Check for any regions that might indicate leaks\n", "#     largest_idx = np.argmax(outside_sizes)\n", "#     largest_size = outside_sizes[largest_idx]\n", "#     total_outside = np.sum(outside_mask)\n", "    \n", "#     print(\"Closure verification results:\")\n", "#     print(f\"- Number of outside regions: {num_outside}\")\n", "#     print(f\"- Largest outside region: {largest_size} voxels ({largest_size/total_outside*100:.1f}% of total)\")\n", "    \n", "#     if num_outside > 1:\n", "#         # There might be small isolated regions - let's see if they're significant\n", "#         other_sizes = np.delete(outside_sizes, largest_idx)\n", "#         if np.any(other_sizes > 100):  # Arbitrary threshold for significance\n", "#             print(\"⚠️ Potential leak detected - multiple significant outside regions\")\n", "#         else:\n", "#             print(\"✓ Passed - minor isolated regions only\")\n", "#     else:\n", "#         print(\"✓ Passed - single outside region\")\n", "    \n", "#     return thinned_pericardium\n", "\n", "# # Function to save the final modified data\n", "# def save_modified_data(thinning_factor=0.4, filename='data_with_thin_pericardium.npy'):\n", "#     \"\"\"Save the modified data with thinned pericardium\"\"\"\n", "#     modified_data, _ = create_thin_pericardium_preserving_closure(data_input, thinning_factor)\n", "#     np.save(filename, modified_data)\n", "#     print(f\"Modified data saved to {filename}\")\n", "#     return modified_data\n", "\n", "# # Main visualization function that accepts all parameters directly\n", "# def visualize_pericardium(thinning_factor, slice_idx, view_type):\n", "#     \"\"\"\n", "#     Visualization function with all parameters passed directly\n", "#     \"\"\"\n", "#     # Generate the thinned pericardium data\n", "#     modified_data, thinned_pericardium = create_thin_pericardium_preserving_closure(\n", "#         data_input, thinning_factor)\n", "    \n", "#     # Original pericardium\n", "#     orig_pericardium = create_pericardium_mask(data_input)\n", "    \n", "#     # Get the appropriate slice based on the view type\n", "#     if view_type == 'axial':\n", "#         orig_slice = orig_pericardium[:,:,slice_idx]\n", "#         thin_slice = thinned_pericardium[:,:,slice_idx]\n", "#         removed_slice = (orig_pericardium & ~thinned_pericardium)[:,:,slice_idx]\n", "#         img_slice = data_input[:,:,slice_idx]\n", "#         cardiac_slice = cardiac_mask[:,:,slice_idx]\n", "#     elif view_type == 'coronal':\n", "#         orig_slice = orig_pericardium[:,slice_idx,:]\n", "#         thin_slice = thinned_pericardium[:,slice_idx,:]\n", "#         removed_slice = (orig_pericardium & ~thinned_pericardium)[:,slice_idx,:]\n", "#         img_slice = data_input[:,slice_idx,:]\n", "#         cardiac_slice = cardiac_mask[:,slice_idx,:]\n", "#     else:  # 'sagittal'\n", "#         orig_slice = orig_pericardium[slice_idx,:,:]\n", "#         thin_slice = thinned_pericardium[slice_idx,:,:]\n", "#         removed_slice = (orig_pericardium & ~thinned_pericardium)[slice_idx,:,:]\n", "#         img_slice = data_input[slice_idx,:,:]\n", "#         cardiac_slice = cardiac_mask[slice_idx,:,:]\n", "    \n", "#     # Create the visualization\n", "#     fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "    \n", "#     # Original data with pericardium outlined\n", "#     axes[0, 0].imshow(img_slice, cmap='gray')\n", "#     axes[0, 0].contour(orig_slice, colors='r', linewidths=1)\n", "#     axes[0, 0].contour(cardiac_slice, colors='b', linewidths=0.5)\n", "#     axes[0, 0].set_title(f'Original Pericardium (Red)')\n", "#     axes[0, 0].axis('off')\n", "    \n", "#     # Thinned pericardium\n", "#     axes[0, 1].imshow(img_slice, cmap='gray')\n", "#     axes[0, 1].contour(thin_slice, colors='g', linewidths=1)\n", "#     axes[0, 1].contour(cardiac_slice, colors='b', linewidths=0.5)\n", "#     axes[0, 1].set_title(f'Thinned Per<PERSON>rdium (Green)')\n", "#     axes[0, 1].axis('off')\n", "    \n", "#     # Difference - what was removed\n", "#     axes[1, 0].imshow(img_slice, cmap='gray')\n", "#     axes[1, 0].contour(removed_slice, colors='y', linewidths=1)\n", "#     axes[1, 0].contour(cardiac_slice, colors='b', linewidths=0.5)\n", "#     axes[1, 0].set_title(f'Removed <PERSON><PERSON><PERSON> (Yellow)')\n", "#     axes[1, 0].axis('off')\n", "    \n", "#     # Combined view\n", "#     axes[1, 1].imshow(img_slice, cmap='gray')\n", "#     axes[1, 1].contour(orig_slice, colors='r', linewidths=1, alpha=0.5)\n", "#     axes[1, 1].contour(thin_slice, colors='g', linewidths=1.5)\n", "#     axes[1, 1].contour(cardiac_slice, colors='b', linewidths=0.5)\n", "#     axes[1, 1].set_title(f'Original (Red) vs Thinned (Green)')\n", "#     axes[1, 1].axis('off')\n", "    \n", "#     plt.suptitle(f\"{view_type.capitalize()} View - Slice {slice_idx} - Thinning {thinning_factor:.2f}\", fontsize=16)\n", "#     plt.tight_layout()\n", "#     plt.show()\n", "\n", "# # Create thinning slider\n", "# thinning_slider = widgets.FloatSlider(\n", "#     min=0.1,\n", "#     max=0.8,\n", "#     step=0.05,\n", "#     value=0.4,\n", "#     description='Thinning:',\n", "#     continuous_update=False,\n", "#     readout_format='.2f'\n", "# )\n", "\n", "# # Create slice navigation slider\n", "# slice_slider = widgets.IntSlider(\n", "#     min=0,\n", "#     max=data_input.shape[2]-1,\n", "#     step=1,\n", "#     value=best_slice,\n", "#     description='Slice:',\n", "#     continuous_update=False\n", "# )\n", "\n", "# # Create view type dropdown\n", "# view_dropdown = widgets.Dropdown(\n", "#     options=['axial', 'coronal', 'sagittal'],\n", "#     value='axial',\n", "#     description='View:',\n", "#     disabled=False\n", "# )\n", "\n", "# # Function to update slice range based on view\n", "# def update_slice_range(change):\n", "#     if change['new'] == 'axial':\n", "#         slice_slider.max = data_input.shape[2]-1\n", "#         slice_slider.value = min(slice_slider.value, slice_slider.max)\n", "#     elif change['new'] == 'coronal':\n", "#         slice_slider.max = data_input.shape[1]-1\n", "#         slice_slider.value = min(slice_slider.value, slice_slider.max)\n", "#     else:  # 'sagittal'\n", "#         slice_slider.max = data_input.shape[0]-1\n", "#         slice_slider.value = min(slice_slider.value, slice_slider.max)\n", "\n", "# view_dropdown.observe(update_slice_range, names='value')\n", "\n", "# # Add verification button\n", "# verify_button = widgets.Button(description=\"Verify 3D Closure\")\n", "\n", "# def on_verify_button_clicked(b):\n", "#     verify_pericardium_closure(thinning_slider.value)\n", "\n", "# verify_button.on_click(on_verify_button_clicked)\n", "\n", "# # Add button to save current modified data\n", "# save_button = widgets.Button(description=\"Save Modified Data\")\n", "# filename_text = widgets.Text(\n", "#     value=\"data_with_thin_pericardium.npy\",\n", "#     description=\"Filename:\",\n", "#     layout=Layout(width='350px')\n", "# )\n", "\n", "# def on_save_button_clicked(b):\n", "#     save_modified_data(thinning_slider.value, filename_text.value)\n", "\n", "# save_button.on_click(on_save_button_clicked)\n", "\n", "# # Create all controls together in a clear layout\n", "# controls = widgets.VBox([\n", "#     thinning_slider,\n", "#     widgets.HBox([view_dropdown, slice_slider]),\n", "#     widgets.HBox([verify_button]),\n", "#     widgets.HBox([filename_text, save_button])\n", "# ])\n", "\n", "# # Connect all widgets to the visualization function using interactive_output\n", "# out = widgets.interactive_output(\n", "#     visualize_pericardium, \n", "#     {\n", "#         'thinning_factor': thinning_slider,\n", "#         'slice_idx': slice_slider,\n", "#         'view_type': view_dropdown\n", "#     }\n", "# )\n", "\n", "# # Display controls and output\n", "# display(controls)\n", "# display(out)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Practice for Dilating the Pericardium Region"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import numpy as np\n", "# import matplotlib.pyplot as plt\n", "# from scipy import ndimage\n", "# import ipywidgets as widgets\n", "# from IPython.display import display\n", "\n", "# # --- Helper Functions ---\n", "\n", "# def create_pericardium_mask(data):\n", "#     \"\"\"Extract the existing pericardium mask (label 50)\"\"\"\n", "#     return data == 50\n", "\n", "# def create_reconstructed_pericardium(cardiac_mask, dilation_iter=3):\n", "#     \"\"\"\n", "#     Reconstruct a pericardium mask that surrounds the refined cardiac region.\n", "#     The new pericardium is defined as the 'shell' obtained by dilating the cardiac mask\n", "#     and subtracting the cardiac mask. A binary closing operation is applied to ensure connectivity.\n", "    \n", "#     Parameters:\n", "#       cardiac_mask : ndarray of bool\n", "#           Binary mask of the cardiac region (excluding any previous pericardium).\n", "#       dilation_iter : int\n", "#           Number of dilation iterations controlling the pericardium thickness.\n", "    \n", "#     Returns:\n", "#       new_pericardium : ndarray of bool\n", "#           The reconstructed pericardium mask.\n", "#     \"\"\"\n", "#     dilated = ndimage.binary_dilation(cardiac_mask, iterations=dilation_iter)\n", "#     new_pericardium = dilated & (~cardiac_mask)\n", "#     new_pericardium = ndimage.binary_closing(new_pericardium, structure=np.ones((3,3,3)))\n", "#     return new_pericardium\n", "\n", "# def generate_modified_data_input_reconstructed(data_input, tissue_types, cardiac_mask, dil_iter=3, lung_label=None):\n", "#     \"\"\"\n", "#     Generate a new data_input where the pericardium is replaced by a reconstructed pericardium.\n", "#     The provided cardiac_mask is first refined by excluding previous pericardium (label 50).\n", "#     Voxels in the new pericardium are set to label 50, and any voxels originally labeled 50\n", "#     but not included in the new pericardium are reassigned to lung.\n", "    \n", "#     Parameters:\n", "#       data_input   : ndarray\n", "#           Original 3D label image.\n", "#       tissue_types : dict\n", "#           Tissue names mapped to integer labels.\n", "#       cardiac_mask : ndarray of bool\n", "#           Binary mask for the cardiac region (may initially include pericardium).\n", "#       dil_iter     : int\n", "#           Number of dilation iterations to build the new pericardium.\n", "#       lung_label   : int or None\n", "#           Label for lung. If None, it is determined from tissue_types, or defaults to 15.\n", "    \n", "#     Returns:\n", "#       modified_data : ndarray\n", "#           New 3D label image with reconstructed pericardium.\n", "#       new_pericardium : ndarray of bool\n", "#           The reconstructed pericardium mask.\n", "#     \"\"\"\n", "#     # Exclude any previous pericardium from the cardiac mask \n", "#     # so that the new pericardium is built solely around the true cardiac tissue.\n", "#     refined_cardiac_mask = cardiac_mask & ~(data_input == 50) & (data_input == 3) & (data_input ==4) & (data_input==7)& (data_input==8)\n", "    \n", "#     # Reconstruct pericardium around the refined cardiac region.\n", "#     new_pericardium = create_reconstructed_pericardium(refined_cardiac_mask, dilation_iter=dil_iter)\n", "    \n", "#     modified_data = data_input.copy()\n", "#     # Assign the new pericardium label (50)\n", "#     modified_data[new_pericardium] = 50\n", "    \n", "#     # For voxels originally labeled 50 but not in the new pericardium, reassign to lung.\n", "#     removed_region = (data_input == 50) & (~new_pericardium)\n", "    \n", "#     lung_label = 16\n", "#     # Determine lung_label if not provided\n", "#     if lung_label is None:\n", "#         lung_label = None\n", "#         for key, value in tissue_types.items():\n", "#             if 'lung' in key.lower():\n", "#                 lung_label = value\n", "#                 break\n", "#         if lung_label is None:\n", "#             lung_label = 16  # fallback value\n", "    \n", "#     modified_data[removed_region] = lung_label\n", "    \n", "#     return modified_data, new_pericardium\n", "\n", "# # --- Demo Using the Interactive Viewer ---\n", "\n", "# # Here we assume you already have:\n", "# #   data_input: the original 3D labeled image,\n", "# #   tissue_types: a dictionary mapping tissue names to labels,\n", "# #   best_slice: an integer slice index for demonstration,\n", "# #   maskLabels: any additional label information,\n", "# #   cardiac_mask: a binary mask covering the cardiac region (which initially might include pericardium).\n", "\n", "# # IMPORTANT: Make sure that the provided cardiac_mask excludes noncardiac regions.\n", "# # For example, if your tissue_types define 'LV', 'RV', etc., you might compute:\n", "# # cardiac_mask = ((data_input == tissue_types['LV']) | (data_input == tissue_types['RV']) | \n", "# #                 (data_input == tissue_types['LA']) | (data_input == tissue_types['RA']))\n", "# # Adjust as needed.\n", "\n", "# # Example parameters:\n", "# dilation_iterations = 6           # Adjust thickness of the reconstructed pericardium.\n", "# thinning_factor = 0.2              # (Not used now, since we are reconstructing, not eroding)\n", "# lung_label = None                  # Let the function determine lung label from tissue_types.\n", "\n", "# # Generate the modified data_input with the reconstructed pericardium.\n", "# modified_data_input, new_pericardium = generate_modified_data_input_reconstructed(\n", "#     updated_data_input, tissue_types, cardiac_mask, dil_iter=dilation_iterations, lung_label=lung_label)\n", "\n", "# # Now, use the interactive viewer to display the modified data.\n", "# # This call demos the new labels:\n", "# # - The reconstructed pericardium is assigned label 50.\n", "# # - Voxels originally labeled 50 but not part of the new pericardium are now assigned to lung.\n", "# create_improved_interactive_viewer(modified_data_input, tissue_types, best_slice, maskLabels)\n", "\n", "# # Optionally, you can compare with the original display:\n", "# # create_improved_interactive_viewer(data_input, tissue_types, best_slice, maskLabels)\n", "\n", "# # Optionally display a static view of the reconstructed pericardium:\n", "# # Demo: Display the reconstructed pericardium mask for a specific slice\n", "# slice_to_show = 270  # or any slice index you prefer\n", "# plt.figure(figsize=(8, 6))\n", "# plt.imshow(new_pericardium[:, :, slice_to_show], cmap='gray')\n", "# plt.title(f'Reconstructed Pericardium (Slice {slice_to_show})')\n", "# plt.axis('off')\n", "# plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save mask files\n", "import os\n", "import numpy as np\n", "\n", "# Create directory for masks if it doesn't exist\n", "output_dir = \"./mask\"\n", "if not os.path.exists(output_dir):\n", "    os.makedirs(output_dir)\n", "\n", "# Save masks as NumPy files\n", "mask_files = {\n", "    'cardiac_mask': cardiac_mask,\n", "    'lung_mask': lung_mask,\n", "    'tissue_mask': tissue_mask,\n", "    'air_mask': air_mask\n", "}\n", "\n", "# Save each mask\n", "for name, mask in mask_files.items():\n", "    file_path = os.path.join(output_dir, f\"{name}.npy\")\n", "    np.save(file_path, mask)\n", "    print(f\"Saved {name} to {file_path} ({mask.sum()} voxels, shape: {mask.shape})\")\n", "\n", "print(\"\\nAll masks saved successfully!\")\n", "\n", "# Optionally: Save as NIFTI files for use in imaging software\n", "# try:\n", "#     import nibabel as nib\n", "    \n", "#     # Create affine matrix (identity for now - adjust if you have spatial information)\n", "#     affine = np.eye(4)\n", "    \n", "#     for name, mask in mask_files.items():\n", "#         nii_path = os.path.join(mask_dir, f\"{name}.nii.gz\")\n", "#         nii_img = nib.Nifti1Image(mask.astype(np.uint8), affine)\n", "#         nib.save(nii_img, nii_path)\n", "#         print(f\"Saved NIFTI file: {nii_path}\")\n", "        \n", "# except ImportError:\n", "#     print(\"\\nNote: nibabel not installed. Skipping NIFTI export.\")\n", "#     print(\"To install: pip install nibabel\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Block 4: B0 Field Calculation\n", "Implements the main B0 field synthesis:\n", "- Converts tissue masks to susceptibility maps\n", "- Performs dipole field calculation in k-space\n", "- Includes zero-padding to reduce boundary artifacts\n", "- Shows both susceptibility and B0 field maps\n", "- Provides interactive slice viewing\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from scipy.fft import fftn, ifftn\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import vtk\n", "from vtk.util.numpy_support import vtk_to_numpy\n", "import ipywidgets as widgets\n", "from ipywidgets import interact\n", "import psutil  # Add this for memory monitoring\n", "import gc     # Add this for garbage collection\n", "\n", "def create_susceptibility_map(cardiac_mask, lung_mask, tissue_mask, air_mask):\n", "    \"\"\"Create susceptibility map from masks\"\"\"\n", "    chi_map = np.zeros_like(cardiac_mask, dtype=float)\n", "    \n", "    # Assign susceptibility values (in ppm)\n", "    chi_map[cardiac_mask] = -9.0  # Cardiac tissue\n", "    chi_map[tissue_mask] = -9.0   # Other tissues\n", "    chi_map[lung_mask] = 0.1     # Mix of air and tissue; varies with inflation\n", "    chi_map[air_mask] = 0.36      # Air\n", "\n", "    chi_map_Axial = chi_map.transpose(1, 2, 0)  # Transpose to match the original orientation\n", "    chi_map_Coronal = chi_map.transpose(0, 2, 1)   # Transpose to match the original orientation\n", "    chi_map_Sagittal = chi_map # Transpose to match the original orientation\n", "    \n", "    return chi_map_Axial, chi_map_Coronal, chi_map_Sagittal\n", "\n", "def create_detailed_susceptibility_map(data_input, maskLabels,air_mask):\n", "    \"\"\"Create susceptibility map with detailed tissue-specific values\"\"\"\n", "    chi_map = np.zeros_like(data_input, dtype=float)\n", "    \n", "    # Default soft tissue (diamagnetic)\n", "    chi_map.fill(-9.0)\n", "    \n", "    # Air-containing structures (paramagnetic)\n", "    chi_map[data_input == maskLabels.airways] = 0.36\n", "    chi_map[data_input == maskLabels.trachea_bronchi] = 0.36\n", "    chi_map[data_input == maskLabels.right_lung] = 0.1\n", "    chi_map[data_input == maskLabels.left_lung] = 0.1\n", "    chi_map[air_mask] = 0.36  # External air\n", "    \n", "    # Bone (more diamagnetic than soft tissue)\n", "    chi_map[data_input == maskLabels.ribs] = -11.0\n", "    chi_map[data_input == maskLabels.cortical_bone] = -11.3\n", "    chi_map[data_input == maskLabels.spine] = -11.0\n", "    \n", "    # Blood pools (oxygen-dependent)\n", "    # Arterial blood (oxygenated)\n", "    chi_map[data_input == maskLabels.LV_blood] = -9.25\n", "    chi_map[data_input == maskLabels.LA_blood] = -9.25\n", "    chi_map[data_input == maskLabels.arteries] = -9.25\n", "    \n", "    # Venous blood (deoxygenated)\n", "    chi_map[data_input == maskLabels.RV_blood] = -8.85\n", "    chi_map[data_input == maskLabels.RA_blood] = -8.85\n", "    chi_map[data_input == maskLabels.veins] = -8.85\n", "    \n", "    # Fat-containing tissues (less diamagnetic)\n", "    chi_map[data_input == maskLabels.Peri] = -8.8  # Pericardium\n", "    chi_map[data_input == maskLabels.bone_marrow] = -8.7\n", "\n", "    chi_map_Axial = chi_map.transpose(1, 2, 0)  # Transpose to match the original orientation\n", "    chi_map_Coronal = chi_map.transpose(0, 2, 1)   # Transpose to match the original orientation\n", "    chi_map_Sagittal = chi_map # Transpose to match the original orientation\n", "    \n", "    return chi_map_Axial, chi_map_Coronal, chi_map_Sagittal\n", "\n", "def calculate_b0_field(chi_map, voxel_size, B0=3.0, pad_factor=0.1, slice_range=100):\n", "    \"\"\"Calculate B0 field with reduced data size\"\"\"\n", "    # Get center slice index\n", "    b0_field_full = np.zeros(chi_map.shape, dtype=np.float32)\n", "    center_slice = chi_map.shape[2] // 2\n", "    start_slice = center_slice - slice_range//2\n", "    end_slice = center_slice + slice_range//2\n", "    print(f\"Center slice: {center_slice}, Start slice: {start_slice}, End slice: {end_slice}\")\n", "    \n", "    start_slice = 0\n", "    end_slice = chi_map.shape[2]\n", "    # Extract central portion of the data\n", "    chi_map = chi_map[:, :, start_slice:end_slice]\n", "    print(f\"\\nReduced data shape: {chi_map.shape}\")\n", "    \n", "    # Debug input types and shapes\n", "    print(\"\\nDebug Information:\")\n", "    print(f\"Input chi_map type: {type(chi_map)}\")\n", "    print(f\"Input chi_map dtype: {chi_map.dtype}\")\n", "    print(f\"Input voxel_size type: {type(voxel_size)}\")\n", "    \n", "    # Convert to numpy arrays with explicit types\n", "    chi_map = np.asarray(chi_map, dtype=np.float32)\n", "    voxel_size = np.asarray(voxel_size, dtype=np.float32)\n", "    \n", "    # Convert units and check ranges\n", "    chi_map_SI = chi_map * 1e-6\n", "    voxel_size_m = voxel_size * 1e-2\n", "    \n", "    print(f\"\\nConversion check:\")\n", "    print(f\"chi_map_SI range: [{chi_map_SI.min():.2e}, {chi_map_SI.max():.2e}]\")\n", "    print(f\"voxel_size_m: {voxel_size_m}\")\n", "    \n", "    # Calculate padding\n", "    Nx, Ny, Nz = chi_map_SI.shape\n", "    print(f\"Original shape: {chi_map_SI.shape}\")\n", "    pad_x = int(Nx * pad_factor)\n", "    pad_y = int(Ny * pad_factor)\n", "    pad_z = int(Nz * pad_factor)\n", "\n", "    # Calculate memory requirements\n", "    padded_size = (Nx + 2*pad_x) * (Ny + 2*pad_y) * (Nz + 2*pad_z) * 8  # 8 bytes per complex64\n", "    available_mem = psutil.virtual_memory().available\n", "    \n", "    if padded_size > available_mem * 0.5:  # Use only 50% of available memory\n", "        scale_factor = np.sqrt(available_mem * 0.5 / padded_size)\n", "        pad_x = int(pad_x * scale_factor)\n", "        pad_y = int(pad_y * scale_factor)\n", "        pad_z = int(pad_z * scale_factor)\n", "        print(f\"Reducing padding to fit memory: ({pad_x}, {pad_y}, {pad_z})\")\n", "    \n", "    print(f\"Original shape: {chi_map_SI.shape}\")\n", "    print(f\"Padding sizes: ({pad_x}, {pad_y}, {pad_z})\")\n", "    \n", "    # Create padded array with explicit zero padding\n", "    padded_map = np.pad(chi_map_SI, \n", "                        ((pad_x, pad_x), (pad_y, pad_y), (pad_z, pad_z)),\n", "                        mode='constant', constant_values=0)\n", "    print(f\"Padded shape: {padded_map.shape}\")\n", "    \n", "    # Perform FFT\n", "    chi_FT = fftn(padded_map.astype(np.complex64))\n", "    # Create an array with explicit dtype\n", "    # chi_FT_complex = np.zeros_like(chi_FT, dtype=np.complex64)\n", "    # chi_FT_complex[:] = chi_FT\n", "    \n", "    # print(\"\\nFFT check:\")\n", "    # print(f\"FFT output shape: {chi_FT.shape}\")\n", "    # print(f\"FFT output dtype: {chi_FT.dtype}\")\n", "    # print(f\"FFT is finite: {np.all(np.isfinite(chi_FT))}\")\n", "\n", "\n", "    voxel_size_m = np.float32(voxel_size_m)\n", "    \n", "    # Create k-space coordinates\n", "    print(\"\\nVoxel size check:\")\n", "    print(f\"voxel_size_m type: {type(voxel_size_m)}\")\n", "    print(f\"voxel_size_m: {voxel_size_m}\")\n", "\n", "        # Get voxel sizes as scalars\n", "    dx = float(voxel_size_m[0])\n", "    dy = float(voxel_size_m[1])\n", "    dz = float(voxel_size_m[2])\n", "    \n", "    print(\"\\nVoxel size check:\")\n", "    print(f\"dx, dy, dz: {dx:.6f}, {dy:.6f}, {dz:.6f}\")\n", "    \n", "    # Create k-space coordinates using scalar values\n", "    kx = np.fft.fftfreq(Nx+2*pad_x, dx) * (2 * np.pi)\n", "    ky = np.fft.fftfreq(Ny+2*pad_y, dy) * (2 * np.pi)\n", "    kz = np.fft.fftfreq(Nz+2*pad_z, dz) * (2 * np.pi)\n", "\n", "    # # Get dimensions of padded array\n", "    # nx, ny, nz = padded_map.shape\n", "    \n", "    # # Create k-space coordinates using integer indices first\n", "    # kx_indices = np.arange(-nx//2, nx//2)\n", "    # ky_indices = np.arange(-ny//2, ny//2)\n", "    # kz_indices = np.arange(-nz//2, nz//2)\n", "    \n", "    # # Convert to physical k-space coordinates\n", "    # dx, dy, dz = [float(v) for v in voxel_size_m]\n", "    # kx = (2 * np.pi * kx_indices / (nx * dx)).astype(np.float32)\n", "    # ky = (2 * np.pi * ky_indices / (ny * dy)).astype(np.float32)\n", "    # kz = (2 * np.pi * kz_indices / (nz * dz)).astype(np.float32)\n", "    \n", "    # Create k-space grid with explicit dtype\n", "    KX, KY, KZ = np.meshgrid(kx, ky, kz, indexing='ij')\n", "    KX = KX.astype(np.float32)\n", "    KY = KY.astype(np.float32)\n", "    KZ = KZ.astype(np.float32)\n", "    \n", "    # Calculate K2 with improved numerical stability\n", "    K2 = KX**2 + KY**2 + KZ**2\n", "    K2 = K2.astype(np.float32)\n", "    \n", "    with np.errstate(divide='ignore', invalid='ignore'):\n", "        D = np.where(K2 > 0,\n", "                    1/3 - (KZ**2)/K2,\n", "                    0)\n", "        \n", "    # Ensure both arrays have compatible complex dtypes\n", "    D = D.astype(np.complex64)\n", "    \n", "    print(\"\\nArray type check before multiplication:\")\n", "    print(f\"D dtype: {D.dtype}\")\n", "    print(f\"D shape: {D.shape}\")\n", "    # print(f\"chi_FT dtype: {chi_FT.dtype}\")\n", "    print(f\"chi_FT shape: {chi_FT.shape}\")\n", "    \n", "    # Calculate field in k-space\n", "    # 直接进行乘法运算\n", "    field_FT = (D * chi_FT).astype(np.complex64)\n", "    \n", "    # print(\"\\nField calculation check:\")\n", "    # print(f\"field_FT shape: {field_FT.shape}\")\n", "    # print(f\"field_FT dtype: {field_FT.dtype}\")\n", "    \n", "    # Inverse FFT\n", "    field = ifftn(field_FT)\n", "    field = field[pad_x:-pad_x, pad_y:-pad_y, pad_z:-pad_z]\n", "    \n", "    # print(\"\\nFinal field check:\")\n", "    # print(f\"field shape: {field.shape}\")\n", "    # print(f\"field dtype: {field.dtype}\")\n", "    \n", "    # Convert to Hz\n", "    gamma = 42.577478518e6  # Hz/T\n", "    field_Hz = np.real(field) * B0 * gamma\n", "    \n", "    print(\"\\nFrequency conversion check:\")\n", "    print(f\"field_Hz shape: {field_Hz.shape}\")\n", "    # print(f\"field_Hz dtype: {field_Hz.dtype}\")\n", "    print(f\"field_Hz range: [{field_Hz.min():.2f}, {field_Hz.max():.2f}] Hz\")\n", "    \n", "    # Insert calculated field into the center portion\n", "    b0_field_full[:, :, start_slice:end_slice] = field_Hz\n", "    \n", "    \n", "    return field_Hz, b0_field_full\n", "    \n", "\n", "# Calculate B0 field\n", "B0_strength = 3  # Tesla\n", "\n", "# Create susceptibility map\n", "chi_map_Axial,  chi_map_Coronal, chi_map_Sagittal = create_susceptibility_map(cardiac_mask, lung_mask, tissue_mask, air_mask)\n", "\n", "# chi_map_Axial,  chi_map_Coronal, chi_map_Sagittal = create_detailed_susceptibility_map(data_input, maskLabels,air_mask)\n", "\n", "voxel_size = [0.1,0.1,0.1]\n", "# Calculate B0 field with reduced data\n", "slice_range = 200  # Define slice_range as a global variable\n", "print(f\"Memory available: {psutil.virtual_memory().available / 1e9:.1f} GB\")\n", "# field_Hz, b0_field = calculate_b0_field(chi_map_Axial, voxel_size, B0=B0_strength, \n", "#                             pad_factor=0.5, slice_range=slice_range)\n", "\n", "# #Interactive viewer for both maps\n", "# def update_field_viewer(slice_idx):\n", "#     \"\"\"Visualize B0 field with zero-padding for reduced slices\"\"\"\n", "#     global slice_range  # Access the global variable\n", "#     fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))\n", "    \n", "#     # Get the center slice information\n", "#     center_slice = chi_map_Axial.shape[2] // 2\n", "#     start_slice = center_slice - slice_range//2\n", "#     end_slice = center_slice + slice_range//2\n", "    \n", "#     # Add information about calculated range\n", "#     print(\"\\nSlice range information:\")\n", "#     print(f\"Full volume: {chi_map_Axial.shape[2]} slices\")\n", "#     print(f\"Calculated range: slices {start_slice} to {end_slice-1}\")\n", "#     print(f\"Zero-padded: slices 0 to {start_slice-1} and {end_slice} to {chi_map_Axial.shape[2]-1}\")\n", "\n", "#     print(\"Input shapes and values:\")\n", "#     print(f\"chi_map shape: {chi_map_Axial.shape}\")\n", "#     print(f\"voxel_size: {voxel_size}\")\n", "#     print(f\"chi_map range: [{chi_map_Axial.min():.2f}, {chi_map_Axial.max():.2f}] ppm\")\n", "#     # Plot susceptibility map\n", "#     im1 = ax1.imshow(chi_map_Axial[:,:,slice_idx], cmap='bwr', vmin=-9.0, vmax=0.36)\n", "#     plt.colorbar(im1, ax=ax1, label='Susceptibility (ppm)')\n", "#     ax1.set_title(f'Susceptibility Map\\nSlice {slice_idx}')\n", "#     ax1.axis('off')\n", "    \n", "#     # Plot B0 field if available\n", "#     if b0_field is not None:\n", "#         im2 = ax2.imshow(b0_field[:,:,slice_idx], cmap='jet')\n", "#         plt.colorbar(im2, ax=ax2, label='Frequency Shift (Hz)')\n", "        \n", "#         # Check if this slice was calculated or zero-padded\n", "#         if start_slice <= slice_idx < end_slice:\n", "#             ax2.set_title(f'B0 Field Map\\nSlice {slice_idx} (Calculated)')\n", "#         else:\n", "#             ax2.set_title(f'B0 Field Map\\nSlice {slice_idx} (Zero-padded)')\n", "#     else:\n", "#         ax2.text(0.5, 0.5, 'B0 calculation failed', ha='center', va='center')\n", "#     ax2.axis('off')\n", "    \n", "#     plt.tight_layout()\n", "#     plt.show()\n", "\n", "# # Create interactive slider with full range\n", "# interact(update_field_viewer, \n", "#         slice_idx=widgets.IntSlider(\n", "#             min=0, \n", "#             max=chi_map_Axial.shape[2]-1,  # Use full range\n", "#             step=1, \n", "#             value=chi_map_Axial.shape[2]//2,  # Start at center slice\n", "#             description='Slice:',\n", "#             continuous_update=False,\n", "#             style={'description_width': 'initial'}\n", "#         ))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import ipywidgets as widgets\n", "from ipywidgets import interact\n", "import os\n", "\n", "# Load the saved results\n", "output_dir = \"/Users/<USER>/Documents/important document backup/Local python code/MRXCAT2/mrxcat-2.0-main/B0_output\"\n", "field_Hz_file = \"field_Hz_20250327_105540.npy\"  # 使用您的实际文件名\n", "b0_field_file = \"b0_field_20250327_105540.npy\"  # 使用您的实际文件名\n", "\n", "# field_Hz_file = \"field_Hz_20250326_142439.npy\"  # 使用您的实际文件名\n", "# b0_field_file = \"b0_field_20250326_142439.npy\"  # 使用您的实际文件名\n", "\n", "field_Hz = np.load(os.path.join(output_dir, field_Hz_file))\n", "b0_field = np.load(os.path.join(output_dir, b0_field_file))\n", "\n", "print(f\"Loaded field_Hz: {field_Hz.shape}\")\n", "print(f\"Loaded b0_field: {b0_field.shape}\")\n", "\n", "# # Interactive viewer for both maps\n", "def update_field_viewer(slice_idx):\n", "    \"\"\"Visualize B0 field with zero-padding for reduced slices\"\"\"\n", "    global slice_range  # Access the global variable\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))\n", "    \n", "    # Get the center slice information\n", "    center_slice = chi_map_Axial.shape[2] // 2\n", "    start_slice = center_slice - slice_range//2\n", "    end_slice = center_slice + slice_range//2\n", "    \n", "    # Add information about calculated range\n", "    print(\"\\nSlice range information:\")\n", "    print(f\"Full volume: {chi_map_Axial.shape[2]} slices\")\n", "    print(f\"Calculated range: slices {start_slice} to {end_slice-1}\")\n", "    print(f\"Zero-padded: slices 0 to {start_slice-1} and {end_slice} to {chi_map_Axial.shape[2]-1}\")\n", "\n", "    print(\"Input shapes and values:\")\n", "    print(f\"chi_map shape: {chi_map_Axial.shape}\")\n", "    print(f\"voxel_size: {voxel_size}\")\n", "    print(f\"chi_map range: [{chi_map_Axial.min():.2f}, {chi_map_Axial.max():.2f}] ppm\")\n", "    # Plot susceptibility map\n", "    im1 = ax1.imshow(chi_map_Axial[:,:,slice_idx], cmap='bwr', vmin=-9.0, vmax=0.36)\n", "    plt.colorbar(im1, ax=ax1, label='Susceptibility (ppm)')\n", "    ax1.set_title(f'Susceptibility Map\\nSlice {slice_idx}')\n", "    ax1.axis('off')\n", "    \n", "    # Plot B0 field if available\n", "    if b0_field is not None:\n", "        im2 = ax2.imshow(b0_field[:,:,slice_idx], cmap='jet')\n", "        plt.colorbar(im2, ax=ax2, label='Frequency Shift (Hz)')\n", "        \n", "        # Check if this slice was calculated or zero-padded\n", "        if start_slice <= slice_idx < end_slice:\n", "            ax2.set_title(f'B0 Field Map\\nSlice {slice_idx} (Calculated)')\n", "        else:\n", "            ax2.set_title(f'B0 Field Map\\nSlice {slice_idx} (Zero-padded)')\n", "    else:\n", "        ax2.text(0.5, 0.5, 'B0 calculation failed', ha='center', va='center')\n", "    ax2.axis('off')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Create interactive slider with full range\n", "interact(update_field_viewer, \n", "        slice_idx=widgets.IntSlider(\n", "            min=0, \n", "            max=chi_map_Axial.shape[2]-1,  # Use full range\n", "            step=1, \n", "            value=chi_map_Axial.shape[2]//2,  # Start at center slice\n", "            description='Slice:',\n", "            continuous_update=False,\n", "            style={'description_width': 'initial'}\n", "        ))\n", "\n", "# 打印B0场的统计信息\n", "print(f\"B0 field statistics:\")\n", "print(f\"Min: {b0_field.min():.2f} Hz\")\n", "print(f\"Max: {b0_field.max():.2f} Hz\")\n", "print(f\"Mean: {b0_field.mean():.2f} Hz\")\n", "print(f\"Std: {b0_field.std():.2f} Hz\")\n", "\n", "print(\"\\nFrequency conversion check:\")\n", "print(f\"field_Hz shape: {field_Hz.shape}\")\n", "# print(f\"field_Hz dtype: {field_Hz.dtype}\")\n", "print(f\"field_Hz range: [{field_Hz.min():.2f}, {field_Hz.max():.2f}] Hz\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Convert axial B0 field to other views\n", "b0_field_Sagittal = b0_field.transpose(2, 0, 1)   # Transpose to match coronal orientation\n", "b0_field_Coronal = b0_field.transpose(2, 1, 0)  # Transpose to match sagittal orientation\n", "\n", "# Viewer functions for each orientation\n", "def update_field_viewer_axial(slice_idx):\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))\n", "    \n", "    im1 = ax1.imshow(chi_map_Axial[:,:,slice_idx], cmap='bwr', vmin=-9.0, vmax=0.36)\n", "    plt.colorbar(im1, ax=ax1, label='Susceptibility (ppm)')\n", "    ax1.set_title(f'Axial Susceptibility Map\\nSlice {slice_idx}')\n", "    ax1.axis('off')\n", "    \n", "    if b0_field is not None:\n", "        im2 = ax2.imshow(b0_field[:,:,slice_idx], cmap='jet',vmin=-700,vmax=300)\n", "        plt.colorbar(im2, ax=ax2, label='Frequency Shift (Hz)')\n", "        ax2.set_title(f'Axial B0 Field Map\\nSlice {slice_idx}')\n", "    else:\n", "        ax2.text(0.5, 0.5, 'B0 calculation failed', ha='center', va='center')\n", "    ax2.axis('off')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "def update_field_viewer_coronal(slice_idx):\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))\n", "    \n", "    im1 = ax1.imshow(chi_map_Coronal[:,:,slice_idx], cmap='bwr', vmin=-9.0, vmax=0.36)\n", "    plt.colorbar(im1, ax=ax1, label='Susceptibility (ppm)')\n", "    ax1.set_title(f'Coronal Susceptibility Map\\nSlice {slice_idx}')\n", "    ax1.axis('off')\n", "    \n", "    if b0_field_Coronal is not None:\n", "        im2 = ax2.imshow(b0_field_Coronal[:,:,slice_idx], cmap='jet',vmin=-700,vmax=300)\n", "        plt.colorbar(im2, ax=ax2, label='Frequency Shift (Hz)')\n", "        ax2.set_title(f'Coronal B0 Field Map\\nSlice {slice_idx}')\n", "    else:\n", "        ax2.text(0.5, 0.5, 'B0 calculation failed', ha='center', va='center')\n", "    ax2.axis('off')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "def update_field_viewer_sagittal(slice_idx):\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))\n", "    \n", "    im1 = ax1.imshow(chi_map_Sagittal[:,:,slice_idx], cmap='bwr', vmin=-9.0, vmax=0.36)\n", "    plt.colorbar(im1, ax=ax1, label='Susceptibility (ppm)')\n", "    ax1.set_title(f'Sagittal Susceptibility Map\\nSlice {slice_idx}')\n", "    ax1.axis('off')\n", "    \n", "    if b0_field_Sagittal is not None:\n", "        im2 = ax2.imshow(b0_field_Sagittal[:,:,slice_idx], cmap='jet',vmin=-700,vmax=300)\n", "        plt.colorbar(im2, ax=ax2, label='Frequency Shift (Hz)')\n", "        ax2.set_title(f'Sagittal B0 Field Map\\nSlice {slice_idx}')\n", "    else:\n", "        ax2.text(0.5, 0.5, 'B0 calculation failed', ha='center', va='center')\n", "    ax2.axis('off')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "print(\"Axial View:\")\n", "interact(update_field_viewer_axial, \n", "        slice_idx=widgets.IntSlider(\n", "            min=0, \n", "            max=chi_map_Axial.shape[2]-1, \n", "            step=1, \n", "            value=chi_map_Axial.shape[2]//2,\n", "            description='Slice:',\n", "            continuous_update=False\n", "        ))\n", "\n", "# Create interactive sliders for each view\n", "print(\"Coronal View:\")\n", "interact(update_field_viewer_coronal, \n", "        slice_idx=widgets.IntSlider(\n", "            min=0, \n", "            max=chi_map_Coronal.shape[2]-1, \n", "            step=1, \n", "            value=chi_map_Coronal.shape[2]//2,\n", "            description='Slice:',\n", "            continuous_update=False\n", "        ))\n", "\n", "print(\"Sagittal View:\")\n", "interact(update_field_viewer_sagittal, \n", "        slice_idx=widgets.IntSlider(\n", "            min=0, \n", "            max=chi_map_Sagittal.shape[0]-1, \n", "            step=1, \n", "            value=chi_map_Sagittal.shape[0]//2,\n", "            description='Slice:',\n", "            continuous_update=False\n", "        ))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Print chi_map dimensions and orientation\n", "def print_chi_map_info(chi_map_axial, chi_map_sag):\n", "    print(\"Chi map dimensions in axial view:\", chi_map_axial.shape)\n", "    print(\"Chi map dimensions in sagittal view:\", chi_map_sag.shape)\n", "    \n", "    # Print example slice dimensions\n", "    print(\"\\nSlice dimensions:\")\n", "    print(\"Axial slice shape:\", chi_map_axial[:,:,0].shape)\n", "    print(\"Sagittal slice shape:\", chi_map_sag[:,:,0].shape)\n", "    \n", "    # Print value ranges\n", "    print(\"\\nValue ranges:\")\n", "    print(\"Min susceptibility:\", np.min(chi_map_axial), \"ppm\")\n", "    print(\"Max susceptibility:\", np.max(chi_map_axial), \"ppm\")\n", "    \n", "    # Print unique values to confirm tissue types\n", "    print(\"\\nUnique susceptibility values (ppm):\", np.unique(chi_map_axial))\n", "\n", "chi_map_sag = axial_to_sagittal(chi_map)\n", "# Call this function after chi_map creation\n", "print_chi_map_info(chi_map, chi_map_sag)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Block 5: Coronal View Visualization\n", "Adds coronal view capability:\n", "- Converts axial calculations to coronal view\n", "- Shows original masks, susceptibility map, and B0 field\n", "- Includes interactive slice selection\n", "- Maintains proper coordinate system transformations\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def visualize_axial_b0_mask(data_input, cardiac_mask, chi_map_axial, b0_field_axial, best_slice, voxel_size):\n", "    \"\"\"Visualize B0 field in axial view with three separate figures\"\"\"\n", "    # Convert all inputs to appropriate types\n", "    data_input = np.asarray(data_input, dtype=np.float32)\n", "    data_axial = np.asarray(cardiac_mask, dtype=np.float32)  # Changed from bool to float32\n", "    \n", "    chi_map_axial = np.asarray(chi_map_axial, dtype=np.float32)\n", "    b0_field_axial = np.asarray(b0_field_axial, dtype=np.float32)\n", "    voxel_size_ax = np.asarray(voxel_size, dtype=np.float32)\n", "    \n", "    # Print shapes and types for debugging\n", "    print(\"Data types and shapes:\")\n", "    print(f\"data_input: {data_input.dtype}, {data_input.shape}\")\n", "    print(f\"cardiac_mask: {data_axial.dtype}, {data_axial.shape}\")\n", "    print(f\"chi_map: {chi_map_axial.dtype}, {chi_map_axial.shape}\")\n", "    print(f\"b0_field: {b0_field_axial.dtype}, {b0_field_axial.shape}\")\n", "    \n", "    # Ensure all arrays are in the same orientation (axial)\n", "    if data_axial.shape != chi_map_axial.shape:\n", "        chi_map_axial = chi_map_axial.transpose(1, 2, 0)\n", "    if data_axial.shape != b0_field_axial.shape:\n", "        b0_field_axial = b0_field_axial.transpose(1, 2, 0)\n", "    \n", "    # Adjust B0 field by subtracting median within cardiac mask\n", "    # This centers the cardiac tissue around zero frequency offset\n", "    cardiac_indices = np.where(cardiac_mask)\n", "    if len(cardiac_indices[0]) > 0:  # Check if cardiac mask contains any points\n", "        cardiac_field_values = b0_field_axial[cardiac_indices]\n", "        cardiac_median = np.median(cardiac_field_values)\n", "        print(f\"Cardiac field median: {cardiac_median:.2f} Hz\")\n", "        b0_field_adjusted = b0_field_axial - cardiac_median\n", "    else:\n", "        print(\"No cardiac tissue found in mask\")\n", "        b0_field_adjusted = b0_field_axial\n", "    \n", "    # Create masked versions using float32 multiplication\n", "    chi_map_masked = np.multiply(chi_map_axial, data_axial, dtype=np.float32)\n", "    b0_field_masked = np.multiply(b0_field_adjusted, data_axial, dtype=np.float32)\n", "    \n", "    # Calculate physical dimensions\n", "    physical_dims = np.array(data_axial.shape) * voxel_size_ax\n", "    \n", "    def update_axial_view(slice_idx):\n", "        width_inches = 6\n", "        height_inches = width_inches * (physical_dims[1]/physical_dims[0])\n", "        \n", "        # Create figure with three subplots\n", "        fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(width_inches*3, height_inches))\n", "        \n", "        # Cardiac mask\n", "        img1 = ax1.imshow(data_input[:,:,slice_idx], \n", "                         cmap='gray',\n", "                         aspect='equal',\n", "                         extent=[0, physical_dims[1], physical_dims[0], 0])\n", "        plt.colorbar(img1, ax=ax1)\n", "        ax1.set_title(f'Cardiac Mask\\nSlice {slice_idx} ({slice_idx*voxel_size_ax[2]:.1f}mm)')\n", "        ax1.set_xlabel('AP (mm)')\n", "        ax1.set_ylabel('LR (mm)')\n", "        \n", "        # Susceptibility map (masked)\n", "        img2 = ax2.imshow(chi_map_masked[:,:,slice_idx], \n", "                         cmap='bwr',\n", "                         aspect='equal',\n", "                         vmin=-9.0, vmax=0.36,\n", "                         extent=[0, physical_dims[1], physical_dims[0], 0])\n", "        plt.colorbar(img2, ax=ax2, label='ppm')\n", "        ax2.set_title(f'Susceptibility Map\\nSlice {slice_idx}')\n", "        ax2.set_xlabel('AP (mm)')\n", "        \n", "        # B0 field (masked)\n", "        img3 = ax3.imshow(b0_field_masked[:,:,slice_idx], \n", "                         cmap='jet',\n", "                         aspect='equal',\n", "                         extent=[0, physical_dims[1], physical_dims[0], 0])\n", "        plt.colorbar(img3, ax=ax3, label='Hz')\n", "        ax3.set_title(f'Adjusted B0 Field\\nSlice {slice_idx}')\n", "        ax3.set_xlabel('AP (mm)')\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "    \n", "    # Create interactive slider\n", "    max_slice = data_axial.shape[2] - 1\n", "    best_slice = min(best_slice, max_slice)\n", "    \n", "    interact(update_axial_view, \n", "            slice_idx=widgets.IntSlider(\n", "                min=0, \n", "                max=max_slice,\n", "                step=1,\n", "                value=best_slice,\n", "                description='Slice:',\n", "                continuous_update=False\n", "            ))\n", "            \n", "    return b0_field_adjusted\n", "\n", "# Call visualization with correct orientation\n", "adjusted_b0 = visualize_axial_b0_mask(data_input, cardiac_mask, chi_map_Sagittal, b0_field_Sagittal, best_slice, voxel_size)\n", "\n", "# #save adjusted_b0 to file vti\n", "# import vtk\n", "# from vtk.util import numpy_support\n", "# def save_vti(filename, data, spacing):\n", "#     \"\"\"Save 3D data to VTI file format\"\"\"\n", "#     # Create a VTK image object\n", "#     image = vtk.vtkImageData()\n", "#     image.SetDimensions(data.shape)\n", "#     image.SetSpacing(spacing)\n", "    \n", "#     # Convert numpy array to VTK array\n", "#     vtk_data = numpy_support.numpy_to_vtk(num_array=data.ravel(), deep=True, array_type=vtk.VTK_FLOAT)\n", "#     image.GetPointData().SetScalars(vtk_data)\n", "    \n", "#     # Write to file\n", "#     writer = vtk.vtkXMLImageDataWriter()\n", "#     writer.<PERSON><PERSON>ileName(filename)\n", "#     writer.SetInputData(image)\n", "#     writer.Write()\n", "# # Save adjusted B0 field to VTI file\n", "# save_vti('adjusted_b0.vti', adjusted_b0, voxel_size)\n", "np.save('adjusted_b0.npy', adjusted_b0)"]}], "metadata": {"kernelspec": {"display_name": "mrxcat20", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 2}