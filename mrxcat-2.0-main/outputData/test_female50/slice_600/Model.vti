<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.2" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="58"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="1"                    offset="6580"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAABGAAAAlQEAAM8BAAAhAgAAQgIAAJACAABpAgAAFgEAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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CAAAAACAAACQUAAANAAAADQAAAA0AAAA9QAAADQAAAA0AAAANAAAACoAAAA=eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJzt2kkOgzAQRUF8/0tny9ROsgBLv6tWLC09GTywbQAAAAAAANDN2Fs9GJ53DC18vtvAsier045N90STqr7vmSY9reoy/TLJRY9StzwnFz3FH8k1z/A1+f5Z9AhVx1NlzYMUHS8TW/Mc9x1v3uWixyjPXM+FNQ9yKXm/YtM8WLFI1zxYsS/TPFe1Fdc8Vnn6onmq+sBN81hlWsljlWk1TzVPrnmiMqzmscqukseaX62+OxZeMX+za57I17yh6XX624NhIc3bkbyd+gyeTMUvFOSSvJsxNG9kHK0eDo8bkrcjeUOKN6R4R4oDAAAAALDYB+hRAgF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACCIP+vbkhAAQAAAAAAAAAAAAAAAAAAAAAAAABvBlCQAAE=
  </AppendedData>
</VTKFile>
