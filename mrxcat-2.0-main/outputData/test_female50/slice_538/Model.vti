<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.076" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="52"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="0"                    offset="6848"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAABYAAAARgEAAOMBAAA3AgAAxAIAAF4DAACDAgAAiQAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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CAAAAACAAACQUAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAACoAAAA=eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAIIg/69uSEABAAAAAAAAAAAAAAAAAAAAAAAAAG8GUJAAAQ==
  </AppendedData>
</VTKFile>
