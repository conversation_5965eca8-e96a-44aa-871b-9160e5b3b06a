<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.214" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="58"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="1"                    offset="6620"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAABKAAAASgEAAP4BAAD3AQAAZgIAALkCAACLAgAACAEAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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CAAAAACAAACQUAAANAAAADQAAAA0AAAA8gAAADQAAAA0AAAANAAAACoAAAA=eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJzt2csKgzAURdH6/z/dQgu1eq8dmcDJWlMngU3M6/EAAAAAAAAAWMH2MnsMjLPtzB4LAxxKCx+vLqx6sHZOi56qn89meqirsJb1SBdV7eJD/U8uepp2/+a8Hqs5ph2MHxc3Koqekose5lS0SK55mEPRKrnoYX6D1sk1z7Kdnld2mTUP9S16ntmah/okrX7mJnq0ev3WPNZW/NffHzRP1e7RNQ/WpNU8WFNW8mBXyTUPpfmCLOfrKdK6kknXvqXPGAxjdG/pc0bDDJovR/LVNJex5Oou4AlVv6uSTPL1SL4cyVekOAAAAAAAAPd6Ah+rAeB4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACCIP+vbkhAAQAAAAAAAAAAAAAAAAAAAAAAAABvBlCQAAE=
  </AppendedData>
</VTKFile>
