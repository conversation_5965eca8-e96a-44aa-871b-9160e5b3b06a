<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.108" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="52"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="0"                    offset="7020"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAABWAAAAjAEAAFcCAABHAgAATAIAAF4DAACkAgAAmQAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3VMQ0AIAxFwUpAAlbwbwoLJC1D4S75azu+CAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHjdWPkBwE8q2tltANBVRQdncjdvn/6p6fsG46MxVnic7dsxTsMwGIbhHiFHYGDkEu3M0CNwNEaOyUJEFOz0t2s7QXke6ZuqSmk6vHWkXi4AMMJ0ze9lpx3hmrauYR4A7OlI7S7p56PtdY0AMNIRWzhyvT8DAPSU68/7Lb0jdvyzYq277hk8AHtIdSzX8FY937PfNW2v6byuAzDKiI7XtvqeWa+W92j71ns0HYAW1n1p0fAWZ+5cx+dtNfirYr27vryP0/XvPdV0AGqkGvRMx1s9O3+m5TUdL2l79PojTc/dYwCIynWntOMtGx7peK7lrToe6XrJ54me153VASi11Zpoy1t3/Jlzea+Wb3W9RdMfndM1HYCcyLlxvY/V9mp5qucjWt6i6ZHfT5oOQFRJy9cdT+1MPY80/fWWXq7rkZ5rOgBLrVveo+tbLZ9+Gvp2+92Rep5rearp83cxaToABaItL+1466ZvtfwIPV83PdryXNPnPfpPgaYDMKLlPZu+fG3Z8iP3/L5YpOfrpq+/Mz0HOLeRLW/53D23/9Dze2XPU9N0zuMbAydABXic7do9boNAEEBhDpCCI1CkzCWW2gVHyNFcpszxUrqwV1mNZndnfwAD70kjJZFi2UHiYyDDQETxRjcMU2a+O874ms9Zn9HVz9esz4/bdu7BaJ93EdPyN5DHyv+ciIiuk8XycQXPY5a3uh7zfEvT767cc2l66efWPMd0IqJrFDpwm58T2/V6em6xvMY16Xdo5VauS8u38Nwfu/D4YToR0XXSPJcmLK/Zw/IS11KWS8/XMl2zfGvP/fHDcyKia+Qt/xNzCyxZ3P6e52zT7qtrVmo7evi95nBPy7VnDGt6julERNco5/mizB6Wp2wrsTy2p+dMt9pu8Vwz3bve8pxBeq7t6JhORHS+pOUf8//XMcv39FzzrcbyFs9zplssT5ne6rm8LmNHJyI6d/7cHloeeh6z/J08X8ty6aTV9hLLLabH3mPq9bRrM3Z0IqLzFu7m3nJvQMryHqa3eD7NbZbnPE/5mdvbSy1PuV7yvmKep0wnIqLj58/p0nPLbv6unlstrzW9xPUSxy3PD0pMt3qO6URExy+0XN6btVi+t+etlnvPU36m3G/xuqfluR2d5+hEROdN7ubynG/1vNb0qcHyVs9zNlp//909l8d2crrnmE5EdNxGcX6vtby35zlLY57XWl57z77E1ZjVvSyPvd6UGDwnIjp+ufN9qec1pqf28608jxnaa8+3Or2H55hORHT8Uuf7Gst7e54yXfO81t9f95way63/S9fD85rrB3Z0IqJzt8ZuXmN6zX4ee37eYnmr55Zn7C17vPU6InZ/H8+JiM7Z6B7ziyaKeJzt20FOwzAQQNEcgEWP0EUXLLhEc4IcgSP2eF2iCCIZy/HMOB47bv+XZgMlCUTyww1M0+U+TdfELBXmWznXeZpuilmC2T62fu1XMItiwtc/7v/Heizr+aRZ78c28ee0xw6PEU7qPocTv56IiM6ftM7X8Fxr+nWWTY8dK/U8ts/qean1FpePWI7nRETvVQvLtaZLnu9Zfqtsecp07WjPrbm2lOcl+3ur6XhORDRWrfbmWtM1+/PUWPbmWstLTT+D55LleE5E9Frtre/Pv/HwXGO61XMvy0tM177n3tvzS3Cfn4LnmE5EdO56eZ4z3bpHD/fmHpbnbN/zVnN+y7V6WL55/jH/Tmg6nhMRjVNPy6U9+jo1LY+NLLU8nu3nJ5lu3cf39pz33ImIxinn+bq+e3tew/TQ0taWh6ZbzdVca8lx8ZyI6P1Kef68t/W81PR4X9zL8lLTvTy3WJ7yPPeeOxERna94rY735i0915iessjqo5flsesahz08t1qO50RE43c2zyXTc55rbWzh+SNznRbPtb8X1PB8u+eS55hORHS+Upa3fnZuMd3qecq5Vp4/EteYszznsqflsen8zxoR0VjFa3SPv23Xur5nkNbH8Gtae67dkx/1/Ijl8f3HcyKicRrB80UwaM9JybnYXC/Prc/Kc9fs5bjkOc/QiYjOnWY97225ZHrt/aqX5RbPNd9rLcPxnIho7DTreW/DNaZ771mPGF/iuYfTeE5E9LqN6LnGdQ/nanmueXa+nfNzzg+ee/UDK+fh43ic7ZxNTsMwEEZZcACOwJIFl6An4Kg5DsfwkiWKRKViHOfzz9jj6D3p27RVm4nH80iQ8vQEALASLx9/85rIp+PEx2+drTLvt7/5jBK/v//W201PzxpTPXBP/FkAgNWomYsrsLrPZ3i9p8/j12tc3tvp+BwArkiP+eiZq/h8pNt7+PzI4/fgcwCAPlh4wCPKLJ/t59pYrOEe1eFxVJfvefR0fG8+Dj4HAEhj5QFvM1Cd5bO93BKrNYxdrSb2+ON7j9+PzwEA2rCa/6nMRp3ls53cEqu12wyzf7+Vz8NDjj6DzwHgCljN/6N4qxWf+3C6hc9DImoPHPl8dg8DAKRone/K7PM0D9VZPtrBPdO6pmfZjNLb5yETpQfwOQCsgjq/czOuZAZ6mIlqfSs7vcTNNVHcXJOePg8nUXvAU++qKGsIANfibM8r1yz43F+Uc9+SzTD4vLx/WwMA65Pb4zmXKz5X3e6hZnzux+nqNXrO50GI2gMe+nbEeo6uCQD6kdvXpY5ucbqHuvG5H5/vaX2eTBCj9MDsvh2xljPqAoB+HO1nxc25Oef9Gr3kmGNPrpJRs38zygiXB7EHZvXsqDWcURsA9OVoL7feP6/5X/rsuq/o868oufP/fPsfZe5vhql9zmsoiNIDM3pWOffWAYA1yO1jC5+ven2+qtNjl6d8nnL4UXJrtxmn5HntoTAtfTuyP2uj/j09ukYA6MfZ/u99r92T00uP+8ydHtPT5Wde3wZE9U8oSKoHvn9z1rMje7MlJffIRtYJ4JMfE36/x3ic7dtBjtswDAXQWXTRxSx6hF7FWXcxR80xcoQeI8ssB0ZrgBFISqRkUTT+B4hOOxOPZVt8tpJ+fGTNr+29fhuqfK1lG9Jro8bN7ffrf+1ffyWrv0zRsf649RV3/O7Oqm1Hula4ehqrfP1rez/vEdeqZby165i7rrUxzRwngiBjo/WCsyzP5vlevb7OrjMt10w/ft+9sXrtovV0VLmNaM9HHAdunsF0BLl+pD5wtufSNiLGLe373tM/b//+3L8/ytoZJXk+yvLSdG49gP7eO1Mj7Drq6Sy6jdeW33Npnlnn8YyxIggyNrVe0NoHLK/N5vlheqTPltJcHeW45HeL6b1mlaV5rVW5HXr/ltFzbZ7BcwS5flr6wRmWw/Pca+0Wz8t9GFk1s7Wi2zkMP8511HtDnmPQMs961txmzksEQfzxmDzC8tl9Uhp3zfPymS3a6h7P/wxea5fcPtv0HsM1z+m9WwbPe+beqvfZCIL40+N55t7R2h8zPqPP8py+h156/VD2I9JwznLqebnWPvMatR4T6zxbfU4iCNIXa294m+M3vVbuHRbPaa+Pttpq+SzPy+I895o+2vLSc22tfVXPe+6dPfMSQZD14/G85rjF9SjP6dhrxyDTM7rm5+75USM9587jY+t/Rj/D8dJy6jln+Yqej1oLg+cIcq1YeoTk9adQXtNnj91i+sqe156HqecjbefO42PjTX8oTs2wvPScrsG0ej7zehxlOR0HnZfSNuE5guRLa59oNdxjehbPac+Pttvq+b7PUZ57TJ9heel59PrR2Z5ra2h0+zPHjCDIuNR6hddxi+kZPC/7frTdLZ6XY4r0nJpO/y3Sc8sz7Yxr80zPuXnMeS4VgiDrR+oZlnV1i+nRPbN1/NJ+0r9H+231XDL9DM8l07ma5Xmv5dGej3ge1+61YTmC5I44hx2Ocz/f8tm4LJ5zFW245Lk2Huk53eO8dh5X8txzflf2XFov5+ZvbQ7DcwS5Tmqe1+7rez/rvprnWU2XPkPOHe+a5fRnvJ63mn625xks167FmuVaWe/F4TmC5I54X97htvf/rEXkKp5/NY6Dfp/6ba1WBz2ejzLde05XspzuO2d173th8BxBrpXWHtJqtLd3rjL2rKZ7zaX18/Ze9ePzDVm2FgB4nO2aMVLEMAxFU1BQUHCEvQo5AcfmOJRbMsB48ArbkiU5tpL/ZlStk5UVrZ+T7Lbd3rafeC3EBxOlY77j0xj5uW4dUcplNLUaPOSw/8WLc6Tzzpg7AMAf+jvuWQO1scraUVrHeufyPjkkTuDief8frfESF3JOH+3zCD1pcfnGhMXnAIC4HOn01daO6E7XeFbi9Pyzp/03JPWRer2Vy2yXH9mXEp/3uhw+B+DaeHit1yUrUMstgtO5XEvzom7mfJ7G15wu3UeM9rn22s3uS87l1M0Sl7ecTsfMnj8AYAweXovi8ZyITre4vOZ0yTHWPpD4vMfplmu2Qm9yPq+5mDqfc3rps1INAADnweq1aC5PePj8KKdrrwt1c83trXHc99yzkOwXLE6P7vJa73H35lKX16JVBwDAudC4LarHE7W8V3M6zSv3J5dXy9PSkPg8+WeU1z2u00o92vJ5y8fcmJbH4XMAroNkzYzucMrqTs9zSq7M3WnxOX3PqnE59bmn07mQ7mlWczntPc7VdC6SMdI6AADOi2ZdjY6X09NxXh6nzizdB4/0ec/c75XQeFbqcct3rECtzySO1ric1gIAcB3O7HCK1emld8geHm/5sne/wfld43EuT+2eiHO59BnF6r2r3TtqYuU6AACAJxanl3ybn6Pl8N712Lquj3J5KV+v81CPn8HliSOcHqEOAADgReu+UOrz2nvkkWv1qj73cHqprmdyeY7l+ZC0BhHqAAAAHng7vedd92ynz86pVs9UU+lz/MgOs/QfXA4AAI9Y19QIXo/m8+j/feuF+x+BJgAA4Ip4Ob10fznbkyWfz86n5WfpuDM6DC4HAAA73Bqpvc+c7cgoPr+6yynwOAAA6PFyOnXPbFfmTl8lH63z4TFwDF90WH3ReJzt1bERgzAQRUECCnAJtAL9F+XAsYVgjO5O3p35uW4UvGUBanvt59sKbj0+q3jDt38AgJYZm95zU7b7Wu8DgF4Vune103eW7SYAuCpr90Z0fPR9Og7A07J0MKLjGQYAvxTdtX8bADwpunOzDwBGi27fLAOATKK7WGkAUEl0NzMMAGaUuau6DQAAAAAAAAAAAADU9QaK6UDmeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQ0AAADCoPdPbQ43oAAAAAAAAAAAAAAAAAAAAAB4NkJAAAE=CAAAAACAAACQUAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAACoAAAA=eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAIIg/69uSEABAAAAAAAAAAAAAAAAAAAAAAAAAG8GUJAAAQ==
  </AppendedData>
</VTKFile>
