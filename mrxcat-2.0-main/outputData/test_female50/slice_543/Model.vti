<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.086" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="52"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="0"                    offset="6944"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAABUAAAASAEAABQCAAAoAgAAxQIAAGIDAACjAgAAiwAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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CAAAAACAAACQUAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAACoAAAA=eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAIIg/69uSEABAAAAAAAAAAAAAAAAAAAAAAAAAG8GUJAAAQ==
  </AppendedData>
</VTKFile>
