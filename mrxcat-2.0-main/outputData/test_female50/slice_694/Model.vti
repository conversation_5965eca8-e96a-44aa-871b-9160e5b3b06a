<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.3880000000000001" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="65"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="1"                    offset="4328"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAAGcAAACZAQAAEQIAAMwBAADnAAAAWAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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CAAAAACAAACQUAAANAAAADQAAAA0AAAAcAAAADQAAAA0AAAANAAAACoAAAA=eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJzt2MENwCAMBEHcf9P5kGBRACi6mQosreyHxwAAAAAAAAAAAAAAAAAAAAD+q6bbc3BMlehhvtiip2j7LXqGftI1z7A1Fz1Az6x5Bs3zuO2BVmjJY7zvGMlzrDec5DEkBwAAAAAAAAAAAKYHDQIAbHic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAIIg/69uSEABAAAAAAAAAAAAAAAAAAAAAAAAAG8GUJAAAQ==
  </AppendedData>
</VTKFile>
