<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.582" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="37"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="1"                    offset="4280"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAABAAAAAFAEAABsBAACBAQAAMgEAAAIBAAAHAQAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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CAAAAACAAACQUAAANAAAADQAAAA0AAAAXAEAAJIAAAA0AAAANAAAACoAAAA=eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJzt29EOwiAMRmF4/5fWxM1NpKQdU8LP+a40u2lyVhaNpgQAAAAAAAAAqMpPo2fAH+UN6VeR82fz/TVkZcvowfAjRWay6ysbs+vqzGOd6rI8zYkuxZWc6FKczYmuw5uc5jpovpx6XqIrCzQnugj30U5zFZHkNNdA8/XQfD2h5kRXYKWlua7YmhNdQDQ5zaeX7W/ciC6qsc7Fe5KriDcn+vyMc5xfwQoz2xq3weh50c19uBNbh7s50XW4m9NdCc1XYp3ildp0F/EK6Giejvtj7MS4QSVydc/Tvu2jB0an1sObZ7qmamuaC2sWN++B0VOjw7uj78/INBewNU++5qd1x6yOBY4ZPTeuC6amuQCarycYmuYKLjYn+sQahdv5MSnnUtNciF356yrFRbSbF5dpLsF/mJ8ujx4aXWLNqa7An5wPajLanWkuyLfi1TsAk6L5eupHN81v8QCURQl/eJzt00sKhEAQREG9/6VdOag4fmlKOiNWtUx41DB80vizvC+oHs5zt0Jr3gXF82geSPQ8h2El75PmeR40r57MS/+b+/JuaR5I80CSB5I8kOR5BA+0E1fyBCfNq+fRwGHz6nG0sSw8H5r3bpt8bl69i6bWb04QzQEAAAAAAAAAAAAAAAAAAAAAAAAAAL5uAvD1A3p4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAIIg/69uSEABAAAAAAAAAAAAAAAAAAAAAAAAAG8GUJAAAQ==
  </AppendedData>
</VTKFile>
