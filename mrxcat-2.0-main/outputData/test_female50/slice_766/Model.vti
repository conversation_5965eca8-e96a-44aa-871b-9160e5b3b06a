<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.532" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="51"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="1"                    offset="4392"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAABhAAAAHAEAAGMBAABuAQAAKwEAAEMBAADDAAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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CAAAAACAAACQUAAANAAAADQAAAA0AAAApgAAAJcAAAA0AAAANAAAACoAAAA=eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJzt1tsKg0AMRVHz/z9dtBeKtNQBx5RkrUefDmxGsiwAAAAAAAAAAAAAAAAAAAAAAAAki8hewNVC9HZCdGjAO2/Hz70hzQEA/llsN7qLrZN4yN7BVeLVXPQm4k32Fia7J46d9VPyMKZZ++6Lb7ecB1/VM+9n2es42/fWmhf1O7notRwprnopR5PLXsZIctFrGGsuegWDzUUvQPN+JO9H80lukJ4CNXic7ddBCgMhEEVBvf+ls0tg1IRAJs70r1q6ang0amvX1b+ye1p+QfNAkkeSPJDkiSRPJHkgyWO8emoe41l06Ct6WcuNlrywz8k1r2Ya131e2qyuN1xxx7x9ckYx41Zb8/KGvpLX96753sk4UV/81PZNxF9JnkfyPJInEhwAAAAAAAAAAAAAAAAAAAAAAAAAAG7kAUskAm54nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAIIg/69uSEABAAAAAAAAAAAAAAAAAAAAAAAAAG8GUJAAAQ==
  </AppendedData>
</VTKFile>
