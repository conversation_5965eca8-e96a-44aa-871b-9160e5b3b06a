<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.196" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="58"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="1"                    offset="6616"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAABFAAAAfgEAANYBAAAjAgAAawIAAJYCAABmAgAAFAEAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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CAAAAACAAACQUAAANAAAADQAAAA0AAAABAEAADQAAAA0AAAANAAAACoAAAA=eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJzt2sEOgyAURUH5/5/upk1a5FE3aHKZ2bUrkhMUnx4HAAAAAAAA7Kb9eHo1LNd1Vj5emVf1ULP9bMNHmhR1g890rbjoQS4nFz1FnfKUXPQQf5N//7hvWaxThuwqi57jYvLPH/csiqXKUYzmscYZR2c2zVMMOw6P6ZrHGIQcP5m5uEcZbOhzXs1zVQMYyWOVMzfNY5VjVs1TzZNrHqh+maJ5qnqsLnmqeXLNE5VlNY9VhpU81Ty55onmV3bNE7mbb2h2aNc802QcI3mq6uWK5sH6uOV7NnK0n08omuZ76L9qlzzfO3GTfB/t7OklsZjiG5J8Q4rvSHEAAAAAAOB2L5tEAh94nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACCIP+vbkhAAQAAAAAAAAAAAAAAAAAAAAAAAABvBlCQAAE=
  </AppendedData>
</VTKFile>
