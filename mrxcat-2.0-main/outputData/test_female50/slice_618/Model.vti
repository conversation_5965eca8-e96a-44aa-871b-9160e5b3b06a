<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.236" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="58"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="0"                    offset="6356"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAEAEAAKwBAADKAQAAawIAALoCAACFAgAAEQEAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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********+W9yzS9Jrto/6W2ystx3Nalh8ZAGoLeJzt1ztuwzAQRVEVLlNkCd5Ksv8FuUzjQgiCxBQ/jxOcA0wpUCYBXvk4gDneP16be+Ncfb5nzd/mcZqr73b7PI6359y+TevePDpm5L70nmvrADBP653cc++3PjOzRa3Pn3t+7vqIb4wRLV+1b1oOsKdR9/WsSf4f/anpPf/NZ87IfZtxjgDMl2521aan3+Wv/ib7recAGelmV2n6Tj2/uoerzw6AtdLNrtD1HVqePgM9B9hf+u6v0PfVa6f3WMsB6kp3YPeur1o3va9aDlBfugcVej577fS+ajnA/5Fuw05dT/++CgPA/tKtSH0HpN+vwgBQW7ojRscBWC/dH6PfAOwv3TvdBAAAAADYzRduV6P4eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQ0AAADCoPdPbQ43oAAAAAAAAAAAAAAAAAAAAAB4NkJAAAE=CAAAAACAAACQUAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAACoAAAA=eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAIIg/69uSEABAAAAAAAAAAAAAAAAAAAAAAAAAG8GUJAAAQ==
  </AppendedData>
</VTKFile>
