<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.546" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="70"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="1"                    offset="4480"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAABAAAAAMgEAAKABAABtAQAAUgEAAAoBAADnAAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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CAAAAACAAACQUAAANAAAADQAAAA0AAAABQEAAKIAAAA0AAAANAAAACoAAAA=eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJzt2tFqg0AQQFHn/3+6DQZqTTYjWisznvMQYpiHhcuusGSaAAAAAAAAAAAAAAAAAAAAKCy+HRuglpgdHKGQiLTohhEqiYV04n+XxjkibZ5PUEukSfMJatH8diItmk9QS2RJ1wOqV5cH1bybDUE1b2bPPhe9tl3NRa9sQ07Nm9H8fkY1H9+eD5o3Myz+88PjQ/NGPhePubfmrbyLuM7rbG8mS/483iXvZF1w1FfzvgbNF0f9pcvjBIN3ty3e0xz1Y/NrF8ifWHZ82eKvJn91L29D5kF6inp3lmveW/y+c9nc/+p1c8Dgnk3zvnbkFr24jYFFb2T3Pte8LMnvR/MTfQFRtwUIeJzt1VEKwjAQRdG6/00LfslgUlorqfPOWcGDmyHbdkuPs1YP57SPOSXvzZnnkTyQ5HlGVTVvbBZW86Ym0TVv6nDz1YP53ij65DHw5yTPI3kgyQNpHkjyQLWs5AlKWskD1LaSB9g99NUDudzgC5e8sVJY8gQlseQZhs1XD+OH3uoqHuj1qa8eAQAAAAAAAAAAAAAAAAAAAAAAAABc5QmNFwPfeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACCIP+vbkhAAQAAAAAAAAAAAAAAAAAAAAAAAABvBlCQAAE=
  </AppendedData>
</VTKFile>
