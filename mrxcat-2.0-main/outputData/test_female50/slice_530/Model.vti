<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.06" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="52"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="0"                    offset="6532"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAABTAAAALQEAALsBAAAzAgAAuAIAAEQDAAAVAgAAeQAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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CAAAAACAAACQUAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAACoAAAA=eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAIIg/69uSEABAAAAAAAAAAAAAAAAAAAAAAAAAG8GUJAAAQ==
  </AppendedData>
</VTKFile>
