<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.17" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="51"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="1"                    offset="6616"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAABGAAAAhgEAAAECAACSAQAAXwIAAA8DAACRAgAA2wAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJzt2MEKwyAURcHc///pFtpGm9hVaITnzMq4enAIitsGAAAAAAAAsJo8zZ6BO+Vt9hzcJhF9MYnoq8nJ7In4s72y6MvYG2u+ipZY88q6qhk2F72Yrmt+JNe8mPEt3c29smFdyWsbxB3++tTxqZqvHc1LO2R9fUpe3OEgbyvJ60r33tq2JK/tXFjy+vQFAAAAAAAAAAAAAAAAAACACx60ywE+eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgiD/r25IQAEAAAAAAAAAAAAAAAAAAAAAAAAAbwZQkAAB
  </AppendedData>
</VTKFile>
