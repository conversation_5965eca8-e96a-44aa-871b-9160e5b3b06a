<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.072" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="52"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="0"                    offset="6852"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAABUAAAAPgEAAN4BAABjAgAA3AIAAGIDAABTAgAAhAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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CAAAAACAAACQUAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAACoAAAA=eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAIIg/69uSEABAAAAAAAAAAAAAAAAAAAAAAAAAG8GUJAAAQ==
  </AppendedData>
</VTKFile>
