<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.578" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="37"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="1"                    offset="4288"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAABIAAAAKQEAABwBAACGAQAAIwEAAPcAAAAGAQAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJzt2kmOg0AMQFF8/0v3gmassqkAiWX7v1UUWFj6cWVQpgkAAAAAAAAAoBAR7xHwQzKbH3gPg1+QQ3O6VyBr6n1976nwRdJpzrbnJjrv0fA2IzbVkxppTvVUxpLTPJPB5kTPYzQ50fOgeT00L6dfl+iZ0bwcJS7NE+vHVXgPi1fQvB6a10PyemheD2d7QUSvh+YFddOSPLmB5suNvoPiLcOnOquexWhxmmcw56R5IUvONnDbfD3biR7ausJt897Huu1+hLXtsNa3s/3eQ+OZ7dzW3r07J77vyHhoad45x7fL5+X3HBgPnfa3zU7zdK6ac7bns8s5sObrXd5j4wEjebf5/hqCMg73SU9O88jUqjTPykhO86RoXo6VvG0+Ha4hJprXYza/eD0gqkPHi8w0z0Ftrv7uSvPYzMP7/MzpMmKykl9+kUNIatH/rjTPx9pkm/fkuMtccxHjP9Dek+Oujzab5il80nxpTfPYbiTnD3Gj/gDtPglceJzt00EKwkAABMH4/0/npshCkETZuF31goFmtu2WHifN3s0lovdo3iN5j+Y9mvdIHiR5j+Y9mvdIHqR5kORBigdJHjS2lXx5Y1vJl/dB89kT+brj5rPX8RuvwG+1JV/a89MO3qN3j94AAAAAAAAAAAAAAAAAAAAAAAAAAPB3dpaCA6F4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAIIg/69uSEABAAAAAAAAAAAAAAAAAAAAAAAAAG8GUJAAAQ==
  </AppendedData>
</VTKFile>
