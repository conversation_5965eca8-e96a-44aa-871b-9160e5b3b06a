<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.216" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="58"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="1"                    offset="6568"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAABIAAAASAEAAOYBAAAbAgAAZQIAAKsCAAByAgAAAAEAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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CAAAAACAAACQUAAANAAAADQAAAA0AAAA4AAAADQAAAA0AAAANAAAACoAAAA=eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJzt2UEKhDAQRUFz/0vPQpDRdNyp8LvqBA2P2JJsGwAAAAAAAADkGbuvx+AtR27le5gqy56u7it6sFVc0WOt02oe6mZxD0c9001yv/Ch1tt8iB6qbDrO3p+KRxVNx9UXc/GgKemUXPM016ZzctHTVPeumoc7JS2Ti57mL+miuOZxjqZzZs1T7VHLk615qJtvueap1stb82CLuJonq9NqHk3zfjRvyDpvaK6rebxrXddwDSze2L4ah9dJ3o5j3k19F0usIXk7ixcXgknej+T9SN6R4gAAAAAAADzqBwKBAel4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACCIP+vbkhAAQAAAAAAAAAAAAAAAAAAAAAAAABvBlCQAAE=
  </AppendedData>
</VTKFile>
