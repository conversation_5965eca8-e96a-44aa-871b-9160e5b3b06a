<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.544" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="69"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="1"                    offset="4524"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAABAAAAAMAEAAJkBAAB7AQAAbgEAABYBAADcAAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJzt1sGKwzAMRdHq/396KHQxndgoCYVBr+csHS0CF2M9HgAAAAAAAAAAAAAAAAAAAESr+u8/4LOqmqZV7QiTVFu0qp9hkKouaT/BLNUlrXaCWfqimqe52Fz0+aovqnmYvnn1I4zSF/07IfpwJ4Jqnubqcy76eNefc82nu3PNNR/tRMxFc9En26X8daB5mHXJ9yPNwywqHupKnuXYcJNX8lzbO613rF1zdzzWdl+TPNIz6bF5vb5pHmiR+9XZ9pZq13y10BHhRHLVY7y92JrHe0Y8V1v2EJtVXfRkd5qLPtr13qpPJ/n3cc+/zt3kmp/wAxV+BIV4nO3VwQ2EMAxFQei/aY6IDaDAIgW+Zyqw9OJ4ml5pvmv04Ny3U1PzcF19FY/SlVjyLD2LrnmWo6iaBzurqnmok23WPNTl5qMH5n9H0W15LskL0rweyQuSvCDJC2rSah6vKSt5vLat5Pk6PvfRI/Kw/QMuebZtYMlL2BZWvIb1mv++AEpQHAAAAAAAAAAAAAAAAAAAAAAAAADg2xZetgPNeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACCIP+vbkhAAQAAAAAAAAAAAAAAAAAAAAAAAABvBlCQAAE=
  </AppendedData>
</VTKFile>
