<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.572" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="37"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="1"                    offset="4344"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAABMAAAAIgEAAGABAABuAQAAFQEAAAQBAAAIAQAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJzt2tsKwjAQRdHO//+0IL1EYyZprYY5s9dDEfIS2MlQsMsCAAAAAAAAAABWZrN3gP+yp9m7wB/Z3pz2Sdje3LjwOVjVnOja7GhuRvMUaJ4SzfOheRpr2g+j3axYnLlF3Kp6V39vvj1nbxS3eYnrmr1T3IXm+dA8rX5ymquheS4DvUmuheDpcMnTYbDnM9ic8EJagbnsoraWNE9jb/mWtj3iEVzrWrePwOwd41udUX4055tIFf1RXqSmuYQLzYkeWzG7B6LTXEO/dX0uZu8Z3/AS01zTh6BFV5oLKhs6s7xeQVhHXZpn0Yq6pm2vICy/uXMaENbJ0jRXQPNszhVfaC5gqHT9i+iBnbznNBdwofn2REzj17peREyd5nwSJ4jmCbnR+fRVkt/cPw8IyfkXxVujeWC9riTX0xvgNNfTu+C8w+m53pzoUbnjm+aSaP5LD+lUCPt4nO3VQQrDMAxE0eT+l+66qRxKoLjMvLfVRvCRfRx/6Xxq9+I8JnkfzQtpXkjzPpoXkrzPUFTzcM68kOZ9Fl0lT+bM+9zWlTzU4mWfR0RY3PcwkTzG2NzDHm0o68rjjWklzza0VTzeta7kDd7+c8VLLM5871L82MdvvnUbAAAAAAAAAAAAAAAAAAAAAAAAAADgey9mcgO5eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACCIP+vbkhAAQAAAAAAAAAAAAAAAAAAAAAAAABvBlCQAAE=
  </AppendedData>
</VTKFile>
