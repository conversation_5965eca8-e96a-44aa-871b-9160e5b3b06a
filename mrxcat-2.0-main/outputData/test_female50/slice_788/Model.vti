<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.576" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="37"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="1"                    offset="4292"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAABHAAAAIgEAAD0BAABxAQAAMgEAAPAAAAD9AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJzt3ckOwjAMRdHk/38aMbR0sN20VER5vmfBomUR6couggWlAAAAAAAAAACAp/rS+xT4o0rzbOqE+FnUXXOii6uWz62+J8P9zNpmfIhoSU50LW3Nqa6E5um0Jie6DprnQ/N02pMTXQXN86F5OmeS01yDF5fkuhjzhGieD83TYbfnQ/OEzLgkl2bGJbm6pujva72Pipu4D/D1kDPtCoKH9745G15BtZvbm53mCvyZNma/EF3BnNPITnNNc19ju1uX+OAuwFvu5iOdz+0K/NVurnuajy/oa47+lB7j+s4vP7Rksd3aNNdH84SuNSf6yJjzbJYVjbA0FxQVLvH3NRjUQfP1pbK9ixFFY725X3dvx5DWTfeD7t/vfXJcFTaP9D44fnCiM81FBFVpLimOSnNFbtLFK9G1ROs7jt775LjqoDXNBZ1p/no3f+MwPH+OnTmn+fBON1/Ex4EH7y4JW3ic7dFRCkBQAABB7n9pn3woEj3szAm2dppeab5qdDiXOV5keo/nPZ73eN5jeZHlPZ73eN5jeZDnQZYHWR5keZDjPTtvLf+7M89HN3K7o+ej+3jA9q7jFetZx3vs7rEbAAAAAAAAAAAAAAAAAAAAAAAAAAA+ZwEixgO3eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACCIP+vbkhAAQAAAAAAAAAAAAAAAAAAAAAAAABvBlCQAAE=
  </AppendedData>
</VTKFile>
