<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.226" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="58"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="1"                    offset="6544"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA/AAAAPwEAAMwBAAALAgAAcAIAAKACAABTAgAASgEAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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CAAAAACAAACQUAAANAAAADQAAAA0AAAAiwAAADQAAAA0AAAANAAAACoAAAA=eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJzt1NEJw0AMREFv/00HDCbhMPnzCbwzFSw8pOMAAAAAAAAAAAAAAICnJNML2Cun6RXsk8v0EDbJj+ktbJGs0aV/uyzRHfz75d70LB70DSx6jauvSy/iuxfSvI/mfTz3QpoX0ryP5oUkL6R5Ic0LaV5I8kKaN5K8kOKNJAcAAAAAAAAA4K8PvB8Bj3ic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAIIg/69uSEABAAAAAAAAAAAAAAAAAAAAAAAAAG8GUJAAAQ==
  </AppendedData>
</VTKFile>
