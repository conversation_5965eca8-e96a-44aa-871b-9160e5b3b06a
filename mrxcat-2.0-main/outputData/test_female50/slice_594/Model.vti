<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.188" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="51"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="1"                    offset="6632"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAABMAAAAhwEAAP4BAAD1AQAAfwIAAHsCAACHAgAA/AAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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CAAAAACAAACQUAAANAAAADQAAAA0AAAA+AAAADQAAAA0AAAANAAAACoAAAA=eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJzt2UEOAiEQRUG5/6WNiRsRGDcw8XfVSmbVyQsj4uMBAAAAAADAh9bunoAzWu/ugdhoWPm1FD7TfE/b8KGmSb3lQ817+moPNY/pPBfqKnn/kb939WIfLfhrPyZ/L8/MxF5Xx7fP9ZmZ2OrqR1r/5MhQbPV7cs1TaF7P+sL1+9mRodhrmHF4B6N5kK+Q8+SahxpftWoebHK7Lnmu2R8qmudabXPNIy23ueaJJC9nnVzzRLZ5PbZ5Paszu+SRVjdwmmcaNpc82yD67FhHiNYVbv0D4rSpuydjG80LkrweyQuSvCDJK1K8IMUrUhwAAAAAAID9nhbtAnl4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACCIP+vbkhAAQAAAAAAAAAAAAAAAAAAAAAAAABvBlCQAAE=
  </AppendedData>
</VTKFile>
