<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.588" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="37"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="1"                    offset="4172"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAA/AAAAC4BAAAgAQAASQEAACwBAADoAAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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CAAAAACAAACQUAAANAAAADQAAAA0AAAAWwEAAJYAAAA0AAAANAAAACoAAAA=eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJzt3NGOwjAMRFHy/z8NSxFbaCYK9bJWxvc8FZ4s3TqqKsTlAgAAAAAAAAAAbtqP7Sp5EvyP9vB+DVft1/5T9lj4mvaC5gU02Zzwptq7400AL4fkNLdH83KOyWnurtO8dxvAx1xyojuZbU50HzQviOj19OoS3VqnLItujubViLhE96Xa0tyXOsWV7HkRJsuqj9kDI0xvs7oFsidGkGg82vrskRGkDvOR7JkR0p4P6DSvYrozzW3QvB6K1yO76m+zR0bUFnOyOYtuQL5VV71pvrzZ4jT3Md98nx8rO9Wc6EsjeUGqKs19qag0t3VmzWm+tmHbLTDNzYzj9lc+e2bETDTvvJfF6h7bPP84lz0w/sDooW33i7nndfa8iJK1XxedPwM1Ile8c7hnz4q4ce/+UZ89M0I+Ts62L+9kc6Kv62xymq9rnJXmjtjzemhezz0fzUuh+RddAXnGCV94nO3USwqEQBBEQb3/pQdkwA/IqIyWVEbsepfwqB6Gdxovqt7NdZrnkTyQ5nnmiorH+AbXPMjZI9e8gami4lEWNTVPcfS8Ne/jV2R33tCp5tVj+Y9jv7vkrUgeatt2+ZS8p/3m1cu4zV7z6l3caBVd7xTbK6/ew5MUBwAAAAAAAAAAAAAAAAAAAAAAAAAAACj2AcQOAxB4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAIIg/69uSEABAAAAAAAAAAAAAAAAAAAAAAAAAG8GUJAAAQ==
  </AppendedData>
</VTKFile>
