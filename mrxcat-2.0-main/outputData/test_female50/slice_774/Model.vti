<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.548" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="70"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="1"                    offset="4460"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAABAAAAAQwEAAJYBAABjAQAAWwEAAPYAAADlAAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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CAAAAACAAACQUAAANAAAADQAAAA0AAAADQEAAJcAAAA0AAAANAAAACoAAAA=eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJzt19FqhEAMQFHz/z/dLS20OLGZLqyQ7Dngg8M8CDeDehwAAAAAAAAAAAAAAAAAAMAk8VBsuOlJuElEEb3cQC/xo9px63PxMlE2r4eCViLK6JoPUzevh4JW4p/NRe8vyugbU0ErZfN6KOjlXLRurnp3dVHNp3mmuei9lUGT5Jq3VgfNmoveWR1U82E2gmo+zGXPX11Fn+Ui58WN5hNUyT9vvy7NpyiTPxY0nyVrvqykg0FXWcXy5GveWhYx76v5FBvn/M9V+lk6Ji/0daPmnS0lY/lmO7zRZzknPuK88h1Y8zHSf++Fv7VJ9ppnY0BXe31FH+SpI655a5q/oSym5rM55u9H8hf6AOAfBaB4nO3VQQ6AIAxFQb3/pd2qCAuNQftnTtDktbAsn7TeNHtu7tM8kOZ5JA+keZ5u1nWwD7OH5plRWMlr0jxQN/poG/g1yQNJnkfyQJrnkTxQv7DmZbnyPG1ayetr2kpeXltX8/LOeRUPcPmDS17bMbHmGfqP++zJeM8+sOKRBAcAAAAAAAAAAAAAAAAAAAAAAACAQjbsTQP6eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACCIP+vbkhAAQAAAAAAAAAAAAAAAAAAAAAAAABvBlCQAAE=
  </AppendedData>
</VTKFile>
