<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.3840000000000001" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="65"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="1"                    offset="4624"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAAGIAAADfAQAAPgIAAAYCAAAXAQAAXwAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJzt2MEKgCAQRVHn/386iswWrRvqnYMLl8JlBB0DAAAAAAAAAAAAAAAAAAAA+LKqfZGjTt3n4DUztugxVmrRU9Tjlh+7D7dBz6B5IHd7nhXamKe4Snuhx5ipJQ9yfMH5iMsym3efAwAAAAAAAAAAAOi2AdjPAD94nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACCIP+vbkhAAQAAAAAAAAAAAAAAAAAAAAAAAABvBlCQAAE=
  </AppendedData>
</VTKFile>
