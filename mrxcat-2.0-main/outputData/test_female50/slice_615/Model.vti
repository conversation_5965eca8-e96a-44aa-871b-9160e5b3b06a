<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.23" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="58"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="1"                    offset="6392"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA/AAAAOwEAAK4BAAD+AQAAVgIAAKACAABIAgAALAEAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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CAAAAACAAACQUAAANAAAADQAAAA0AAAAZgAAADQAAAA0AAAANAAAACoAAAA=eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJzt0bENgDAQBEFf/02TAAG5/RI3U8FKuxYAAAAAAAAAAAAAAAAAAAA/lWQ6gcOS93r87/A8z226h/3yNR3Efp4XMr2P54U8L+R5Ic8LeV7I8kaeF7IcAAAAAAAAAAAA6lx3LACpeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgiD/r25IQAEAAAAAAAAAAAAAAAAAAAAAAAAAbwZQkAAB
  </AppendedData>
</VTKFile>
