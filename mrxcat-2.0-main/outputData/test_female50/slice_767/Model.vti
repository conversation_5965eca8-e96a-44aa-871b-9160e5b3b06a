<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.534" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="37"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="1"                    offset="4456"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAABWAAAAHgEAAGABAAB6AQAAOAEAAGEBAADKAAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJzt1sEKAjEMRdHJ//+0qAMiI9hZdKp55yzbTeBCyLYBAAAAAAAAAAAAAAAAAAAAAAA/pqpWj8C1qkRPU6KHqdI8TYkeR/JAoifSPI/mAH/uscQt8iiu9DD1snoULqJ5nHq3ehxmeh5uB/sHDd3zHovvr6uHY4qPve34tr70Fr2fkeSitzKWXPRGRpOL3sZ4ctGbOJNc9CY0z6N5nlPNRe9B8ylu+QUCZnic7dXBCoIhEIVRe/+XbtHfIkeIwLDmnrMTNwMfo2P8sNsnTg/LJpIH0jyQ5EkeHSUPpHmSq6TkQZ4xRQ9y1SyB6/n0pOxS2i6vhuadLN7w1eafHpOdalzN+5vbLv5zzZspbTVvrzaXvL2aV/P25sKS9/dmzw9Px3dMfSXP8FJX8jyS55E8kOIAAAAAAAAAAAAAAAAAAAAAAAAAAPBP7qJkAq94nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAIIg/69uSEABAAAAAAAAAAAAAAAAAAAAAAAAAG8GUJAAAQ==
  </AppendedData>
</VTKFile>
