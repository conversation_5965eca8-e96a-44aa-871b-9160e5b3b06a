<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.3800000000000001" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="65"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="0"                    offset="4968"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAAGYAAADgAQAAjQIAAHECAABIAQAAcQAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAIIg/69uSEABAAAAAAAAAAAAAAAAAAAAAAAAAG8GUJAAAQ==
  </AppendedData>
</VTKFile>
