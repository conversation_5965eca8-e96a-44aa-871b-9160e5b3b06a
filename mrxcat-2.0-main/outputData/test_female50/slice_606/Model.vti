<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.212" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="58"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="1"                    offset="6556"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAABKAAAAYAEAAOYBAADsAQAATQIAAMUCAAB7AgAAAQEAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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CAAAAACAAACQUAAANAAAADQAAAA0AAAA6wAAADQAAAA0AAAANAAAACoAAAA=eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJzt2lsOhCAQRUHZ/6bnaxIfNH9icrtqBZ2cgKgcBwAAAAAAAEC+8ff1IOxxai18B5PGsmcr8ooea7GgRc+02sM1j7RM7jQXabGxe3fLVBcdQ/RMVdBxtXkq3jTtOZ72T8ZrJj0nyUXPMv0Ep3m2W9BrZ9EjXXvel7bmkc49H7u55uFmD3DNo03PbJonm5/SNQ9WvZhpHqyIq3mwIq3mwVbJNY9UhNU8meb9rJrvnoVNPM4bqi9Q7J+FTaoLFF/Mwkc0b0fybooP8OSSvJfTr3TJm5C8H8nbkbwhxVtSHAAAAAAA2OkHlzgBzXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAIIg/69uSEABAAAAAAAAAAAAAAAAAAAAAAAAAG8GUJAAAQ==
  </AppendedData>
</VTKFile>
