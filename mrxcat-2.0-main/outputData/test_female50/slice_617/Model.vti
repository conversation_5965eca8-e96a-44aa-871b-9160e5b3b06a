<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.234" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="58"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="0"                    offset="6344"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAKQEAAJsBAADIAQAAjQIAAJsCAABgAgAAIwEAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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CAAAAACAAACQUAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAACoAAAA=eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAIIg/69uSEABAAAAAAAAAAAAAAAAAAAAAAAAAG8GUJAAAQ==
  </AppendedData>
</VTKFile>
