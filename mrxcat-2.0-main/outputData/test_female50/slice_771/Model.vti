<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.542" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="69"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="1"                    offset="4488"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAABAAAAAJQEAAIoBAACGAQAAWQEAAB4BAADcAAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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CAAAAACAAACQUAAANAAAADQAAAA0AAAA8gAAAK8AAAA0AAAANAAAACoAAAA=eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJzt1kGOwjAMQFF8/0sjNEViBoITTUtl894yysLSb5NcLgAAAAAAAAAAAAAAAAAAAEAlEf/dQC0RSdLYfGgeDhdZ0nQDxURkTfMd1JIWjRC9mbSo5t1MFBW9l4i8qOa9LDcXvbz15qJXt55c8+rWk4te3ERLzZvR/PsMUj6uPDcXvbSXEf/G1bu9t//+aVNxoMExLnlfo7tb87ZG7zXJ2xo+0iVv6db0bfNzx2N/L3Jvze8fw9kTsrdR8+fnHE3kyUXvRvPvM9Nc9C5+Yk41l72JreVsdNUbmI4teg8Lp7rmPazc5Jr38Lul6Du6AinHA/F4nO3VMQ6DMBBFQbj/pdNCjG0iB22yO9MBzZeebLbtR+1H56f+h+jRLOlVHooezZJ7jSXPZNb36lX0ZtbcvM01z2R+lWueTafqoH70YpYNTrNjntSnzaP38gWa19M76G72xCQvSPF6NC+oSat5fk1ZydPTvKAmruT5df7gkif23lfzAibRo+fxiFHz6G085VhX8IokBwAAAAAAAAAAAAAAAAAAAAAAAAD4ay+ZDgOxeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACCIP+vbkhAAQAAAAAAAAAAAAAAAAAAAAAAAABvBlCQAAE=
  </AppendedData>
</VTKFile>
