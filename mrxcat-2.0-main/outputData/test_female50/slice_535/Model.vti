<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.07" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="52"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="0"                    offset="6808"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAABTAAAANQEAAOMBAABiAgAAxAIAAGQDAABQAgAAggAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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CAAAAACAAACQUAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAACoAAAA=eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAIIg/69uSEABAAAAAAAAAAAAAAAAAAAAAAAAAG8GUJAAAQ==
  </AppendedData>
</VTKFile>
