<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.066" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="52"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="0"                    offset="6700"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAABVAAAAJQEAAMUBAABlAgAArQIAAIUDAAAkAgAAfgAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAIIg/69uSEABAAAAAAAAAAAAAAAAAAAAAAAAAG8GUJAAAQ==
  </AppendedData>
</VTKFile>
