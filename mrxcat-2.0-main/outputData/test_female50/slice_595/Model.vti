<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.19" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="51"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="1"                    offset="6648"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAABMAAAAjgEAAPcBAAACAgAAdAIAAJMCAAB1AgAAAgEAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJzt2bsOgzAQRcH4/386ZR54DY1BujtTJVQrHZmQ5fUCAAAAAACAhsbR0yOxT1FZ9Fj1iXbeM9VJ3eVDXUsuepA6pue5UGfJP580D3EluehRzn7Lf7/eMxN7XU2ueYyzJ/b/C7cMxVYnm5jDpTtmYq+i4nwLq3mEecVi8655hmnH6T8zzYMcQtbJNQ8137RqHqxYrmueq3qhInms8h2a5qnWyTVPVCV3zGOtk2ueyJ29H8e8n2VyzRMt7+ySR1pt4DTPtFi0S55qUlfycP99x8eDU7HVd+ExJG9hFJ6ei30kb0jyhhTvSPKGFG9IcgAAAAAAAG7wBljjAlR4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACCIP+vbkhAAQAAAAAAAAAAAAAAAAAAAAAAAABvBlCQAAE=
  </AppendedData>
</VTKFile>
