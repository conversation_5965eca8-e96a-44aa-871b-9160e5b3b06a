<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.198" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="58"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="1"                    offset="6620"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAABFAAAAmQEAAMYBAAAZAgAAUgIAAKACAAB3AgAAFQEAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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CAAAAACAAACQUAAANAAAADQAAAA0AAAA8gAAADQAAAA0AAAANAAAACoAAAA=eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJzt2TEOgzAQRcH4/pdOG4jXIQVG+jvT0a30ZDD26wUAAAAAAADdjE9PD8P9jqGFz1cElj3WKq0VH2mR1Ac+06Xioie5mlz0GHXLc3LRU/yRXPQMP5MfnraNxY2qjufIoucoMn6/zDWPMc84+X5rHmPacbZl0zzHpOR0l655suLHTPNc1b+45rHK8xfNU62Ta56oPGXVPFZ53qZ5rDKt5KnKspZ5rDKs5rHWzTcPww6WeT/r5JonsswbWl6n7x6GLS5fpxNN8nbKWxdSSd7NkLyboXkf4+zpgbib5A0p3pDkDSnekeAAAAAAADzsDYKGAg54nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACCIP+vbkhAAQAAAAAAAAAAAAAAAAAAAAAAAABvBlCQAAE=
  </AppendedData>
</VTKFile>
