<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.22" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="52"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="0"                    offset="6748"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAACSAAAAWQEAALYBAACkAgAANgMAANoCAAByAQAA1AAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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CAAAAACAAACQUAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAACoAAAA=eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAIIg/69uSEABAAAAAAAAAAAAAAAAAAAAAAAAAG8GUJAAAQ==
  </AppendedData>
</VTKFile>
