<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.214" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="52"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="0"                    offset="6500"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAACMAAAAYAEAAJwBAABlAgAA4QIAAPMCAABVAQAAywAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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CAAAAACAAACQUAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAACoAAAA=eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAIIg/69uSEABAAAAAAAAAAAAAAAAAAAAAAAAAG8GUJAAAQ==
  </AppendedData>
</VTKFile>
