<?xml version="1.0"?>
<VTKFile type="ImageData" version="0.1" byte_order="LittleEndian" header_type="UInt32" compressor="vtkZLibDataCompressor">
  <ImageData WholeExtent="0 499 0 499 0 0" Origin="0 0 1.224" Spacing="0.002 0.002 0.002" Direction="1 0 0 0 1 0 0 0 1">
    <Piece Extent="0 499 0 499 0 0"                                                   >
      <PointData>
        <DataArray type="Float32" Name="labels" format="appended" RangeMin="0"                    RangeMax="52"                   offset="0"                   />
        <DataArray type="UInt8" Name="LV_mask" format="appended" RangeMin="0"                    RangeMax="0"                    offset="6808"                />
      </PointData>
      <CellData>
      </CellData>
    </Piece>
  </ImageData>
  <AppendedData encoding="base64">
   _HwAAAACAAABAQgAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAACZAAAAcAEAAK4BAACFAgAAOQMAACYDAABgAQAAzQAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAAJwAAAA==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CAAAAACAAACQUAAANAAAADQAAAA0AAAANAAAADQAAAA0AAAANAAAACoAAAA=eJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGIAAAAF4nO3BAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABiAAAABeJztwQEBAAAAgJD+r+4ICgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYgAAAAXic7cEBAQAAAIIg/69uSEABAAAAAAAAAAAAAAAAAAAAAAAAAG8GUJAAAQ==
  </AppendedData>
</VTKFile>
