#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
体积变换和重组模块

此模块负责3D体积的轴变换和重组操作。
"""

import numpy as np
from scipy import ndimage

def extract_oblique_slice(data_input, best_slice, offset=0, Lu=200, Lv=100, theta=-45):
    """
    从3D体积中提取斜切片

    新的切片平面定义如下:
      • 中心点:  center = (best_slice, Y/2, X/2)  [使用体积顺序 (z,y,x)]
      • new_x轴: theta方向在轴向(y–x)平面:
                 new_x = (0, cos(theta), sin(theta))
      • new_y轴: 沿原始z轴:
                 new_y = (1, 0, 0)
      • new_z轴: 垂直计算为:
                 new_z = cross(new_x, new_y)

    2D斜切片通过以下方式从体积中提取:
         original_coord = center + u * new_x + v * new_y + offset * new_z,
    其中 u ∈ [–Lu/2, Lu/2], v ∈ [–Lv/2, Lv/2]。

    参数:
      data_input : 3D numpy数组，形状为(Z, Y, X)
      best_slice : 作为中心的轴向切片索引
      offset : 沿new_z轴的位移
      Lu : 沿new_x轴的像素长度（轴向平面中的斜向方向）
      Lv : 沿new_y轴的像素长度（原始z方向）
      theta : 轴向平面中斜向方向的角度（度）

    返回:
      oblique_slice : 2D numpy数组，提取的斜切片
    """
    # 确定体积尺寸和中心点
    Y, X = data_input.shape[1], data_input.shape[2]
    center = np.array([best_slice, Y/2, X/2])  # center = (z, y, x)

    # 定义新的坐标方向
    theta_rad = np.deg2rad(theta)
    new_x = np.array([0, np.cos(theta_rad), np.sin(theta_rad)])  # 例如 (0, 0.7071, 0.7071)
    new_y = np.array([1, 0, 0])  # 沿原始z轴
    new_z = np.cross(new_x, new_y)  # 垂直于两者

    # 创建新切片坐标的网格
    Lu_pixels = int(Lu)
    Lv_pixels = int(Lv)
    u = np.linspace(-Lu/2, Lu/2, Lu_pixels)
    v = np.linspace(-Lv/2, Lv/2, Lv_pixels)
    U, V = np.meshgrid(u, v)  # 形状 (Lv_pixels, Lu_pixels)

    # 计算原始坐标
    Z_coord = center[0] + V * new_y[0] + offset * new_z[0]  # new_y贡献z; new_z[0]是0
    Y_coord = center[1] + U * new_x[1] + offset * new_z[1]  # new_x[1] = cos(theta); new_z[1] = cos(theta)
    X_coord = center[2] + U * new_x[2] + offset * new_z[2]  # new_x[2] = sin(theta); new_z[2] = -sin(theta)
    coords = [Z_coord, Y_coord, X_coord]

    # 使用插值提取斜切片
    oblique_slice = ndimage.map_coordinates(data_input, coords, order=1, mode='constant', cval=0)
    return oblique_slice

def transform_modality_to_oblique_view(volume):
    """
    使用两个连续变换将体积转换为斜视图

    参数:
    -----------
    volume : 3D numpy数组
        要转换的输入体积（例如，data['T2'], data['T1']）

    返回:
    --------
    new_ref_volume : 3D numpy数组
        最终斜视图中的转换体积
    """
    # ===== 第一次变换 =====
    # 第一次变换的参数
    best_slice = 60
    Lu = 150
    Lv = 150
    Lz = 101
    D = 100
    fixed_offset = -7
    theta = -45

    # 获取体积尺寸
    Z_in, Y_in, X_in = volume.shape
    print(f"[DEBUG] Original volume shape: {volume.shape}")

    # 在原始体积坐标中定义中心
    center = np.array([best_slice, Y_in/2, X_in/2])
    print(f"[DEBUG] Center in original volume: {center}")

    # 定义新的坐标轴
    theta_rad = np.deg2rad(theta)
    new_x = np.array([0, np.cos(theta_rad), np.sin(theta_rad)])
    new_y = np.array([1, 0, 0])
    new_z = -np.cross(new_x, new_y)
    print(f"[DEBUG] New coordinate axes: x={new_x}, y={new_y}, z={new_z}")

    # 包含固定偏移
    shifted_center = center + fixed_offset * new_z
    print(f"[DEBUG] Shifted center: {shifted_center}")

    # 设置尺寸
    Lu_pixels = int(Lu)
    Lv_pixels = int(Lv)
    Lz_pixels = int(Lz)

    # 创建新坐标的网格
    u = np.linspace(-Lu/2, Lu/2, Lu_pixels)
    v = np.linspace(-Lv/2, Lv/2, Lv_pixels)
    d = np.linspace(-D/2, D/2, Lz_pixels)

    # 创建网格
    D_grid, V_grid, U_grid = np.meshgrid(d, v, u, indexing='ij')

    # 将坐标从新到原始体积映射
    orig_Z = shifted_center[0] + D_grid * new_z[0] + V_grid * new_y[0] + U_grid * new_x[0]
    orig_Y = shifted_center[1] + D_grid * new_z[1] + V_grid * new_y[1] + U_grid * new_x[1]
    orig_X = shifted_center[2] + D_grid * new_z[2] + V_grid * new_y[2] + U_grid * new_x[2]

    coords = [orig_Z, orig_Y, orig_X]

    # 重新切片原始体积（第一次变换）
    ref_volume = ndimage.map_coordinates(volume, coords, order=1, mode='constant', cval=0)
    print(f"[DEBUG] Intermediate volume shape after first transformation: {ref_volume.shape}")

    # ===== 第二次变换 =====
    # 第二次变换的参数
    new_best_slice = 51
    Lu_new = 150
    Lv_new = 150
    Lz_new = 101
    D_new = 100
    fixed_offset = 0
    new_theta = -18

    # 中间体积的尺寸
    Lz_old, Lv_old, Lu_old = ref_volume.shape

    # 在中间体积坐标中定义中心
    center_new = np.array([new_best_slice, Lv_old/2, Lu_old/2])
    print(f"[DEBUG] Center in intermediate volume: {center_new}")

    # 定义新的坐标轴
    new_theta_rad = np.deg2rad(new_theta)
    new_x_re = np.array([0, np.cos(new_theta_rad), np.sin(new_theta_rad)])
    new_y_re = np.array([1, 0, 0])
    new_z_re = -np.cross(new_x_re, new_y_re)
    print(f"[DEBUG] New coordinate axes for second transformation: x={new_x_re}, y={new_y_re}, z={new_z_re}")

    # 包含固定偏移
    shifted_center = center_new + fixed_offset * new_z_re

    # 设置尺寸
    Lu_pixels = int(Lu_new)
    Lv_pixels = int(Lv_new)
    Lz_pixels = int(Lz_new)

    # 创建本地坐标的网格
    u = np.linspace(-Lu_new/2, Lu_new/2, Lu_pixels)
    v = np.linspace(-Lv_new/2, Lv_new/2, Lv_pixels)
    d = np.linspace(-D_new/2, D_new/2, Lz_pixels)

    # 网格
    D_grid, V_grid, U_grid = np.meshgrid(d, v, u, indexing='ij')

    # 映射坐标
    orig_d = shifted_center[0] + D_grid * new_z_re[0] + V_grid * new_y_re[0] + U_grid * new_x_re[0]
    orig_v = shifted_center[1] + D_grid * new_z_re[1] + V_grid * new_y_re[1] + U_grid * new_x_re[1]
    orig_u = shifted_center[2] + D_grid * new_z_re[2] + V_grid * new_y_re[2] + U_grid * new_x_re[2]

    coords_new = [orig_d, orig_v, orig_u]

    # 重新采样中间体积（第二次变换）
    new_ref_volume = ndimage.map_coordinates(ref_volume, coords_new, order=1, mode='constant', cval=0)
    print(f"[DEBUG] Final volume shape after second transformation: {new_ref_volume.shape}")

    return new_ref_volume

def process_transformed_slice(transformed_data, modality, slice_idx=None):
    """
    通过以下方式处理转换数据中的切片:
    1. 交换x和y轴
    2. 垂直翻转y轴（上下颠倒）

    参数:
    -----------
    transformed_data : dict
        包含所有转换模态的字典
    modality : str
        要处理的模态（例如，'T2', 'T1'）
    slice_idx : int or None
        要提取的切片索引（如果为None，则使用中间切片）

    返回:
    --------
    processed_slice : 2D numpy数组
        处理后的切片，轴已交换和翻转
    """
    # 如果slice_idx为None，则使用中间切片
    if slice_idx is None:
        slice_idx = transformed_data[modality].shape[0] // 2

    print(f"[DEBUG] Processing {modality} slice {slice_idx}")
    print(f"[DEBUG] Volume shape: {transformed_data[modality].shape}")

    # 提取切片
    central_slice = transformed_data[modality][slice_idx, :, :]
    print(f"[DEBUG] Original slice shape: {central_slice.shape}")
    print(f"[DEBUG] Original slice min/max: {central_slice.min():.4f}/{central_slice.max():.4f}")

    # 1. 转置切片（交换x和y）
    transposed_slice = central_slice.T
    print(f"[DEBUG] Transposed slice shape: {transposed_slice.shape}")

    # 2. 垂直翻转（上下颠倒）
    # 注意：[::-1, :]表示翻转第一个维度（行）
    processed_slice = transposed_slice[::-1, :]
    print(f"[DEBUG] Processed slice shape: {processed_slice.shape}")
    print(f"[DEBUG] Processed slice min/max: {processed_slice.min():.4f}/{processed_slice.max():.4f}")

    return processed_slice

def reorganize_volume(volume):
    """
    重新组织3D体积，通过:
    1. 保持z维度（第一维度）不变
    2. 交换x和y维度（转置每个切片）
    3. 垂直翻转新的y轴

    参数:
    -----------
    volume : 形状为(z, y, x)的3D numpy数组

    返回:
    --------
    reorganized : 形状为(z, x, y)且y已翻转的3D numpy数组
    """
    # 获取原始形状
    Z, Y, X = volume.shape
    print(f"  Original shape: {volume.shape}", end='')

    # 创建新体积，注意这里交换了X和Y的位置
    reorganized = np.zeros((Z, X, Y), dtype=volume.dtype)

    # 对每个z位置处理切片
    for z in range(Z):
        # 提取切片
        slice_data = volume[z, :, :]

        # 转置并翻转（交换x和y，然后翻转y轴）
        # 注意：这里的翻转是对第一个维度进行的，即垂直翻转
        # 原始代码中的slice_data.T[::-1, :]将切片转置并翻转第一个维度
        # 我们需要确保这个操作与原始代码一致
        reorganized[z, :, :] = slice_data.T[::-1, :]

        # 打印调试信息
        if z == Z // 2:  # 只对中间切片进行调试
            print(f"\n[DEBUG] Original slice shape: {slice_data.shape}")
            print(f"[DEBUG] Transposed slice shape: {slice_data.T.shape}")
            print(f"[DEBUG] Final slice shape: {reorganized[z, :, :].shape}")
            print(f"[DEBUG] Original slice min/max: {slice_data.min():.4f}/{slice_data.max():.4f}")
            print(f"[DEBUG] Reorganized slice min/max: {reorganized[z, :, :].min():.4f}/{reorganized[z, :, :].max():.4f}")

    print(f", Reorganized shape: {reorganized.shape}")
    return reorganized
