# Part 1: Setup and Data Loading
import numpy as np
import vtk
from vtk.util.numpy_support import vtk_to_numpy
import matplotlib.pyplot as plt
from ipywidgets import interact
import ipywidgets as widgets
import matplotlib.colors as mcolors
import matplotlib.patches as mpatches

# Define tissue labels
class myLabels:
    def __init__(self, LV_wall, RV_wall, LA_wall, RA_wall, LV_blood, RV_blood, LA_blood, RA_blood,
                 body, muscle, brain, sinus, liver, gall_bladder, right_lung, left_lung,
                 esophagus, esophagus_contents, laryngopharynx, stomach_wall, stomach_contents,
                 pancreas, right_kidney_cortex, right_kidney_medulla, left_kidney_cortex,
                 left_kidney_medulla, adrenal, right_renal_pelvis, left_renal_pelvis,
                 spleen, ribs, cortical_bone, spine, spinal_cord, bone_marrow, arteries,
                 veins, bladder, prostate, ascending_intestine, transverse_intestine,
                 descending_intestine, small_intestine, rectum, seminal_vesicles,
                 vas_deferens, testes, epididymis, ejaculatory_duct, pericardium,
                 cartilage, intestine_air, ureter, urethra, lymph, lymph_abnormal,
                 trachea_bronchi, airways, thyroid, thymus):
        self.LV_wall = LV_wall
        self.RV_wall = RV_wall
        self.LA_wall = LA_wall
        self.RA_wall = RA_wall
        self.LV_blood = LV_blood
        self.RV_blood = RV_blood
        self.LA_blood = LA_blood
        self.RA_blood = RA_blood
        self.body = body
        self.muscle = muscle
        self.brain = brain
        self.sinus = sinus
        self.liver = liver
        self.gall_bladder = gall_bladder
        self.right_lung = right_lung
        self.left_lung = left_lung
        self.esophagus = esophagus
        self.esophagus_contents = esophagus_contents
        self.laryngopharynx = laryngopharynx
        self.stomach_wall = stomach_wall
        self.stomach_contents = stomach_contents
        self.pancreas = pancreas
        self.right_kidney_cortex = right_kidney_cortex
        self.right_kidney_medulla = right_kidney_medulla
        self.left_kidney_cortex = left_kidney_cortex
        self.left_kidney_medulla = left_kidney_medulla
        self.adrenal = adrenal
        self.right_renal_pelvis = right_renal_pelvis
        self.left_renal_pelvis = left_renal_pelvis
        self.spleen = spleen
        self.ribs = ribs
        self.cortical_bone = cortical_bone
        self.spine = spine
        self.spinal_cord = spinal_cord
        self.bone_marrow = bone_marrow
        self.arteries = arteries
        self.veins = veins
        self.bladder = bladder
        self.prostate = prostate
        self.ascending_intestine = ascending_intestine
        self.transverse_intestine = transverse_intestine
        self.descending_intestine = descending_intestine
        self.small_intestine = small_intestine
        self.rectum = rectum
        self.seminal_vesicles = seminal_vesicles
        self.vas_deferens = vas_deferens
        self.testes = testes
        self.epididymis = epididymis
        self.ejaculatory_duct = ejaculatory_duct
        self.Peri = pericardium
        self.cartilage = cartilage
        self.intestine_air = intestine_air
        self.ureter = ureter
        self.urethra = urethra
        self.lymph = lymph
        self.lymph_abnormal = lymph_abnormal
        self.trachea_bronchi = trachea_bronchi
        self.airways = airways
        self.thyroid = thyroid
        self.thymus = thymus

maskLabels = myLabels(1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 
                     17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 
                     31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 
                     45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 
                     65, 66)

# Load data
# Load data
input_vti = "/Users/<USER>/Documents/important document backup/Local python code/MRXCAT2/mrxcat-2.0-main/female50/singleframe_act_1_fixed.vti" # Replace with your actual path

# Create VTK reader and read the file
read_img = vtk.vtkXMLImageDataReader()
read_img.SetFileName(input_vti)
read_img.Update()
img_ref = read_img.GetOutput()

# Part 2: Process Data and Find Best Slice
dims = img_ref.GetDimensions()
# data_input = vtk_to_numpy(img_ref.GetPointData().GetArray('labels')).reshape(dims[0], dims[1], dims[2], order="F")

# —— 新增：列出所有可用的 point data arrays —— 
point_data = img_ref.GetPointData()
print("Available arrays in VTI:")
for i in range(point_data.GetNumberOfArrays()):
    print(f"  {i}: {point_data.GetArrayName(i)}")

# 然后用正确的数组名（比如实际可能是 'Label' 或者其他）替换下面的 'labels'
array_name = 'labels'  # ← 替换为上面打印出的正确名字
vtk_arr = point_data.GetArray(array_name)
if vtk_arr is None:
    avail = [point_data.GetArrayName(i) for i in range(point_data.GetNumberOfArrays())]
    raise RuntimeError(f"Cannot find array '{array_name}', available: {avail}")

data_input = vtk_to_numpy(vtk_arr) \
    .reshape(dims[0], dims[1], dims[2], order="F")
print(f"Data type: {data_input.dtype}")

print(f"Data type: {data_input.dtype}")
slice_sums = np.sum(data_input > 0, axis=(0,1))
best_slice = np.argmax(slice_sums)
print(f"Best slice index: {best_slice}")

# Define the tissue types to display with their corresponding label values
tissue_types = {
    'LV Wall': maskLabels.LV_wall,
    'RV Wall': maskLabels.RV_wall,
    'LA Wall': maskLabels.LA_wall,  
    'RA Wall': maskLabels.RA_wall,
    'LV Blood': maskLabels.LV_blood,
    'RV Blood': maskLabels.RV_blood,
    'LA Blood': maskLabels.LA_blood,
    'RA Blood': maskLabels.RA_blood,
    'Pericardium': maskLabels.Peri,
    'Arteries': maskLabels.arteries,
    'veins': maskLabels.veins,
    'Right Lung': maskLabels.right_lung,
    'Left Lung': maskLabels.left_lung,
    'Liver': maskLabels.liver,
    'Spleen': maskLabels.spleen,
    # 'Stomach Wall': maskLabels.stomach_wall,
    'Pancreas': maskLabels.pancreas,
    'Spine': maskLabels.spine,
    'Spinal Cord': maskLabels.spinal_cord,
    'muscle': maskLabels.muscle,
    'body': maskLabels.body,
    'coritcal bone': maskLabels.cortical_bone,
    'ribs': maskLabels.ribs,
    'bone marrow': maskLabels.bone_marrow,
}

# Part 3: Display Static Best Slice View
n_tissues = len(tissue_types)
n_cols = 3
n_rows = (n_tissues + n_cols - 1) // n_cols  # Ceiling division to ensure enough rows

fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 5*n_rows))
axes = axes.ravel()

for idx, (name, label) in enumerate(tissue_types.items()):
    mask = (data_input[:,:,best_slice] == label)
    axes[idx].imshow(mask, cmap='gray')
    axes[idx].set_title(f'{name} (Label {label})\nSlice {best_slice}')
    axes[idx].axis('off')

# Hide empty subplots
for idx in range(n_tissues, len(axes)):
    axes[idx].axis('off')

plt.tight_layout()
plt.show()


# Part 4: Show Single Best Slice
# Modified Part 4: Show Single Best Slice (only listed tissues)
plt.figure(figsize=(8, 8))

# Create a modified slice where unlisted tissues are set to 0
slice_data = data_input[:,:,best_slice].copy()
listed_labels = list(tissue_types.values())

# Set all unlisted tissues to 0 (background)
mask = np.isin(slice_data, listed_labels)
filtered_slice = np.where(mask, slice_data, 0)

# Create a custom colormap with distinct colors
# Get a list of distinct colors from matplotlib
distinct_colors = list(mcolors.TABLEAU_COLORS.values())
# Add more colors if needed
distinct_colors += list(mcolors.CSS4_COLORS.values())[:len(tissue_types) - len(distinct_colors)]

# Create a mapping from label values to color indices
label_to_idx = {label: i for i, label in enumerate(listed_labels)}

# Create a custom normalized array where each tissue gets a distinct value 
# between 0 and 1 based on its position in the tissue_types dictionary
custom_colored_slice = np.zeros_like(filtered_slice, dtype=float)
for label in listed_labels:
    if np.any(filtered_slice == label):
        custom_colored_slice[filtered_slice == label] = label_to_idx[label] + 1  # +1 to keep 0 as background

# Normalize to 0-1 range for colormap
if len(label_to_idx) > 0:  # Check to avoid division by zero
    custom_colored_slice = custom_colored_slice / (len(label_to_idx) + 1)

# Create custom colormap with the distinct colors
n_colors = len(tissue_types) + 1  # +1 for background
cmap_colors = [(0,0,0,0)]  # Start with transparent for background (0)
for i in range(len(distinct_colors[:len(tissue_types)])):
    cmap_colors.append(mcolors.to_rgba(distinct_colors[i]))
custom_cmap = mcolors.ListedColormap(cmap_colors)

# Plot with the custom colormap
img = plt.imshow(custom_colored_slice, cmap=custom_cmap, vmin=0, vmax=1)
plt.title(f'Combined Tissue Labels (Slice {best_slice}) - Listed Tissues Only')

# Create legend with the actual distinct colors
legend_patches = []
for i, (name, label) in enumerate(tissue_types.items()):
    if np.any(filtered_slice == label):  # Check if this tissue exists in the slice
        color = distinct_colors[i]
        legend_patches.append(mpatches.Patch(color=color, label=f'{name} ({label})'))

plt.legend(handles=legend_patches, bbox_to_anchor=(1.05, 1), loc='upper left')
plt.axis('off')
plt.tight_layout()
plt.show()

# Part 5: Interactive Slice Viewer
#Modified Interactive Slice Viewer (only listed tissues)
def create_filtered_interactive_viewer_with_distinct_colors():
    # Create one large subplot for the combined view
    fig = plt.figure(figsize=(10, 8))
    
    # Predefine distinct colors
    distinct_colors = list(mcolors.TABLEAU_COLORS.values())
    distinct_colors += list(mcolors.CSS4_COLORS.values())[:len(tissue_types) - len(distinct_colors)]
    
    # Create mapping from labels to indices
    listed_labels = list(tissue_types.values())
    label_to_idx = {label: i for i, label in enumerate(listed_labels)}
    
    # Create custom colormap with the distinct colors
    n_colors = len(tissue_types) + 1  # +1 for background
    cmap_colors = [(0,0,0,0)]  # Start with transparent for background (0)
    for i in range(len(distinct_colors[:len(tissue_types)])):
        cmap_colors.append(mcolors.to_rgba(distinct_colors[i]))
    custom_cmap = mcolors.ListedColormap(cmap_colors)
    
    def update(slice_idx):
        plt.clf()  # Clear the current figure
        
        # Get the slice data
        original_slice = data_input[:,:,slice_idx].copy()
        
        # Filter out unlisted tissues (set to 0)
        mask = np.isin(original_slice, listed_labels)
        filtered_slice = np.where(mask, original_slice, 0)
        
        # Create a custom normalized array for distinct colors
        custom_colored_slice = np.zeros_like(filtered_slice, dtype=float)
        for label in listed_labels:
            if np.any(filtered_slice == label):
                custom_colored_slice[filtered_slice == label] = label_to_idx[label] + 1
        
        # Normalize to 0-1 range for colormap
        if len(label_to_idx) > 0:
            custom_colored_slice = custom_colored_slice / (len(label_to_idx) + 1)
        
        # Plot with the custom colormap
        img = plt.imshow(custom_colored_slice, cmap=custom_cmap, vmin=0, vmax=1)
        plt.title(f'Combined Tissue Labels (Slice {slice_idx}) - Listed Tissues Only')
        
        # Create legend with the actual distinct colors
        legend_patches = []
        for i, (name, label) in enumerate(tissue_types.items()):
            if np.any(filtered_slice == label):  # Check if this tissue exists in the slice
                color = distinct_colors[i]
                legend_patches.append(mpatches.Patch(color=color, label=f'{name} ({label})'))
        
        plt.legend(handles=legend_patches, bbox_to_anchor=(1.05, 1), loc='upper left')
        plt.axis('off')
        plt.tight_layout()
        plt.draw()
    
    interact(update, 
            slice_idx=widgets.IntSlider(
                min=0, 
                max=data_input.shape[2]-1, 
                step=1, 
                value=best_slice,
                description='Slice:',
                continuous_update=False
            ))

# Use the modified viewer
create_filtered_interactive_viewer_with_distinct_colors()

# Part 6: Print Mask Information
print("\nMask information:")
for name, label in tissue_types.items():
    mask = (data_input == label)
    print(f"{name}: {mask.sum()} voxels (Label {label})")

# Part 8: Coronal View Interactive Viewer
def create_coronal_viewer():
    fig = plt.figure(figsize=(10, 8))
    
    def update(slice_idx):
        plt.clf()
        # For coronal view, we take slices along the second dimension
        img = plt.imshow(data_input[:,slice_idx,:], cmap='nipy_spectral')
        plt.title(f'Coronal View - Slice {slice_idx}')
        
        legend_elements = []
        unique_labels = np.unique(data_input[:,slice_idx,:])
        unique_labels = unique_labels[unique_labels != 0]
        
        for name, label in tissue_types.items():
            if label in unique_labels:
                color = img.cmap(img.norm(label))
                legend_elements.append(plt.Rectangle((0,0), 1, 1, fc=color, label=f'{name} ({label})'))
        
        plt.legend(handles=legend_elements, bbox_to_anchor=(1.05, 1), loc='upper left')
        plt.axis('off')
        plt.tight_layout()
        plt.draw()
    
    interact(update, 
            slice_idx=widgets.IntSlider(
                min=0,
                max=data_input.shape[1]-1,
                step=1,
                value=data_input.shape[1]//2,
                description='Coronal Slice:',
                continuous_update=False
            ))

create_coronal_viewer()

def find_connected_components(binary_mask):
    """
    Find connected components in a 3D binary mask using NumPy
    """
    # Initialize labels array
    labels = np.zeros_like(binary_mask, dtype=int)
    current_label = 1
    
    def flood_fill(pos, label):
        stack = [pos]
        while stack:
            x, y, z = stack.pop()
            if not (0 <= x < binary_mask.shape[0] and 
                   0 <= y < binary_mask.shape[1] and 
                   0 <= z < binary_mask.shape[2]):
                continue
            if not binary_mask[x,y,z] or labels[x,y,z]:
                continue
            labels[x,y,z] = label
            # Add neighbors to stack
            for dx, dy, dz in [(0,0,1), (0,0,-1), (0,1,0), (0,-1,0), (1,0,0), (-1,0,0)]:
                stack.append((x+dx, y+dy, z+dz))
    
    # Find connected components
    for x in range(binary_mask.shape[0]):
        for y in range(binary_mask.shape[1]):
            for z in range(binary_mask.shape[2]):
                if binary_mask[x,y,z] and not labels[x,y,z]:
                    flood_fill((x,y,z), current_label)
                    current_label += 1
    
    return labels

def clean_rv_wall_mask(data):
    """
    Filter RV wall mask to keep connected components that are adjacent to RV blood
    
    Parameters:
    -----------
    data : ndarray
        3D array with tissue labels where 2=RV wall and 6=RV blood
    
    Returns:
    --------
    ndarray
        Binary mask of cleaned RV wall
    """
    # Create masks for RV wall and blood
    rv_wall = (data == 2)
    rv_blood = (data == 6)
    
    # Find connected components in RV wall
    wall_components = find_connected_components(rv_wall)
    num_components = wall_components.max()
    
    # Create cleaned mask
    cleaned_rv_wall = np.zeros_like(rv_wall)
    
    # Check each component for adjacency to blood
    for label in range(1, num_components + 1):
        component = (wall_components == label)
        
        # Check if any part of this component is adjacent to blood
        is_adjacent = False
        comp_coords = np.where(component)
        
        for i, j, k in zip(*comp_coords):
            # Check 6-connected neighborhood
            for di, dj, dk in [(0,0,1), (0,0,-1), (0,1,0), (0,-1,0), (1,0,0), (-1,0,0)]:
                ni, nj, nk = i+di, j+dj, k+dk
                if (0 <= ni < data.shape[0] and 
                    0 <= nj < data.shape[1] and 
                    0 <= nk < data.shape[2] and 
                    rv_blood[ni,nj,nk]):
                    is_adjacent = True
                    break
            if is_adjacent:
                break
        
        # If component is adjacent to blood, keep it
        if is_adjacent:
            cleaned_rv_wall |= component
    
    return cleaned_rv_wall

def create_tissue_groups(data):
    # Initialize masks for each group
    cardiac_mask = np.zeros_like(data, dtype=bool)
    lung_mask = np.zeros_like(data, dtype=bool)
    tissue_mask = np.zeros_like(data, dtype=bool)
    air_mask = np.zeros_like(data, dtype=bool)
    
    # Clean RV wall mask
    cleaned_rv_wall = clean_rv_wall_mask(data)
    
    # Cardiac tissues (labels 1, 5, 6 and cleaned label 2)
    cardiac_labels = [1, 3, 4, 5, 6, 7, 8, 50] #include pericardiac region
    for label in cardiac_labels:
        cardiac_mask |= (data == label)
    # Add cleaned RV wall
    cardiac_mask |= cleaned_rv_wall

    # FIXED: Add removed parts of RV wall to tissue mask
    removed_rv_wall = (data == 2) & ~cleaned_rv_wall
    tissue_mask |= removed_rv_wall
    
    # Lung (label 15,16)
    lung_labels = [15, 16]
    for label in lung_labels:
        lung_mask |= (data == label)
    
    # All other labeled tissues
    all_labels = np.unique(data)
    other_tissue_labels = [label for label in all_labels 
                          if label not in cardiac_labels + [0, 2, 15, 16]]
    for label in other_tissue_labels:
        tissue_mask |= (data == label)
    
    # Air (unlabeled regions, label 0)
    air_mask = (data == 0)
    
    return cardiac_mask, lung_mask, tissue_mask, air_mask


# First, compute the cleaned RV wall mask using your existing function:
cleaned_rv = clean_rv_wall_mask(data_input)

# Create a copy of the original data_input:
updated_data_input = data_input.copy()

# Identify the voxels that were originally RV wall (label 2) but are removed in the cleaning process.
removed_rv_wall = (data_input == 2) & (~cleaned_rv)

# Determine the body label from tissue_types; change the key to match your definition.
body_label = tissue_types.get('body', 9)  # fallback to 99 if 'body' is not defined

# Update these voxels to body
updated_data_input[removed_rv_wall] = body_label

# Create the tissue group masks
cardiac_mask, lung_mask, tissue_mask, air_mask = create_tissue_groups(data_input)

# Visualization
plt.figure(figsize=(15, 10))

# Plot all four masks
plt.subplot(221)
plt.imshow(cardiac_mask[:,:,best_slice], cmap='gray')
plt.title('Cardiac Tissues\n(Labels 1,2,5,6)')
plt.axis('off')

plt.subplot(222)
plt.imshow(lung_mask[:,:,best_slice], cmap='gray')
plt.title('Lung Tissue\n(Label 16)')
plt.axis('off')

plt.subplot(223)
plt.imshow(tissue_mask[:,:,best_slice], cmap='gray')
plt.title('Other Tissues\n(Remaining Labels)')
plt.axis('off')

plt.subplot(224)
plt.imshow(air_mask[:,:,best_slice], cmap='gray')
plt.title('Air\n(Label 0)')
plt.axis('off')

plt.tight_layout()
plt.show()

# Print volume statistics
print("\nVolume statistics:")
print(f"Cardiac tissues: {cardiac_mask.sum()} voxels")
print(f"Lung tissue: {lung_mask.sum()} voxels")
print(f"Other tissues: {tissue_mask.sum()} voxels")
print(f"Air: {air_mask.sum()} voxels")

# Interactive slice viewer for all masks
def update_group_viewer(slice_idx):
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # Plot cardiac mask
    axes[0,0].imshow(cardiac_mask[:,:,slice_idx], cmap='gray')
    axes[0,0].set_title('Cardiac Tissues\n(Labels 1,2,5,6)')
    axes[0,0].axis('off')
    
    # Plot lung mask
    axes[0,1].imshow(lung_mask[:,:,slice_idx], cmap='gray')
    axes[0,1].set_title('Lung Tissue\n(Label 16)')
    axes[0,1].axis('off')
    
    # Plot other tissues mask
    axes[1,0].imshow(tissue_mask[:,:,slice_idx], cmap='gray')
    axes[1,0].set_title('Other Tissues\n(Remaining Labels)')
    axes[1,0].axis('off')
    
    # Plot air mask
    axes[1,1].imshow(air_mask[:,:,slice_idx], cmap='gray')
    axes[1,1].set_title('Air\n(Label 0)')
    axes[1,1].axis('off')
    
    plt.tight_layout()
    plt.show()

# Create interactive slider
interact(update_group_viewer, 
        slice_idx=widgets.IntSlider(
            min=0, 
            max=data_input.shape[2]-1, 
            step=1, 
            value=best_slice,
            description='Slice:',
            continuous_update=False
        ))

def compare_rv_wall_masks(data):
    """Create interactive viewer to compare original and cleaned RV wall masks"""
    original_rv_wall = (data == 2)
    cleaned_rv_wall = clean_rv_wall_mask(data)
    
    def update_comparison(slice_idx):
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # Plot original RV wall mask
        axes[0].imshow(original_rv_wall[:,:,slice_idx], cmap='gray')
        axes[0].set_title(f'Original RV Wall\nSlice {slice_idx}\n{original_rv_wall[:,:,slice_idx].sum()} pixels')
        axes[0].axis('off')
        
        # Plot RV blood for reference
        axes[1].imshow((data[:,:,slice_idx] == 6), cmap='gray')
        axes[1].set_title(f'RV Blood\nSlice {slice_idx}')
        axes[1].axis('off')
        
        # Plot cleaned RV wall mask
        axes[2].imshow(cleaned_rv_wall[:,:,slice_idx], cmap='gray')
        axes[2].set_title(f'Cleaned RV Wall\nSlice {slice_idx}\n{cleaned_rv_wall[:,:,slice_idx].sum()} pixels')
        axes[2].axis('off')
        
        plt.tight_layout()
        plt.show()
        
        # Print statistics for this slice
        print(f"\nSlice {slice_idx} statistics:")
        print(f"Original RV wall pixels: {original_rv_wall[:,:,slice_idx].sum()}")
        print(f"Cleaned RV wall pixels: {cleaned_rv_wall[:,:,slice_idx].sum()}")
        print(f"Removed pixels: {original_rv_wall[:,:,slice_idx].sum() - cleaned_rv_wall[:,:,slice_idx].sum()}")
    
    interact(update_comparison, 
            slice_idx=widgets.IntSlider(
                min=0, 
                max=data_input.shape[2]-1, 
                step=1, 
                value=best_slice,
                description='Slice:',
                continuous_update=False
            ))
    
    # Print overall statistics
    print("\nOverall statistics:")
    print(f"Total original RV wall voxels: {original_rv_wall.sum()}")
    print(f"Total cleaned RV wall voxels: {cleaned_rv_wall.sum()}")
    print(f"Total removed voxels: {original_rv_wall.sum() - cleaned_rv_wall.sum()}")
    print(f"Percentage of RV wall removed: {(1 - cleaned_rv_wall.sum()/original_rv_wall.sum())*100:.1f}%")

# Run the comparison
# compare_rv_wall_masks(data_input)

def create_cardiac_box_mask(cardiac_mask):
    """
    Create a bounding box mask around the cardiac tissues
    
    Parameters:
    -----------
    cardiac_mask : ndarray
        3D binary mask of cardiac tissues
    
    Returns:
    --------
    ndarray
        3D binary mask with a box surrounding all cardiac tissues
    """
    # Find the coordinates of non-zero elements (cardiac tissues)
    coords = np.where(cardiac_mask)
    
    if len(coords[0]) == 0:  # No cardiac tissues found
        return np.zeros_like(cardiac_mask), None, None, None, None, None, None
    
    # Get the min and max coordinates to create bounding box
    min_x, max_x = np.min(coords[0]), np.max(coords[0])
    min_y, max_y = np.min(coords[1]), np.max(coords[1])
    min_z, max_z = np.min(coords[2]), np.max(coords[2])
    
    # Create box mask
    box_mask = np.zeros_like(cardiac_mask)
    box_mask[min_x:max_x+1, min_y:max_y+1, min_z:max_z+1] = 1
    
    return box_mask, min_x, max_x, min_y, max_y, min_z, max_z

# Create the cardiac box mask and get the box coordinates
cardiac_box_mask, min_x, max_x, min_y, max_y, min_z, max_z = create_cardiac_box_mask(cardiac_mask)

# Visualization of the cardiac box mask
def compare_cardiac_and_box(data, min_x, max_x, min_y, max_y, min_z, max_z):
    """Create interactive viewer to compare cardiac mask and cardiac box mask"""
    
    def update_comparison(slice_idx):
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # Plot original data
        axes[0].imshow(data[:,:,slice_idx], cmap='gray')
        axes[0].set_title(f'Original Data\nSlice {slice_idx}')
        axes[0].axis('off')
        
        # Plot cardiac mask
        axes[1].imshow(cardiac_mask[:,:,slice_idx], cmap='gray')
        axes[1].set_title(f'Cardiac Tissues\nSlice {slice_idx}')
        axes[1].axis('off')
        
        # Plot cardiac box mask
        axes[2].imshow(cardiac_box_mask[:,:,slice_idx], cmap='gray')
        axes[2].set_title(f'Cardiac Box Mask\nSlice {slice_idx}')
        axes[2].axis('off')
        
        plt.tight_layout()
        plt.show()
        
        # Print statistics for this slice
        print(f"\nSlice {slice_idx} statistics:")
        print(f"Cardiac mask pixels: {cardiac_mask[:,:,slice_idx].sum()}")
        print(f"Cardiac box mask pixels: {cardiac_box_mask[:,:,slice_idx].sum()}")
        print(f"Box to cardiac ratio: {cardiac_box_mask[:,:,slice_idx].sum() / max(1, cardiac_mask[:,:,slice_idx].sum()):.2f}x")
    
    interact(update_comparison, 
            slice_idx=widgets.IntSlider(
                min=0, 
                max=data.shape[2]-1, 
                step=1, 
                value=best_slice,
                description='Slice:',
                continuous_update=False
            ))
    
    # Print overall statistics
    print("\nOverall statistics:")
    print(f"Total cardiac voxels: {cardiac_mask.sum()}")
    print(f"Total cardiac box voxels: {cardiac_box_mask.sum()}")
    print(f"Box to cardiac ratio: {cardiac_box_mask.sum() / cardiac_mask.sum():.2f}x")
    if min_x is not None:  # Check if coordinates were found
        print(f"Box dimensions: {max_x-min_x+1} x {max_y-min_y+1} x {max_z-min_z+1} voxels")
    else:
        print("No cardiac tissues found.")

# Run the comparison
compare_cardiac_and_box(data_input, min_x, max_x, min_y, max_y, min_z, max_z)

# Optional: Save the cardiac box mask for later use
# np.save('cardiac_box_mask.npy', cardiac_box_mask)

# If you need to visualize the box boundaries on the original data
def visualize_box_boundaries(data, box_mask, min_x, max_x, min_y, max_y, min_z, max_z):
    """Visualize the box boundaries overlaid on the original data"""
    
    def update_view(slice_idx):
        fig, ax = plt.subplots(1, 1, figsize=(8, 8))
        
        # Plot original data
        ax.imshow(data[:,:,slice_idx], cmap='gray')
        
        # Only add box if this slice is within the box range and coordinates were found
        if min_z is not None and min_z <= slice_idx <= max_z:
            # Add box outline
            rect = patches.Rectangle((min_y, min_x), max_y-min_y, max_x-min_x, 
                                    linewidth=2, edgecolor='r', facecolor='none')
            ax.add_patch(rect)
        
        ax.set_title(f'Cardiac Box Overlay\nSlice {slice_idx}')
        ax.axis('off')
        
        plt.tight_layout()
        plt.show()
    
    interact(update_view, 
            slice_idx=widgets.IntSlider(
                min=0, 
                max=data.shape[2]-1, 
                step=1, 
                value=best_slice,
                description='Slice:',
                continuous_update=False
            ))

# Import the necessary module for rectangles
from matplotlib import patches

# Run the box boundary visualization
visualize_box_boundaries(data_input, cardiac_box_mask, min_x, max_x, min_y, max_y, min_z, max_z)

# Save mask files
import os
import numpy as np

# Create directory for masks if it doesn't exist
output_dir = "./mask"
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# Save masks as NumPy files
mask_files = {
    'cardiac_mask': cardiac_mask,
    'lung_mask': lung_mask,
    'tissue_mask': tissue_mask,
    'air_mask': air_mask
}

# Save each mask
for name, mask in mask_files.items():
    file_path = os.path.join(output_dir, f"{name}.npy")
    np.save(file_path, mask)
    print(f"Saved {name} to {file_path} ({mask.sum()} voxels, shape: {mask.shape})")

print("\nAll masks saved successfully!")

# Optionally: Save as NIFTI files for use in imaging software
# try:
#     import nibabel as nib
    
#     # Create affine matrix (identity for now - adjust if you have spatial information)
#     affine = np.eye(4)
    
#     for name, mask in mask_files.items():
#         nii_path = os.path.join(mask_dir, f"{name}.nii.gz")
#         nii_img = nib.Nifti1Image(mask.astype(np.uint8), affine)
#         nib.save(nii_img, nii_path)
#         print(f"Saved NIFTI file: {nii_path}")
        
# except ImportError:
#     print("\nNote: nibabel not installed. Skipping NIFTI export.")
#     print("To install: pip install nibabel")

from scipy.fft import fftn, ifftn
import numpy as np
import matplotlib.pyplot as plt
import vtk
from vtk.util.numpy_support import vtk_to_numpy
import ipywidgets as widgets
from ipywidgets import interact
import psutil  # Add this for memory monitoring
import gc     # Add this for garbage collection

def create_susceptibility_map(cardiac_mask, lung_mask, tissue_mask, air_mask):
    """Create susceptibility map from masks"""
    chi_map = np.zeros_like(cardiac_mask, dtype=float)
    
    # Assign susceptibility values (in ppm)
    chi_map[cardiac_mask] = -9.0  # Cardiac tissue
    chi_map[tissue_mask] = -9.0   # Other tissues
    chi_map[lung_mask] = 0.1     # Mix of air and tissue; varies with inflation
    chi_map[air_mask] = 0.36      # Air

    chi_map_Axial = chi_map.transpose(1, 2, 0)  # Transpose to match the original orientation
    chi_map_Coronal = chi_map.transpose(0, 2, 1)   # Transpose to match the original orientation
    chi_map_Sagittal = chi_map # Transpose to match the original orientation
    
    return chi_map_Axial, chi_map_Coronal, chi_map_Sagittal

def create_detailed_susceptibility_map(data_input, maskLabels,air_mask):
    """Create susceptibility map with detailed tissue-specific values"""
    chi_map = np.zeros_like(data_input, dtype=float)
    
    # Default soft tissue (diamagnetic)
    chi_map.fill(-9.0)
    
    # Air-containing structures (paramagnetic)
    chi_map[data_input == maskLabels.airways] = 0.36
    chi_map[data_input == maskLabels.trachea_bronchi] = 0.36
    chi_map[data_input == maskLabels.right_lung] = 0.1
    chi_map[data_input == maskLabels.left_lung] = 0.1
    chi_map[air_mask] = 0.36  # External air
    
    # Bone (more diamagnetic than soft tissue)
    chi_map[data_input == maskLabels.ribs] = -11.0
    chi_map[data_input == maskLabels.cortical_bone] = -11.3
    chi_map[data_input == maskLabels.spine] = -11.0
    
    # Blood pools (oxygen-dependent)
    # Arterial blood (oxygenated)
    chi_map[data_input == maskLabels.LV_blood] = -9.25
    chi_map[data_input == maskLabels.LA_blood] = -9.25
    chi_map[data_input == maskLabels.arteries] = -9.25
    
    # Venous blood (deoxygenated)
    chi_map[data_input == maskLabels.RV_blood] = -8.85
    chi_map[data_input == maskLabels.RA_blood] = -8.85
    chi_map[data_input == maskLabels.veins] = -8.85
    
    # Fat-containing tissues (less diamagnetic)
    chi_map[data_input == maskLabels.Peri] = -8.8  # Pericardium
    chi_map[data_input == maskLabels.bone_marrow] = -8.7

    chi_map_Axial = chi_map.transpose(1, 2, 0)  # Transpose to match the original orientation
    chi_map_Coronal = chi_map.transpose(0, 2, 1)   # Transpose to match the original orientation
    chi_map_Sagittal = chi_map # Transpose to match the original orientation
    
    return chi_map_Axial, chi_map_Coronal, chi_map_Sagittal

def calculate_b0_field(chi_map, voxel_size, B0=3.0, pad_factor=0.1, slice_range=100):
    """Calculate B0 field with reduced data size"""
    # Get center slice index
    b0_field_full = np.zeros(chi_map.shape, dtype=np.float32)
    center_slice = chi_map.shape[2] // 2
    start_slice = center_slice - slice_range//2
    end_slice = center_slice + slice_range//2
    print(f"Center slice: {center_slice}, Start slice: {start_slice}, End slice: {end_slice}")
    
    start_slice = 0
    end_slice = chi_map.shape[2]
    # Extract central portion of the data
    chi_map = chi_map[:, :, start_slice:end_slice]
    print(f"\nReduced data shape: {chi_map.shape}")
    
    # Debug input types and shapes
    print("\nDebug Information:")
    print(f"Input chi_map type: {type(chi_map)}")
    print(f"Input chi_map dtype: {chi_map.dtype}")
    print(f"Input voxel_size type: {type(voxel_size)}")
    
    # Convert to numpy arrays with explicit types
    chi_map = np.asarray(chi_map, dtype=np.float32)
    voxel_size = np.asarray(voxel_size, dtype=np.float32)
    
    # Convert units and check ranges
    chi_map_SI = chi_map * 1e-6
    voxel_size_m = voxel_size * 1e-2
    
    print(f"\nConversion check:")
    print(f"chi_map_SI range: [{chi_map_SI.min():.2e}, {chi_map_SI.max():.2e}]")
    print(f"voxel_size_m: {voxel_size_m}")
    
    # Calculate padding
    Nx, Ny, Nz = chi_map_SI.shape
    print(f"Original shape: {chi_map_SI.shape}")
    pad_x = int(Nx * pad_factor)
    pad_y = int(Ny * pad_factor)
    pad_z = int(Nz * pad_factor)

    # Calculate memory requirements
    padded_size = (Nx + 2*pad_x) * (Ny + 2*pad_y) * (Nz + 2*pad_z) * 8  # 8 bytes per complex64
    available_mem = psutil.virtual_memory().available
    
    if padded_size > available_mem * 0.5:  # Use only 50% of available memory
        scale_factor = np.sqrt(available_mem * 0.5 / padded_size)
        pad_x = int(pad_x * scale_factor)
        pad_y = int(pad_y * scale_factor)
        pad_z = int(pad_z * scale_factor)
        print(f"Reducing padding to fit memory: ({pad_x}, {pad_y}, {pad_z})")
    
    print(f"Original shape: {chi_map_SI.shape}")
    print(f"Padding sizes: ({pad_x}, {pad_y}, {pad_z})")
    
    # Create padded array with explicit zero padding
    padded_map = np.pad(chi_map_SI, 
                        ((pad_x, pad_x), (pad_y, pad_y), (pad_z, pad_z)),
                        mode='constant', constant_values=0)
    print(f"Padded shape: {padded_map.shape}")
    
    # Perform FFT
    chi_FT = fftn(padded_map.astype(np.complex64))
    # Create an array with explicit dtype
    # chi_FT_complex = np.zeros_like(chi_FT, dtype=np.complex64)
    # chi_FT_complex[:] = chi_FT
    
    # print("\nFFT check:")
    # print(f"FFT output shape: {chi_FT.shape}")
    # print(f"FFT output dtype: {chi_FT.dtype}")
    # print(f"FFT is finite: {np.all(np.isfinite(chi_FT))}")


    voxel_size_m = np.float32(voxel_size_m)
    
    # Create k-space coordinates
    print("\nVoxel size check:")
    print(f"voxel_size_m type: {type(voxel_size_m)}")
    print(f"voxel_size_m: {voxel_size_m}")

        # Get voxel sizes as scalars
    dx = float(voxel_size_m[0])
    dy = float(voxel_size_m[1])
    dz = float(voxel_size_m[2])
    
    print("\nVoxel size check:")
    print(f"dx, dy, dz: {dx:.6f}, {dy:.6f}, {dz:.6f}")
    
    # Create k-space coordinates using scalar values
    kx = np.fft.fftfreq(Nx+2*pad_x, dx) * (2 * np.pi)
    ky = np.fft.fftfreq(Ny+2*pad_y, dy) * (2 * np.pi)
    kz = np.fft.fftfreq(Nz+2*pad_z, dz) * (2 * np.pi)

    # # Get dimensions of padded array
    # nx, ny, nz = padded_map.shape
    
    # # Create k-space coordinates using integer indices first
    # kx_indices = np.arange(-nx//2, nx//2)
    # ky_indices = np.arange(-ny//2, ny//2)
    # kz_indices = np.arange(-nz//2, nz//2)
    
    # # Convert to physical k-space coordinates
    # dx, dy, dz = [float(v) for v in voxel_size_m]
    # kx = (2 * np.pi * kx_indices / (nx * dx)).astype(np.float32)
    # ky = (2 * np.pi * ky_indices / (ny * dy)).astype(np.float32)
    # kz = (2 * np.pi * kz_indices / (nz * dz)).astype(np.float32)
    
    # Create k-space grid with explicit dtype
    KX, KY, KZ = np.meshgrid(kx, ky, kz, indexing='ij')
    KX = KX.astype(np.float32)
    KY = KY.astype(np.float32)
    KZ = KZ.astype(np.float32)
    
    # Calculate K2 with improved numerical stability
    K2 = KX**2 + KY**2 + KZ**2
    K2 = K2.astype(np.float32)
    
    with np.errstate(divide='ignore', invalid='ignore'):
        D = np.where(K2 > 0,
                    1/3 - (KZ**2)/K2,
                    0)
        
    # Ensure both arrays have compatible complex dtypes
    D = D.astype(np.complex64)
    
    print("\nArray type check before multiplication:")
    print(f"D dtype: {D.dtype}")
    print(f"D shape: {D.shape}")
    # print(f"chi_FT dtype: {chi_FT.dtype}")
    print(f"chi_FT shape: {chi_FT.shape}")
    
    # Calculate field in k-space
    # 直接进行乘法运算
    field_FT = (D * chi_FT).astype(np.complex64)
    
    # print("\nField calculation check:")
    # print(f"field_FT shape: {field_FT.shape}")
    # print(f"field_FT dtype: {field_FT.dtype}")
    
    # Inverse FFT
    field = ifftn(field_FT)
    field = field[pad_x:-pad_x, pad_y:-pad_y, pad_z:-pad_z]
    
    # print("\nFinal field check:")
    # print(f"field shape: {field.shape}")
    # print(f"field dtype: {field.dtype}")
    
    # Convert to Hz
    gamma = 42.577478518e6  # Hz/T
    field_Hz = np.real(field) * B0 * gamma
    
    print("\nFrequency conversion check:")
    print(f"field_Hz shape: {field_Hz.shape}")
    # print(f"field_Hz dtype: {field_Hz.dtype}")
    print(f"field_Hz range: [{field_Hz.min():.2f}, {field_Hz.max():.2f}] Hz")
    
    # Insert calculated field into the center portion
    b0_field_full[:, :, start_slice:end_slice] = field_Hz
    
    
    return field_Hz, b0_field_full
    

# Calculate B0 field
B0_strength = 3  # Tesla

# Create susceptibility map
# chi_map_Axial,  chi_map_Coronal, chi_map_Sagittal = create_susceptibility_map(cardiac_mask, lung_mask, tissue_mask, air_mask)

chi_map_Axial,  chi_map_Coronal, chi_map_Sagittal = create_detailed_susceptibility_map(data_input, maskLabels,air_mask)

voxel_size = [0.2,0.2,0.2]
# Calculate B0 field with reduced data
slice_range = 200  # Define slice_range as a global variable
print(f"Memory available: {psutil.virtual_memory().available / 1e9:.1f} GB")
field_Hz, b0_field = calculate_b0_field(chi_map_Axial, voxel_size, B0=B0_strength, 
                            pad_factor=0.1, slice_range=slice_range)



#Interactive viewer for both maps
def update_field_viewer(slice_idx):
    """Visualize B0 field with zero-padding for reduced slices"""
    global slice_range  # Access the global variable
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))
    
    # Get the center slice information
    center_slice = chi_map_Axial.shape[2] // 2
    start_slice = center_slice - slice_range//2
    end_slice = center_slice + slice_range//2
    
    # Add information about calculated range
    print("\nSlice range information:")
    print(f"Full volume: {chi_map_Axial.shape[2]} slices")
    print(f"Calculated range: slices {start_slice} to {end_slice-1}")
    print(f"Zero-padded: slices 0 to {start_slice-1} and {end_slice} to {chi_map_Axial.shape[2]-1}")

    print("Input shapes and values:")
    print(f"chi_map shape: {chi_map_Axial.shape}")
    print(f"voxel_size: {voxel_size}")
    print(f"chi_map range: [{chi_map_Axial.min():.2f}, {chi_map_Axial.max():.2f}] ppm")
    # Plot susceptibility map
    im1 = ax1.imshow(chi_map_Axial[:,:,slice_idx], cmap='bwr', vmin=-9.0, vmax=0.36)
    plt.colorbar(im1, ax=ax1, label='Susceptibility (ppm)')
    ax1.set_title(f'Susceptibility Map\nSlice {slice_idx}')
    ax1.axis('off')
    
    # Plot B0 field if available
    if b0_field is not None:
        im2 = ax2.imshow(b0_field[:,:,slice_idx], cmap='jet')
        plt.colorbar(im2, ax=ax2, label='Frequency Shift (Hz)')
        
        # Check if this slice was calculated or zero-padded
        if start_slice <= slice_idx < end_slice:
            ax2.set_title(f'B0 Field Map\nSlice {slice_idx} (Calculated)')
        else:
            ax2.set_title(f'B0 Field Map\nSlice {slice_idx} (Zero-padded)')
    else:
        ax2.text(0.5, 0.5, 'B0 calculation failed', ha='center', va='center')
    ax2.axis('off')
    
    plt.tight_layout()
    plt.show()

# Create interactive slider with full range
interact(update_field_viewer, 
        slice_idx=widgets.IntSlider(
            min=0, 
            max=chi_map_Axial.shape[2]-1,  # Use full range
            step=1, 
            value=chi_map_Axial.shape[2]//2,  # Start at center slice
            description='Slice:',
            continuous_update=False,
            style={'description_width': 'initial'}
        ))


import numpy as np
import matplotlib.pyplot as plt
import ipywidgets as widgets
from ipywidgets import interact
import os

# Load the saved results
output_dir = "/Users/<USER>/Documents/important document backup/Local python code/MRXCAT2/mrxcat-2.0-main/B0_output"
field_Hz_file = "field_Hz_20250327_105540.npy"  # 使用您的实际文件名
b0_field_file = "b0_field_20250327_105540.npy"  # 使用您的实际文件名

# field_Hz_file = "field_Hz_20250326_142439.npy"  # 使用您的实际文件名
# b0_field_file = "b0_field_20250326_142439.npy"  # 使用您的实际文件名

field_Hz = np.load(os.path.join(output_dir, field_Hz_file))
b0_field = np.load(os.path.join(output_dir, b0_field_file))

print(f"Loaded field_Hz: {field_Hz.shape}")
print(f"Loaded b0_field: {b0_field.shape}")

# # Interactive viewer for both maps
def update_field_viewer(slice_idx):
    """Visualize B0 field with zero-padding for reduced slices"""
    global slice_range  # Access the global variable
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))
    
    # Get the center slice information
    center_slice = chi_map_Axial.shape[2] // 2
    start_slice = center_slice - slice_range//2
    end_slice = center_slice + slice_range//2
    
    # Add information about calculated range
    print("\nSlice range information:")
    print(f"Full volume: {chi_map_Axial.shape[2]} slices")
    print(f"Calculated range: slices {start_slice} to {end_slice-1}")
    print(f"Zero-padded: slices 0 to {start_slice-1} and {end_slice} to {chi_map_Axial.shape[2]-1}")

    print("Input shapes and values:")
    print(f"chi_map shape: {chi_map_Axial.shape}")
    print(f"voxel_size: {voxel_size}")
    print(f"chi_map range: [{chi_map_Axial.min():.2f}, {chi_map_Axial.max():.2f}] ppm")
    # Plot susceptibility map
    im1 = ax1.imshow(chi_map_Axial[:,:,slice_idx], cmap='bwr', vmin=-9.0, vmax=0.36)
    plt.colorbar(im1, ax=ax1, label='Susceptibility (ppm)')
    ax1.set_title(f'Susceptibility Map\nSlice {slice_idx}')
    ax1.axis('off')
    
    # Plot B0 field if available
    if b0_field is not None:
        im2 = ax2.imshow(b0_field[:,:,slice_idx], cmap='jet')
        plt.colorbar(im2, ax=ax2, label='Frequency Shift (Hz)')
        
        # Check if this slice was calculated or zero-padded
        if start_slice <= slice_idx < end_slice:
            ax2.set_title(f'B0 Field Map\nSlice {slice_idx} (Calculated)')
        else:
            ax2.set_title(f'B0 Field Map\nSlice {slice_idx} (Zero-padded)')
    else:
        ax2.text(0.5, 0.5, 'B0 calculation failed', ha='center', va='center')
    ax2.axis('off')
    
    plt.tight_layout()
    plt.show()

# Create interactive slider with full range
interact(update_field_viewer, 
        slice_idx=widgets.IntSlider(
            min=0, 
            max=chi_map_Axial.shape[2]-1,  # Use full range
            step=1, 
            value=chi_map_Axial.shape[2]//2,  # Start at center slice
            description='Slice:',
            continuous_update=False,
            style={'description_width': 'initial'}
        ))

# 打印B0场的统计信息
print(f"B0 field statistics:")
print(f"Min: {b0_field.min():.2f} Hz")
print(f"Max: {b0_field.max():.2f} Hz")
print(f"Mean: {b0_field.mean():.2f} Hz")
print(f"Std: {b0_field.std():.2f} Hz")

print("\nFrequency conversion check:")
print(f"field_Hz shape: {field_Hz.shape}")
# print(f"field_Hz dtype: {field_Hz.dtype}")
print(f"field_Hz range: [{field_Hz.min():.2f}, {field_Hz.max():.2f}] Hz")