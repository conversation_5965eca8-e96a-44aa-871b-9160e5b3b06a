import numpy as np
import os
import vtk
from vtk.util.numpy_support import vtk_to_numpy, numpy_to_vtk

class ImageConverter:
    def __init__(self):
        self.read_img = vtk.vtkXMLImageDataReader()
        
    def get_parameters_from_log(self, log_file_path):
        """Read parameters from the log file"""
        with open(log_file_path, 'r') as infile:
            data = infile.readlines()

        params = {}
        for l_sel in data:
            if 'pixel width =' in l_sel:
                params['res_plane'] = self.get_values_mapping(l_sel)[0] * 0.01
            elif 'slice width =' in l_sel:
                params['res_slice'] = self.get_values_mapping(l_sel)[0] * 0.01
            elif 'array_size =' in l_sel:
                params['arr_size'] = int(self.get_values_mapping(l_sel)[0])
            elif 'starting slice number ' in l_sel:
                params['sl_start'] = int(self.get_values_mapping(l_sel)[0])
            elif 'ending slice number ' in l_sel:
                params['sl_end'] = int(self.get_values_mapping(l_sel)[0])

        # Set image dimensions and spacing
        params['dims_xcat'] = (params['sl_end']-params['sl_start']+1, 
                             params['arr_size'], 
                             params['arr_size'])
        params['dims_np'] = (params['arr_size'], 
                           params['arr_size'], 
                           params['sl_end']-params['sl_start']+1)
        params['spacing'] = [params['res_plane'], 
                           params['res_plane'], 
                           params['res_slice']]
        
        return params

    def get_values_mapping(self, s):
        """Extract numerical values from a string"""
        varstring = ''.join((ch if ch in '0123456789.' else ' ') for ch in s)
        return [float(i) for i in varstring.split()]

    def convert_bin_to_vti(self, bin_file_path, output_vti_path, params):
        try:
            # Read BIN file with float32 data type
            x = np.fromfile(bin_file_path, dtype=np.float32)
            
            # Calculate actual dimensions
            total_elements = x.size
            slice_elements = params['arr_size'] * params['arr_size']
            actual_slices = total_elements // slice_elements
            
            # Reshape data - IMPORTANT: VTK expects dimensions in (X, Y, Z) order
            # The reshape should match how the data is stored in the binary file
            image = x.reshape((params['arr_size'], params['arr_size'], actual_slices), order='F')
            
            # Create VTK image with proper dimensions
            vti_image = vtk.vtkImageData()
            vti_image.SetDimensions(params['arr_size'], params['arr_size'], actual_slices)
            vti_image.SetSpacing(params['spacing'][0], params['spacing'][1], params['spacing'][2])
            vti_image.SetOrigin(0, 0, 0)
            
            # Allocate scalars - IMPORTANT: Must be done before setting array data
            vti_image.AllocateScalars(vtk.VTK_FLOAT, 1)
            
            # Flatten the data in the correct order for VTK
            flat_data = image.flatten(order='F')  # VTK uses Fortran order (column-major)
            
            # Convert numpy array to VTK array
            vtk_array = numpy_to_vtk(
                num_array=flat_data,
                deep=True,
                array_type=vtk.VTK_FLOAT
            )
            
            # Set array name and attach to point data
            vtk_array.SetName('labels')
            vti_image.GetPointData().SetScalars(vtk_array)
            
            # Write VTI file using binary format for better compatibility
            writer = vtk.vtkXMLImageDataWriter()
            writer.SetFileName(output_vti_path)
            writer.SetInputData(vti_image)
            writer.SetDataModeToBinary()  # Use binary mode instead of appended
            writer.Write()
            
            print(f"Successfully converted file.")
            print(f"Input shape: {image.shape}")
            print(f"Output dimensions: {vti_image.GetDimensions()}")
            
            # Verify the file was created correctly
            test_reader = vtk.vtkXMLImageDataReader()
            test_reader.SetFileName(output_vti_path)
            if test_reader.CanReadFile(output_vti_path):
                print("Generated VTI file is valid and can be read by VTK.")
            else:
                print("WARNING: Generated VTI file may not be valid!")
            
        except Exception as e:
            print(f"Error converting file: {e}")
            raise

def main():
    converter = ImageConverter()
    
    # Input and output paths
    input_bin = "/Users/<USER>/Documents/important document backup/Local python code/MRXCAT2/mrxcat-2.0-main/male169/male_169.samp_act.bin"
    output_folder = "/Users/<USER>/Documents/important document backup/Local python code/MRXCAT2/mrxcat-2.0-main/male169/"
    
    # Get the log file path (assumed to be in the same directory)
    log_file = os.path.join(os.path.dirname(input_bin), "male_169.samp_log")
    
    if not os.path.exists(log_file):
        print(f"Warning: Log file not found at {log_file}")
        print("Please make sure the log file exists and has the correct name")
        return
    
    # Read parameters from log file
    params = converter.get_parameters_from_log(log_file)
    print("Parameters from log file:")
    for key, value in params.items():
        print(f"{key}: {value}")
    
    # Ensure output directory exists
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
    
    # Generate output filename
    output_vti = os.path.join(output_folder, 
                             os.path.splitext(os.path.basename(input_bin))[0] + '_fixed.vti')
    
    # Convert file
    converter.convert_bin_to_vti(input_bin, output_vti, params)
    print(f"Converted {input_bin} to {output_vti}")

if __name__ == "__main__":
    main()