import numpy as np
import os
import vtk
from vtk.util.numpy_support import vtk_to_numpy, numpy_to_vtk
import matplotlib.pyplot as plt

class ImageConverter:
    def __init__(self):
        self.read_img = vtk.vtkXMLImageDataReader()
        
    def get_parameters_from_log(self, log_file_path):
        """从log文件读取参数"""
        with open(log_file_path, 'r') as infile:
            data = infile.readlines()

        params = {}
        for l_sel in data:
            if 'pixel width =' in l_sel:
                params['res_plane'] = self.get_values_mapping(l_sel)[0] * 0.01
            elif 'slice width =' in l_sel:
                params['res_slice'] = self.get_values_mapping(l_sel)[0] * 0.01
            elif 'array_size =' in l_sel:
                params['arr_size'] = int(self.get_values_mapping(l_sel)[0])
            elif 'starting slice number ' in l_sel:
                params['sl_start'] = int(self.get_values_mapping(l_sel)[0])
            elif 'ending slice number ' in l_sel:
                params['sl_end'] = int(self.get_values_mapping(l_sel)[0])

        # 设置图像尺寸和间距
        params['dims_xcat'] = (params['sl_end']-params['sl_start']+1, 
                             params['arr_size'], 
                             params['arr_size'])
        params['dims_np'] = (params['arr_size'], 
                           params['arr_size'], 
                           params['sl_end']-params['sl_start']+1)
        params['spacing'] = [params['res_plane'], 
                           params['res_plane'], 
                           params['res_slice']]
        
        return params

    def get_values_mapping(self, s):
        """从字符串中提取数值"""
        varstring = ''.join((ch if ch in '0123456789.' else ' ') for ch in s)
        return [float(i) for i in varstring.split()]

    def convert_bin_to_vti(self, bin_file_path, output_vti_path, params):
        try:
            # 读取bin文件，使用float32
            x = np.fromfile(bin_file_path, dtype=np.float32)
            
            # 计算实际维度
            total_elements = x.size
            slice_elements = params['arr_size'] * params['arr_size']
            actual_slices = total_elements // slice_elements
            
            # 直接使用BIN文件的维度顺序 (490, 256, 256)
            image = x.reshape((actual_slices, params['arr_size'], params['arr_size']))
            
            # 创建VTK图像 - 注意维度顺序
            vti_image = vtk.vtkImageData()
            vti_image.SetDimensions(actual_slices, params['arr_size'], params['arr_size'])
            vti_image.SetSpacing(params['spacing'])
            vti_image.SetOrigin(0, 0, 0)
            
            # 设置数据类型为float32
            vti_image.AllocateScalars(vtk.VTK_FLOAT, 1)
            
            # 直接使用原始数据排列
            flat_data = image.ravel(order='F')
            vtk_array = numpy_to_vtk(
                num_array=flat_data,
                deep=True,
                array_type=vtk.VTK_FLOAT
            )
            vtk_array.SetName('labels')
            vti_image.GetPointData().SetScalars(vtk_array)
            
            # 保存VTI文件
            writer = vtk.vtkXMLImageDataWriter()
            writer.SetFileName(output_vti_path)
            writer.SetInputData(vti_image)
            writer.Write()
            
            print(f"Successfully converted file.")
            print(f"Input shape: {image.shape}")
            print(f"Output dimensions: {vti_image.GetDimensions()}")
            
        except Exception as e:
            print(f"Error converting file: {e}")
            raise

    def analyze_tissue_values(self, bin_file_path):
        """分析.bin文件中的组织标签"""
        # 改用float32
        x = np.fromfile(bin_file_path, dtype=np.float32)
        unique_values = np.unique(x)
        
        # Common XCAT tissue values (这些值需要根据实际情况调整)
        tissue_map = {
            1: 'LV_myocardium',
            2: 'RV_myocardium',
            3: 'LA_myocardium',
            4: 'RA_myocardium',
            5: 'LV_blood',
            6: 'RV_blood',
            7: 'LA_blood',
            8: 'RA_blood',
            9: 'body',
            10: 'muscle',
            11: 'brain',
            12: 'sinus',
            13: 'liver',
            14: 'gall_bladder',
            15: 'right_lung',
            16: 'left_lung',
            17: 'esophagus',
            18: 'esophagus_contents',
            19: 'laryngopharynx',
            20: 'stomach_wall',
            21: 'stomach_contents',
            22: 'pancreas',
            23: 'right_kidney_cortex',
            24: 'right_kidney_medulla',
            25: 'left_kidney_cortex',
            26: 'left_kidney_medulla',
            27: 'adrenal',
            28: 'right_renal_pelvis',
            29: 'left_renal_pelvis',
            30: 'spleen',
            31: 'ribs',
            32: 'cortical_bone',
            33: 'spine',
            34: 'spinal_cord',
            35: 'bone_marrow',
            36: 'arteries',
            37: 'veins',
            38: 'bladder',
            39: 'prostate',
            40: 'ascending_intestine',
            41: 'transverse_intestine',
            42: 'descending_intestine',
            43: 'small_intestine',
            44: 'rectum',
            45: 'seminal_vesicles',
            46: 'vas_deferens',
            47: 'testes',
            48: 'epididymis',
            49: 'ejaculatory_duct',
            50: 'pericardium',
            51: 'cartilage',
            52: 'intestine_air',
            53: 'ureter',
            54: 'urethra',
            55: 'lymph',
            56: 'lymph_abnormal',
            57: 'trachea_bronchi',
            58: 'airways',
            59: 'uterus',
            60: 'vagina',
            61: 'right_ovary',
            62: 'left_ovary',
            63: 'fallopian_tubes',
            64: 'parathyroid',
            65: 'thyroid',
            66: 'thymus',
            67: 'salivary',
            68: 'pituitary',
            69: 'eye',
            70: 'lens',
            71: 'lesion'
            # Add more tissue types as needed
        }
        
        print("\nTissue Analysis:")
        print("-" * 40)
        for value in unique_values:
            count = np.sum(x == value)
            percentage = (count / len(x)) * 100
            tissue_name = tissue_map.get(value, f"Unknown tissue type {value}")
            print(f"Value {value}: {tissue_name}")
            print(f"  Count: {count}, Percentage: {percentage:.2f}%")
        print("-" * 40)

def main():
    converter = ImageConverter()
    
    # 输入输出路径
    input_bin = "/Users/<USER>/Documents/important document backup/Local python code/MRXCAT2/mrxcat-2.0-main/female50/singleframe_act_1.bin"
    output_folder = "/Users/<USER>/Documents/important document backup/Local python code/MRXCAT2/mrxcat-2.0-main/female50/"
    
    # 获取log文件路径（假设在同一目录下）
    log_file = os.path.join(os.path.dirname(input_bin), "singleframe_log")
    
    if not os.path.exists(log_file):
        print(f"Warning: Log file not found at {log_file}")
        print("Please make sure the log file exists and has the correct name")
        return
    
    # 从log文件读取参数
    params = converter.get_parameters_from_log(log_file)
    print("Parameters from log file:")
    for key, value in params.items():
        print(f"{key}: {value}")
    
    # 确保输出目录存在
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
    
    # 生成输出文件名
    output_vti = os.path.join(output_folder, 
                             os.path.splitext(os.path.basename(input_bin))[0] + '.vti')
    
    # 分析组织值
    print("\nAnalyzing tissue values in the bin file...")
    converter.analyze_tissue_values(input_bin)
    
    # 转换文件
    converter.convert_bin_to_vti(input_bin, output_vti, params)
    print(f"Converted {input_bin} to {output_vti}")

if __name__ == "__main__":
    main()