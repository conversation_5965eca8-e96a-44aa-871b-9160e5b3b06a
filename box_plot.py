#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Box Plot Analysis Script
This script creates box plots to visualize the distribution of data across different groups.
"""

# Import necessary libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats

# Set style for better visualization
plt.style.use('seaborn')
sns.set_palette('husl')

# Set figure size and DPI for better quality
plt.rcParams['figure.figsize'] = (12, 6)
plt.rcParams['figure.dpi'] = 100

def create_boxplot(data, title='Box Plot', ylabel='Value', show_stats=True):
    """
    Create a customized box plot with statistical annotations.
    
    Parameters:
    -----------
    data : pandas.DataFrame
        Input data where each column represents a group
    title : str
        Title of the plot
    ylabel : str
        Label for the y-axis
    show_stats : bool
        Whether to show statistical annotations
    """
    # Create figure and axis
    fig, ax = plt.subplots(figsize=(12, 6))
    
    # Create box plot
    box = ax.boxplot([data[col] for col in data.columns],
                     labels=data.columns,
                     patch_artist=True,
                     medianprops={'color': 'black', 'linewidth': 2},
                     flierprops={'marker': 'o', 'markerfacecolor': 'red', 'markersize': 8})
    
    # Customize box colors
    colors = sns.color_palette('husl', n_colors=len(data.columns))
    for patch, color in zip(box['boxes'], colors):
        patch.set_facecolor(color)
    
    # Add statistical annotations if requested
    if show_stats:
        for i, col in enumerate(data.columns):
            stats_text = f'n={len(data[col])}\n'
            stats_text += f'Mean={data[col].mean():.2f}\n'
            stats_text += f'Median={data[col].median():.2f}\n'
            stats_text += f'Std={data[col].std():.2f}'
            
            ax.text(i+1, ax.get_ylim()[0], stats_text,
                   horizontalalignment='center',
                   verticalalignment='bottom',
                   bbox=dict(facecolor='white', alpha=0.8))
    
    # Customize the plot
    ax.set_title(title, fontsize=14, pad=20)
    ax.set_ylabel(ylabel, fontsize=12)
    ax.grid(True, linestyle='--', alpha=0.7)
    
    # Add a light gray background
    ax.set_facecolor('#f8f9fa')
    fig.patch.set_facecolor('white')
    
    # Rotate x-axis labels if needed
    plt.xticks(rotation=45)
    
    # Adjust layout
    plt.tight_layout()
    
    return fig, ax

def perform_statistical_tests(data):
    """
    Perform statistical tests between groups.
    """
    groups = list(data.columns)
    results = []
    
    for i in range(len(groups)):
        for j in range(i+1, len(groups)):
            group1 = groups[i]
            group2 = groups[j]
            
            # Perform t-test
            t_stat, p_val = stats.ttest_ind(data[group1], data[group2])
            
            results.append({
                'Group 1': group1,
                'Group 2': group2,
                't-statistic': t_stat,
                'p-value': p_val
            })
    
    return pd.DataFrame(results)

def main():
    # For demonstration, let's create some sample data
    np.random.seed(42)
    data = {
        'Group A': np.random.normal(100, 15, 100),
        'Group B': np.random.normal(110, 20, 100),
        'Group C': np.random.normal(95, 25, 100)
    }
    df = pd.DataFrame(data)
    
    # Create the box plot
    fig, ax = create_boxplot(df, 
                            title='Distribution of Values Across Groups',
                            ylabel='Measurement Value',
                            show_stats=True)
    
    # Show the plot
    plt.show()
    
    # Perform and display statistical tests
    stats_results = perform_statistical_tests(df)
    print("\nStatistical Test Results:")
    print(stats_results)

if __name__ == "__main__":
    main() 