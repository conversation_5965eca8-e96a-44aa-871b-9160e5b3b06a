"""
TorchKbNufft Basics Tutorial
============================

This tutorial covers the fundamental concepts and usage of torchkbnufft,
a GPU-accelerated non-uniform FFT library for MRI reconstruction.
"""

import torch
import torchkbnufft as tkbn
import numpy as np
import matplotlib.pyplot as plt

print("=== TorchKbNufft Basics Tutorial ===\n")

# 1. Basic Concepts
print("1. BASIC CONCEPTS")
print("=================")
print("TorchKbNufft provides GPU-accelerated non-uniform FFT operations for MRI reconstruction.")
print("Key components:")
print("- KbNufft: Forward NUFFT (image to k-space)")
print("- KbNufftAdjoint: Adjoint NUFFT (k-space to image)")
print("- KbInterp: Interpolation-based NUFFT")
print("- calc_density_compensation_function: Density compensation")
print()

# 2. Setting up device
print("2. DEVICE SETUP")
print("===============")
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")
print()

# 3. Creating sample data
print("3. SAMPLE DATA CREATION")
print("=======================")

# Create a simple 2D image (e.g., a circle)
im_size = (64, 64)
image = torch.zeros(im_size, dtype=torch.complex64, device=device)

# Create a circle in the center
y, x = torch.meshgrid(torch.arange(im_size[0]), torch.arange(im_size[1]), indexing='ij')
center = torch.tensor([im_size[0]//2, im_size[1]//2])
radius = 15
circle_mask = ((x - center[1])**2 + (y - center[0])**2) < radius**2
image[circle_mask] = 1.0 + 0.5j

print(f"Created {im_size} image with circle")
print(f"Image shape: {image.shape}")
print(f"Image dtype: {image.dtype}")
print()

# 4. Cartesian k-space trajectory
print("4. CARTESIAN K-SPACE TRAJECTORY")
print("===============================")

# Create Cartesian k-space coordinates
kx = torch.arange(-im_size[1]//2, im_size[1]//2, device=device) / (im_size[1]//2) * np.pi
ky = torch.arange(-im_size[0]//2, im_size[0]//2, device=device) / (im_size[0]//2) * np.pi
ky_grid, kx_grid = torch.meshgrid(ky, kx, indexing='ij')

# Flatten for trajectory
ktraj_cart = torch.stack([ky_grid.flatten(), kx_grid.flatten()], dim=0)
print(f"Cartesian trajectory shape: {ktraj_cart.shape}")
print(f"k-space range: [{ktraj_cart.min():.3f}, {ktraj_cart.max():.3f}]")
print()

# 5. Non-Cartesian k-space trajectory (spiral example)
print("5. NON-CARTESIAN K-SPACE TRAJECTORY")
print("===================================")

# Create spiral trajectory
num_spokes = 64
num_samples_per_spoke = 64
total_samples = num_spokes * num_samples_per_spoke

# Generate spiral angles and radii
angles = torch.linspace(0, 2*np.pi, num_spokes, device=device)
radii = torch.linspace(0, 1, num_samples_per_spoke, device=device)

# Create spiral coordinates
angle_grid, radius_grid = torch.meshgrid(angles, radii, indexing='ij')
kx_spiral = radius_grid * torch.cos(angle_grid) * np.pi
ky_spiral = radius_grid * torch.sin(angle_grid) * np.pi

# Flatten for trajectory
ktraj_spiral = torch.stack([ky_spiral.flatten(), kx_spiral.flatten()], dim=0)
print(f"Spiral trajectory shape: {ktraj_spiral.shape}")
print(f"Spiral k-space range: [{ktraj_spiral.min():.3f}, {ktraj_spiral.max():.3f}]")
print()

# 6. Forward NUFFT (Image to k-space)
print("6. FORWARD NUFFT")
print("================")

# Initialize forward NUFFT operator
nufft_ob = tkbn.KbNufft(im_size=im_size, device=device)

# Apply forward NUFFT to Cartesian trajectory
image_batch = image.unsqueeze(0).unsqueeze(0)  # Add batch and coil dimensions
ktraj_cart_batch = ktraj_cart.unsqueeze(0)     # Add batch dimension

kdata_cart = nufft_ob(image_batch, ktraj_cart_batch)
print(f"Forward NUFFT result shape: {kdata_cart.shape}")

# Apply forward NUFFT to spiral trajectory
ktraj_spiral_batch = ktraj_spiral.unsqueeze(0)
kdata_spiral = nufft_ob(image_batch, ktraj_spiral_batch)
print(f"Spiral forward NUFFT result shape: {kdata_spiral.shape}")
print()

# 7. Adjoint NUFFT (k-space to image)
print("7. ADJOINT NUFFT")
print("================")

# Initialize adjoint NUFFT operator
adjnufft_ob = tkbn.KbNufftAdjoint(im_size=im_size, device=device)

# Apply adjoint NUFFT
recon_cart = adjnufft_ob(kdata_cart, ktraj_cart_batch)
recon_spiral = adjnufft_ob(kdata_spiral, ktraj_spiral_batch)

print(f"Cartesian adjoint NUFFT result shape: {recon_cart.shape}")
print(f"Spiral adjoint NUFFT result shape: {recon_spiral.shape}")
print()

# 8. Density Compensation
print("8. DENSITY COMPENSATION")
print("=======================")

# Calculate density compensation for spiral trajectory
dcomp_spiral = tkbn.calc_density_compensation_function(
    ktraj_spiral_batch, 
    im_size, 
    num_iterations=10
)
print(f"Density compensation shape: {dcomp_spiral.shape}")

# Apply density compensation
kdata_spiral_dcomp = kdata_spiral * dcomp_spiral
recon_spiral_dcomp = adjnufft_ob(kdata_spiral_dcomp, ktraj_spiral_batch)
print("Applied density compensation to spiral reconstruction")
print()

# 9. Visualization
print("9. VISUALIZATION")
print("================")

# Move data to CPU for plotting
image_cpu = image.cpu()
recon_cart_cpu = recon_cart.squeeze().cpu()
recon_spiral_cpu = recon_spiral.squeeze().cpu()
recon_spiral_dcomp_cpu = recon_spiral_dcomp.squeeze().cpu()

# Create visualization
fig, axes = plt.subplots(2, 3, figsize=(15, 10))
fig.suptitle('TorchKbNufft Basics Demo', fontsize=16)

# Original image
axes[0, 0].imshow(torch.abs(image_cpu), cmap='gray')
axes[0, 0].set_title('Original Image')
axes[0, 0].axis('off')

# Cartesian trajectory
axes[0, 1].scatter(ktraj_cart[1, ::100].cpu(), ktraj_cart[0, ::100].cpu(), s=1, alpha=0.5)
axes[0, 1].set_title('Cartesian Trajectory (sampled)')
axes[0, 1].set_xlabel('kx')
axes[0, 1].set_ylabel('ky')
axes[0, 1].set_aspect('equal')

# Spiral trajectory
axes[0, 2].scatter(ktraj_spiral[1, ::100].cpu(), ktraj_spiral[0, ::100].cpu(), s=1, alpha=0.5)
axes[0, 2].set_title('Spiral Trajectory (sampled)')
axes[0, 2].set_xlabel('kx')
axes[0, 2].set_ylabel('ky')
axes[0, 2].set_aspect('equal')

# Cartesian reconstruction
axes[1, 0].imshow(torch.abs(recon_cart_cpu), cmap='gray')
axes[1, 0].set_title('Cartesian Reconstruction')
axes[1, 0].axis('off')

# Spiral reconstruction (no density comp)
axes[1, 1].imshow(torch.abs(recon_spiral_cpu), cmap='gray')
axes[1, 1].set_title('Spiral Reconstruction (no density comp)')
axes[1, 1].axis('off')

# Spiral reconstruction (with density comp)
axes[1, 2].imshow(torch.abs(recon_spiral_dcomp_cpu), cmap='gray')
axes[1, 2].set_title('Spiral Reconstruction (with density comp)')
axes[1, 2].axis('off')

plt.tight_layout()
plt.show()

# 10. Performance comparison
print("10. PERFORMANCE COMPARISON")
print("==========================")

# Time the operations
import time

# Forward NUFFT timing
start_time = time.time()
for _ in range(10):
    kdata_cart = nufft_ob(image_batch, ktraj_cart_batch)
forward_time = (time.time() - start_time) / 10

# Adjoint NUFFT timing
start_time = time.time()
for _ in range(10):
    recon_cart = adjnufft_ob(kdata_cart, ktraj_cart_batch)
adjoint_time = (time.time() - start_time) / 10

print(f"Forward NUFFT time: {forward_time*1000:.2f} ms")
print(f"Adjoint NUFFT time: {adjoint_time*1000:.2f} ms")
print()

# 11. Advanced features
print("11. ADVANCED FEATURES")
print("=====================")

# Interpolation-based NUFFT
print("Interpolation-based NUFFT:")
interp_ob = tkbn.KbInterp(im_size=im_size, device=device)
kdata_interp = interp_ob(image_batch, ktraj_cart_batch)
print(f"Interpolation result shape: {kdata_interp.shape}")

# Toeplitz-based NUFFT (for repeated trajectories)
print("\nToeplitz-based NUFFT:")
toep_ob = tkbn.ToepNufft(im_size=im_size, device=device)
kdata_toep = toep_ob(image_batch, ktraj_cart_batch)
print(f"Toeplitz result shape: {kdata_toep.shape}")

print("\n=== Tutorial Complete ===")
print("Key takeaways:")
print("1. TorchKbNufft provides fast GPU-accelerated NUFFT operations")
print("2. Use KbNufft for forward operations (image → k-space)")
print("3. Use KbNufftAdjoint for adjoint operations (k-space → image)")
print("4. Density compensation is crucial for non-Cartesian trajectories")
print("5. Different NUFFT types available for different use cases") 