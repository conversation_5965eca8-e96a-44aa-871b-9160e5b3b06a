{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Spiral Reconstruction Demo\n", "\n", "This notebook demonstrates how to use the improved spiral reconstruction methods to get better image quality."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import sys\n", "sys.path.append('.')\n", "import improved_spiral_recon as isr\n", "from scipy.ndimage import gaussian_filter"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Data from recon_cardiac.ipynb\n", "\n", "First, we need to load the data from the original notebook. Run the original notebook up to the point where you have:\n", "- `gre_t2star_image` or `image_data`\n", "- `gre_t2star_kspace` or `kspace`\n", "- Spiral trajectory parameters (`kx`, `ky`, `k_max`)\n", "\n", "Then run the following code to use our improved reconstruction:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# This is a placeholder - you should run this in the context of recon_cardiac.ipynb\n", "# or copy the relevant variables here\n", "\n", "# Example:\n", "# image_data = gre_t2star_image\n", "# kspace = gre_t2star_kspace\n", "# kx_spiral = kx\n", "# ky_spiral = ky\n", "# image_size = image_data.shape[0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Improved Spiral Reconstruction"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Convert k-space coordinates to pixel coordinates for visualization\n", "kx_pixel, ky_pixel = isr.k_to_pixel(kx_spiral, ky_spiral, image_size)\n", "\n", "# Visualize the spiral trajectory\n", "plt.figure(figsize=(15, 5))\n", "plt.subplot(1, 3, 1)\n", "plt.plot(kx_spiral, ky_spiral, 'b.', markersize=1)\n", "plt.title(\"k-space Spiral Trajectory (cycles/m)\")\n", "plt.axis('equal')\n", "plt.grid(True)\n", "theta_vis = np.linspace(0, 2*np.pi, 100)\n", "plt.plot(k_max*np.cos(theta_vis), k_max*np.sin(theta_vis), 'r--')\n", "\n", "plt.subplot(1, 3, 2)\n", "plt.plot(kx_pixel-image_size/2, ky_pixel-image_size/2, 'b.', markersize=1)\n", "plt.title(\"Spiral Trajectory (pixel coordinates)\")\n", "plt.axis('equal')\n", "plt.grid(True)\n", "plt.plot((image_size/2)*np.cos(theta_vis), (image_size/2)*np.sin(theta_vis), 'r--')\n", "\n", "# Create a coverage visualization\n", "kx_pixel_int = np.clip(np.round(kx_pixel).astype(int), 0, image_size - 1)\n", "ky_pixel_int = np.clip(np.round(ky_pixel).astype(int), 0, image_size - 1)\n", "coverage_mask = np.zeros((image_size, image_size))\n", "coverage_mask[ky_pixel_int, kx_pixel_int] = 1\n", "\n", "plt.subplot(1, 3, 3)\n", "plt.imshow(coverage_mask, cmap='binary')\n", "plt.title(\"Spiral Coverage Map\")\n", "plt.colorbar()\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Interpolate Cartesian k-space to spiral trajectory using improved method\n", "spiral_kspace = isr.cartesian_to_spiral(kspace, kx_spiral, ky_spiral, image_size)\n", "print(f'Length of spiral: {spiral_kspace.size}')\n", "\n", "# Create a sparse k-space representation for visualization\n", "sparse_kspace = np.zeros((image_size, image_size), dtype=complex)\n", "sparse_kspace[ky_pixel_int, kx_pixel_int] = spiral_kspace\n", "\n", "# Apply a small blur to make the spiral pattern more visible in the visualization\n", "blurred_viz = gaussian_filter(np.abs(sparse_kspace), sigma=1.5)\n", "\n", "# Display k-space results (raw and blurred for better visibility)\n", "plt.figure(figsize=(15, 5))\n", "plt.subplot(1, 3, 1)\n", "plt.imshow(np.log(np.abs(kspace) + 1e-10), cmap='gray')\n", "plt.title(\"Original k-space (Cartesian)\")\n", "plt.colorbar()\n", "\n", "plt.subplot(1, 3, 2)\n", "plt.imshow(np.log(np.abs(sparse_kspace) + 1e-10), cmap='gray')\n", "plt.title(\"Spiral Sampled k-space\")\n", "plt.colorbar()\n", "\n", "plt.subplot(1, 3, 3)\n", "plt.imshow(np.log(blurred_viz + 1e-10), cmap='gray')\n", "plt.title(\"Spiral Sampled k-space (Blurred for Visibility)\")\n", "plt.colorbar()\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Perform improved reconstruction from spiral k-space\n", "reconstructed_image = isr.reconstruct_from_spiral(spiral_kspace, kx_spiral, ky_spiral, image_size)\n", "reconstructed_magnitude = np.abs(reconstructed_image)\n", "\n", "# For comparison, also do a simple FFT reconstruction from the sparse k-space\n", "simple_recon = np.fft.ifft2(np.fft.ifftshift(sparse_kspace))\n", "simple_recon_magnitude = np.abs(simple_recon)\n", "\n", "# Display original image vs reconstructions\n", "plt.figure(figsize=(15, 5))\n", "plt.subplot(1, 3, 1)\n", "plt.imshow(image_data, cmap='gray')\n", "plt.title(\"Original Image\")\n", "plt.colorbar()\n", "\n", "plt.subplot(1, 3, 2)\n", "plt.imshow(simple_recon_magnitude, cmap='gray')\n", "plt.title(\"Simple FFT Reconstruction\")\n", "plt.colorbar()\n", "\n", "plt.subplot(1, 3, 3)\n", "plt.imshow(reconstructed_magnitude, cmap='gray')\n", "plt.title(\"Improved Gridding Reconstruction\")\n", "plt.colorbar()\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Detailed Analysis of Reconstruction Quality"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate error metrics\n", "def calculate_metrics(original, reconstructed):\n", "    # Normalize images for fair comparison\n", "    original_norm = original / np.max(original)\n", "    reconstructed_norm = reconstructed / np.max(reconstructed)\n", "    \n", "    # Mean Squared Error\n", "    mse = np.mean((original_norm - reconstructed_norm) ** 2)\n", "    \n", "    # Peak Signal-to-Noise Ratio\n", "    if mse == 0:\n", "        psnr = 100\n", "    else:\n", "        psnr = 20 * np.log10(1.0 / np.sqrt(mse))\n", "    \n", "    # Structural Similarity Index (simplified version)\n", "    # For a proper SSIM, you would need to use a dedicated library\n", "    mu1 = np.mean(original_norm)\n", "    mu2 = np.mean(reconstructed_norm)\n", "    sigma1 = np.std(original_norm)\n", "    sigma2 = np.std(reconstructed_norm)\n", "    sigma12 = np.mean((original_norm - mu1) * (reconstructed_norm - mu2))\n", "    \n", "    c1 = (0.01) ** 2\n", "    c2 = (0.03) ** 2\n", "    \n", "    ssim = ((2 * mu1 * mu2 + c1) * (2 * sigma12 + c2)) / \\\n", "           ((mu1 ** 2 + mu2 ** 2 + c1) * (sigma1 ** 2 + sigma2 ** 2 + c2))\n", "    \n", "    return {\n", "        'MSE': mse,\n", "        'PSNR': psnr,\n", "        'SSIM': ssim\n", "    }\n", "\n", "# Calculate metrics for both reconstruction methods\n", "simple_metrics = calculate_metrics(image_data, simple_recon_magnitude)\n", "improved_metrics = calculate_metrics(image_data, reconstructed_magnitude)\n", "\n", "print(\"Simple FFT Reconstruction Metrics:\")\n", "print(f\"  MSE: {simple_metrics['MSE']:.6f}\")\n", "print(f\"  PSNR: {simple_metrics['PSNR']:.2f} dB\")\n", "print(f\"  SSIM: {simple_metrics['SSIM']:.4f}\")\n", "print()\n", "print(\"Improved Gridding Reconstruction Metrics:\")\n", "print(f\"  MSE: {improved_metrics['MSE']:.6f}\")\n", "print(f\"  PSNR: {improved_metrics['PSNR']:.2f} dB\")\n", "print(f\"  SSIM: {improved_metrics['SSIM']:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualize Difference Maps"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Normalize images for visualization\n", "original_norm = image_data / np.max(image_data)\n", "simple_norm = simple_recon_magnitude / np.max(simple_recon_magnitude)\n", "improved_norm = reconstructed_magnitude / np.max(reconstructed_magnitude)\n", "\n", "# Calculate difference maps\n", "simple_diff = np.abs(original_norm - simple_norm)\n", "improved_diff = np.abs(original_norm - improved_norm)\n", "\n", "# Display difference maps\n", "plt.figure(figsize=(15, 5))\n", "plt.subplot(1, 3, 1)\n", "plt.imshow(original_norm, cmap='gray')\n", "plt.title(\"Original Image\")\n", "plt.colorbar()\n", "\n", "plt.subplot(1, 3, 2)\n", "plt.imshow(simple_diff, cmap='hot')\n", "plt.title(\"Simple FFT Error Map\")\n", "plt.colorbar()\n", "\n", "plt.subplot(1, 3, 3)\n", "plt.imshow(improved_diff, cmap='hot')\n", "plt.title(\"Improved Gridding Error Map\")\n", "plt.colorbar()\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "The improved gridding reconstruction method provides better image quality compared to the simple FFT reconstruction. The key improvements are:\n", "\n", "1. **Better Interpolation**: Using linear interpolation instead of nearest neighbor for Cartesian to spiral interpolation.\n", "2. **Proper Density Compensation**: Using a more accurate density compensation function based on k-space radius.\n", "3. **Kaiser-Bessel Gridding**: Using a Kaiser-Bessel kernel for gridding, which provides better frequency response.\n", "4. **Deapodization**: Correcting for the effects of the gridding kernel in image domain.\n", "\n", "These improvements result in reduced artifacts and better overall image quality."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}