import vtk
import numpy as np
from vtk.util.numpy_support import numpy_to_vtk
import os

# Fix numpy compatibility
if not hasattr(np, 'bool'):
    np.bool = bool

print("Testing VTI file creation with VTK...")

# Create a simple test image
test_data = np.zeros((10, 10, 1), dtype=np.float32)
test_data[3:7, 3:7, 0] = 1.0  # Simple square pattern

print(f"Test data shape: {test_data.shape}")
print(f"Test data range: {test_data.min()} to {test_data.max()}")

# Create VTK image data
imageData = vtk.vtkImageData()
imageData.SetDimensions(test_data.shape)
imageData.SetSpacing(0.002, 0.002, 0.002)
imageData.SetOrigin(0, 0, 0)

# Convert numpy array to VTK array
vtk_array = numpy_to_vtk(test_data.ravel(order='F'), deep=True, array_type=vtk.VTK_FLOAT)
vtk_array.SetName("test_labels")

# Add array to point data
imageData.GetPointData().SetScalars(vtk_array)

print(f"VTK image dimensions: {imageData.GetDimensions()}")
print(f"VTK point data arrays: {imageData.GetPointData().GetNumberOfArrays()}")

# Try to write VTI file
test_output = "test_output.vti"
try:
    writer = vtk.vtkXMLImageDataWriter()
    writer.SetFileName(test_output)
    writer.SetInputData(imageData)
    writer.Write()
    
    print(f"✅ Successfully wrote test VTI file: {test_output}")
    print(f"   File size: {os.path.getsize(test_output)} bytes")
    
    # Try to read it back
    reader = vtk.vtkXMLImageDataReader()
    reader.SetFileName(test_output)
    reader.Update()
    
    read_data = reader.GetOutput()
    print(f"✅ Successfully read back VTI file!")
    print(f"   Read dimensions: {read_data.GetDimensions()}")
    print(f"   Read arrays: {read_data.GetPointData().GetNumberOfArrays()}")
    
    if read_data.GetPointData().GetNumberOfArrays() > 0:
        array = read_data.GetPointData().GetArray(0)
        print(f"   Array name: '{array.GetName()}'")
        print(f"   Array size: {array.GetNumberOfTuples()}")
        
    # Cleanup
    os.remove(test_output)
    print("✅ Test VTI creation and reading successful!")
    
except Exception as e:
    print(f"❌ Error in VTI test: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "="*50)
print("Now testing the MMF functions...")

# Test MMF functions
try:
    import sys
    sys.path.append('mrxcat-2.0-main/myWarpFunctions')
    import myMorphingFunctions as MMF
    
    # Create VTK image using MMF function
    test_spacing = [0.002, 0.002, 0.002]
    test_origin = [0, 0, 0]
    
    print("Testing MMF.vtkSliceImage...")
    vtk_img = MMF.vtkSliceImage(test_data, test_spacing, test_origin)
    print(f"✅ MMF.vtkSliceImage successful")
    print(f"   Dimensions: {vtk_img.GetDimensions()}")
    
    print("Testing MMF.addArrayToVtk...")
    vtk_img = MMF.addArrayToVtk(vtk_img, test_data, 'test_labels', False)
    print(f"✅ MMF.addArrayToVtk successful")
    print(f"   Arrays: {vtk_img.GetPointData().GetNumberOfArrays()}")
    
    print("Testing MMF.saveVTI...")
    test_mmf_output = "test_mmf_output.vti"
    MMF.saveVTI(vtk_img, test_mmf_output)
    print(f"✅ MMF.saveVTI successful")
    print(f"   File size: {os.path.getsize(test_mmf_output)} bytes")
    
    # Try to read MMF output
    reader2 = vtk.vtkXMLImageDataReader()
    reader2.SetFileName(test_mmf_output)
    reader2.Update()
    
    read_data2 = reader2.GetOutput()
    print(f"✅ Successfully read MMF VTI file!")
    print(f"   Dimensions: {read_data2.GetDimensions()}")
    print(f"   Arrays: {read_data2.GetPointData().GetNumberOfArrays()}")
    
    # Cleanup
    os.remove(test_mmf_output)
    print("✅ MMF VTI functions working correctly!")
    
except Exception as e:
    print(f"❌ Error testing MMF functions: {e}")
    import traceback
    traceback.print_exc() 