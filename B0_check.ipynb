{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded field_Hz: (500, 500, 950)\n", "Loaded b0_field: (500, 500, 950)\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "89064cdecadb4b8ead46cb636f6cd1de", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=475, continuous_update=False, description='Slice:', max=949, style=Slide…"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["B0 field statistics:\n", "Min: -1248.82 Hz\n", "Max: 1046.49 Hz\n", "Mean: 248.91 Hz\n", "Std: 86.05 Hz\n"]}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import ipywidgets as widgets\n", "from ipywidgets import interact\n", "import os\n", "\n", "# Load the saved results\n", "output_dir = \"/Users/<USER>/Documents/important document backup/Local python code/MRXCAT2/output/male_169\"\n", "field_Hz_file = \"male_169_field_Hz_20250610_211706.npy\"  # 使用您的实际文件名\n", "b0_field_file = \"male_169_b0_field_20250610_211706.npy\"  # 使用您的实际文件名\n", "\n", "field_Hz = np.load(os.path.join(output_dir, field_Hz_file))\n", "b0_field = np.load(os.path.join(output_dir, b0_field_file))\n", "\n", "print(f\"Loaded field_Hz: {field_Hz.shape}\")\n", "print(f\"Loaded b0_field: {b0_field.shape}\")\n", "\n", "# 如果您在Ju<PERSON>ter中使用，可以取消这部分的注释\n", "def update_field_viewer(slice_idx):\n", "    \"\"\"Visualize B0 field\"\"\"\n", "    fig, ax = plt.subplots(1, 1, figsize=(10, 8))\n", "    \n", "    im = ax.imshow(b0_field[:,:,slice_idx], cmap='jet')\n", "    plt.colorbar(im, ax=ax, label='Frequency Shift (Hz)')\n", "    ax.set_title(f'B0 Field Map\\nSlice {slice_idx}')\n", "    ax.axis('off')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Create interactive slider with full range\n", "interact(update_field_viewer, \n", "        slice_idx=widgets.IntSlider(\n", "            min=0, \n", "            max=b0_field.shape[2]-1,\n", "            step=1, \n", "            value=b0_field.shape[2]//2,\n", "            description='Slice:',\n", "            continuous_update=False,\n", "            style={'description_width': 'initial'}\n", "        ))\n", "\n", "# # 非交互式版本，显示中间切片或几个切片\n", "# def show_slices(data, title, slices=None, cmap='jet', figsize=(15, 5)):\n", "#     if slices is None:\n", "#         # 如果没有指定切片，则显示中间切片\n", "#         middle_slice = data.shape[2] // 2\n", "#         slices = [middle_slice]\n", "    \n", "#     fig, axes = plt.subplots(1, len(slices), figsize=figsize)\n", "#     if len(slices) == 1:\n", "#         axes = [axes]\n", "    \n", "#     for i, slice_idx in enumerate(slices):\n", "#         im = axes[i].imshow(data[:,:,slice_idx], cmap=cmap)\n", "#         axes[i].set_title(f'{title}\\nSlice {slice_idx}')\n", "#         axes[i].axis('off')\n", "#         plt.colorbar(im, ax=axes[i])\n", "    \n", "#     plt.tight_layout()\n", "#     plt.savefig(os.path.join(output_dir, f\"{title.replace(' ', '_')}_slices.png\"))\n", "#     plt.show()\n", "\n", "# # 显示几个等间距切片\n", "# num_slices = 3\n", "# slice_indices = np.linspace(0, b0_field.shape[2]-1, num_slices, dtype=int)\n", "# show_slices(b0_field, 'B0 Field Map', slices=slice_indices)\n", "\n", "# # 显示极值切片\n", "# max_slice = np.unravel_index(np.argmax(np.abs(b0_field)), b0_field.shape)[2]\n", "# show_slices(b0_field, 'B0 Field - Maximum Distortion Slice', slices=[max_slice], figsize=(10, 8))\n", "\n", "# 打印B0场的统计信息\n", "print(f\"B0 field statistics:\")\n", "print(f\"Min: {b0_field.min():.2f} Hz\")\n", "print(f\"Max: {b0_field.max():.2f} Hz\")\n", "print(f\"Mean: {b0_field.mean():.2f} Hz\")\n", "print(f\"Std: {b0_field.std():.2f} Hz\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Axial View:\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "baf9a98f7e384b5ebcab9b79bbadf15f", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=475, continuous_update=False, description='Slice:', max=949), Output()),…"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Coronal View:\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e518292d2e324bd3adb80bb25abd19aa", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=250, continuous_update=False, description='Slice:', max=499), Output()),…"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Sagittal View:\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "812a34b958a44095b3fddbbfeaaeac6c", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=250, continuous_update=False, description='Slice:', max=499), Output()),…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<function __main__.update_field_viewer_sagittal(slice_idx)>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# Convert axial B0 field to other views\n", "b0_field_Sagittal = b0_field.transpose(2, 0, 1)   # Transpose to match coronal orientation\n", "b0_field_Coronal = b0_field.transpose(2, 1, 0)  # Transpose to match sagittal orientation\n", "\n", "# Viewer functions for each orientation\n", "def update_field_viewer_axial(slice_idx):\n", "    fig, ax = plt.subplots(1, 1, figsize=(10, 8))\n", "    \n", "    im = ax.imshow(b0_field[:,:,slice_idx], cmap='jet', vmin=-200, vmax=200)\n", "    plt.colorbar(im, ax=ax, label='Frequency Shift (Hz)')\n", "    ax.set_title(f'Axial B0 Field Map\\nSlice {slice_idx}')\n", "    ax.axis('off')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "def update_field_viewer_coronal(slice_idx):\n", "    fig, ax = plt.subplots(1, 1, figsize=(10, 8))\n", "    \n", "    im = ax.imshow(b0_field_Coronal[:,:,slice_idx], cmap='jet', vmin=-200, vmax=200)\n", "    plt.colorbar(im, ax=ax, label='Frequency Shift (Hz)')\n", "    ax.set_title(f'Coronal B0 Field Map\\nSlice {slice_idx}')\n", "    ax.axis('off')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "def update_field_viewer_sagittal(slice_idx):\n", "    fig, ax = plt.subplots(1, 1, figsize=(10, 8))\n", "    \n", "    im = ax.imshow(b0_field_Sagittal[:,:,slice_idx], cmap='jet', vmin=-200, vmax=200)\n", "    plt.colorbar(im, ax=ax, label='Frequency Shift (Hz)')\n", "    ax.set_title(f'Sagittal B0 Field Map\\nSlice {slice_idx}')\n", "    ax.axis('off')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "print(\"Axial View:\")\n", "interact(update_field_viewer_axial, \n", "        slice_idx=widgets.IntSlider(\n", "            min=0, \n", "            max=b0_field.shape[2]-1, \n", "            step=1, \n", "            value=b0_field.shape[2]//2,\n", "            description='Slice:',\n", "            continuous_update=False\n", "        ))\n", "\n", "# Create interactive sliders for each view\n", "print(\"Coronal View:\")\n", "interact(update_field_viewer_coronal, \n", "        slice_idx=widgets.IntSlider(\n", "            min=0, \n", "            max=b0_field_Coronal.shape[2]-1, \n", "            step=1, \n", "            value=b0_field_Coronal.shape[2]//2,\n", "            description='Slice:',\n", "            continuous_update=False\n", "        ))\n", "\n", "print(\"Sagittal View:\")\n", "interact(update_field_viewer_sagittal, \n", "        slice_idx=widgets.IntSlider(\n", "            min=0, \n", "            max=b0_field_Sagittal.shape[2]-1, \n", "            step=1, \n", "            value=b0_field_Sagittal.shape[2]//2,\n", "            description='Slice:',\n", "            continuous_update=False\n", "        ))"]}], "metadata": {"kernelspec": {"display_name": "mrxcat20", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 2}