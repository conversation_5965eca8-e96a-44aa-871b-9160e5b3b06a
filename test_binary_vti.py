import vtk
import numpy as np

if not hasattr(np, 'bool'):
    np.bool = bool

reader = vtk.vtkXMLImageDataReader()
reader.SetFileName('mrxcat-2.0-main/male169/male_169.samp_act_fixed.vti')
reader.Update()
img = reader.GetOutput()

print('Fixed binary VTI file verification:')
print(f'Dimensions: {img.GetDimensions()}')
print(f'Spacing: {img.GetSpacing()}')
print(f'Origin: {img.GetOrigin()}')

# Check if labels array exists
point_data = img.GetPointData()
labels_array = point_data.GetArray('labels')
if labels_array:
    print(f'Labels array found: {labels_array.GetNumberOfTuples()} elements')
    print('✅ Binary conversion VTI file is valid!')
else:
    print('❌ No labels array found') 