{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Improved Spiral Trajectory Reconstruction\n", "\n", "This notebook implements an improved spiral trajectory reconstruction method using proper gridding techniques."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from scipy.interpolate import RegularGridInterpolator\n", "from scipy.special import i0  # Modified Bessel function of the first kind, order 0\n", "from scipy.ndimage import gaussian_filter"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Spiral Trajectory Generation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def generate_spiral_trajectory(matrix_size, interleaves=1, field_of_view=1.0):\n", "    \"\"\"\n", "    Generate spiral trajectory for k-space sampling.\n", "    \n", "    Parameters:\n", "    -----------\n", "    matrix_size : int\n", "        Size of the image matrix (assumed square)\n", "    interleaves : int\n", "        Number of spiral interleaves\n", "    field_of_view : float\n", "        Field of view in meters\n", "        \n", "    Returns:\n", "    --------\n", "    kx, ky : n<PERSON>ray\n", "        k-space coordinates of the spiral trajectory\n", "    \"\"\"\n", "    N = matrix_size\n", "    num_points = N * N\n", "    k = np.zeros((num_points, 2))\n", "    \n", "    # Spiral parameters\n", "    max_k = N / 2\n", "    turns = 10\n", "    \n", "    # Generate single spiral\n", "    t = np.linspace(0, 2 * np.pi * turns, num_points)\n", "    for i in range(interleaves):\n", "        phi = 2 * np.pi * i / interleaves\n", "        k[:, 0] = max_k * t / (2 * np.pi * turns) * np.cos(t + phi)\n", "        k[:, 1] = max_k * t / (2 * np.pi * turns) * np.sin(t + phi)\n", "    \n", "    return k[:, 0], k[:, 1]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Coordinate Conversion Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def k_to_pixel(kx, ky, matrix_size):\n", "    \"\"\"Convert k-space coordinates to pixel coordinates\"\"\"\n", "    kx_pixel = kx + matrix_size / 2\n", "    ky_pixel = ky + matrix_size / 2\n", "    return kx_pixel, ky_pixel"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Density Compensation Function"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def calculate_density_compensation(kx, ky):\n", "    \"\"\"\n", "    Calculate density compensation function for spiral trajectory.\n", "    \n", "    For spiral trajectories, DCF is approximately proportional to radius.\n", "    \"\"\"\n", "    # Calculate distance from k-space center\n", "    k_radius = np.sqrt(kx**2 + ky**2)\n", "    \n", "    # For spiral trajectories, DCF is approximately proportional to radius\n", "    dcf = k_radius\n", "    \n", "    # Normalize DCF\n", "    dcf = dcf / np.max(dcf)\n", "    \n", "    # Avoid division by zero at k-space center\n", "    dcf = np.maximum(dcf, 0.01)\n", "    \n", "    return dcf"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON><PERSON><PERSON> for Gridding"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def kaiser_bessel_kernel(distance, kernel_width=2.5, beta=2.34):\n", "    \"\"\"\n", "    Kaiser-<PERSON><PERSON> kernel function for gridding.\n", "    \n", "    Parameters:\n", "    -----------\n", "    distance : float or ndarray\n", "        Distance from grid point\n", "    kernel_width : float\n", "        Width of the kernel\n", "    beta : float\n", "        Shape parameter of the Kaiser-<PERSON> function\n", "        \n", "    Returns:\n", "    --------\n", "    kernel_value : float or ndarray\n", "        Value of the kernel at the given distance\n", "    \"\"\"\n", "    if np.isscalar(distance):\n", "        if distance > kernel_width:\n", "            return 0.0\n", "        x = distance / kernel_width * np.pi\n", "        return i0(beta * np.sqrt(1 - (x/np.pi)**2)) / i0(beta)\n", "    else:\n", "        result = np.zeros_like(distance)\n", "        mask = distance <= kernel_width\n", "        x = distance[mask] / kernel_width * np.pi\n", "        result[mask] = i0(beta * np.sqrt(1 - (x/np.pi)**2)) / i0(beta)\n", "        return result"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Deapodization Function"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def calculate_deapodization(matrix_size, kernel_width=2.5, beta=2.34):\n", "    \"\"\"\n", "    Calculate deapodization function to correct for kernel effects.\n", "    \n", "    Parameters:\n", "    -----------\n", "    matrix_size : int\n", "        Size of the image matrix (assumed square)\n", "    kernel_width : float\n", "        Width of the kernel\n", "    beta : float\n", "        Shape parameter of the Kaiser-<PERSON> function\n", "        \n", "    Returns:\n", "    --------\n", "    deapod : n<PERSON>ray\n", "        Deapodization function\n", "    \"\"\"\n", "    x = np.linspace(-1, 1, matrix_size)\n", "    y = np.linspace(-1, 1, matrix_size)\n", "    X, Y = np.meshgrid(x, y)\n", "    R = np.sqrt(X**2 + Y**2)\n", "    \n", "    # Calculate deapodization function\n", "    deapod = np.zeros((matrix_size, matrix_size))\n", "    for i in range(matrix_size):\n", "        for j in range(matrix_size):\n", "            r = R[i, j]\n", "            if r <= 1.0:\n", "                # Fourier transform of Kaiser-Bessel is another Kaiser-Bessel\n", "                x = r * kernel_width\n", "                if x < 1e-6:\n", "                    deapod[i, j] = 1.0\n", "                else:\n", "                    deapod[i, j] = np.abs(np.sin(np.pi * x) / (np.pi * x))\n", "            else:\n", "                deapod[i, j] = 1.0\n", "    \n", "    # Avoid division by zero\n", "    deapod = np.maximum(deapod, 0.01)\n", "    \n", "    return deapod"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Cartesian to Spiral Interpolation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def cartesian_to_spiral(cartesian_kspace, kx, ky, matrix_size):\n", "    \"\"\"\n", "    Interpolate Cartesian k-space to spiral trajectory with improved method.\n", "    \n", "    Parameters:\n", "    -----------\n", "    cartesian_kspace : ndarray\n", "        Cartesian k-space data\n", "    kx, ky : n<PERSON>ray\n", "        k-space coordinates of the spiral trajectory\n", "    matrix_size : int\n", "        Size of the image matrix (assumed square)\n", "        \n", "    Returns:\n", "    --------\n", "    spiral_kspace : ndarray\n", "        k-space data along the spiral trajectory\n", "    \"\"\"\n", "    # Convert k-space coordinates to pixel coordinates\n", "    kx_pixel, ky_pixel = k_to_pixel(kx, ky, matrix_size)\n", "    \n", "    # Create interpolator using linear interpolation\n", "    x = np.arange(matrix_size)\n", "    y = np.arange(matrix_size)\n", "    interpolator = RegularGridInterpolator((y, x), cartesian_kspace, \n", "                                         method='linear', bounds_error=False, fill_value=0)\n", "    \n", "    # Interpolate at spiral trajectory points\n", "    spiral_points = np.column_stack((ky_pixel, kx_pixel))\n", "    spiral_kspace = interpolator(spiral_points)\n", "    \n", "    return spiral_kspace"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Spiral to Cartesian Gridding"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def spiral_to_cartesian(spiral_kspace, kx, ky, matrix_size):\n", "    \"\"\"\n", "    Improved gridding method to interpolate spiral k-space to Cartesian grid.\n", "    \n", "    Parameters:\n", "    -----------\n", "    spiral_kspace : ndarray\n", "        k-space data along the spiral trajectory\n", "    kx, ky : n<PERSON>ray\n", "        k-space coordinates of the spiral trajectory\n", "    matrix_size : int\n", "        Size of the image matrix (assumed square)\n", "        \n", "    Returns:\n", "    --------\n", "    gridded_kspace : ndarray\n", "        Gridded k-space data on Cartesian grid\n", "    \"\"\"\n", "    # Convert k-space coordinates to pixel coordinates\n", "    kx_pixel, ky_pixel = k_to_pixel(kx, ky, matrix_size)\n", "    \n", "    # Calculate density compensation function\n", "    dcf = calculate_density_compensation(kx, ky)\n", "    \n", "    # Apply density compensation\n", "    spiral_kspace_compensated = spiral_kspace * dcf\n", "    \n", "    # Create empty Cartesian k-space\n", "    gridded_kspace = np.zeros((matrix_size, matrix_size), dtype=complex)\n", "    \n", "    # Define gridding parameters\n", "    kernel_width = 2.5  # Width of Kaiser-Bessel kernel\n", "    kernel_radius = int(np.ceil(kernel_width))\n", "    \n", "    # Perform gridding with Kaiser-Bessel kernel\n", "    for i in range(len(spiral_kspace)):\n", "        # Find grid points within kernel radius\n", "        x_center = kx_pixel[i]\n", "        y_center = ky_pixel[i]\n", "        x_min = max(0, int(x_center - kernel_radius))\n", "        x_max = min(matrix_size-1, int(x_center + kernel_radius))\n", "        y_min = max(0, int(y_center - kernel_radius))\n", "        y_max = min(matrix_size-1, int(y_center + kernel_radius))\n", "        \n", "        # Apply kernel to nearby grid points\n", "        for x in range(x_min, x_max + 1):\n", "            for y in range(y_min, y_max + 1):\n", "                distance = np.sqrt((x - x_center)**2 + (y - y_center)**2)\n", "                weight = kaiser_bessel_kernel(distance, kernel_width)\n", "                gridded_kspace[y, x] += spiral_kspace_compensated[i] * weight\n", "    \n", "    return gridded_kspace"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Complete Reconstruction Function"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def reconstruct_from_spiral(spiral_kspace, kx, ky, matrix_size):\n", "    \"\"\"\n", "    Complete reconstruction from spiral k-space.\n", "    \n", "    Parameters:\n", "    -----------\n", "    spiral_kspace : ndarray\n", "        k-space data along the spiral trajectory\n", "    kx, ky : n<PERSON>ray\n", "        k-space coordinates of the spiral trajectory\n", "    matrix_size : int\n", "        Size of the image matrix (assumed square)\n", "        \n", "    Returns:\n", "    --------\n", "    reconstructed_image : n<PERSON>ray\n", "        Reconstructed image\n", "    \"\"\"\n", "    # Grid the spiral data to Cartesian coordinates\n", "    gridded_kspace = spiral_to_cartesian(spiral_kspace, kx, ky, matrix_size)\n", "    \n", "    # Perform inverse FFT\n", "    image_domain = np.fft.ifft2(np.fft.ifftshift(gridded_kspace))\n", "    \n", "    # Calculate deapodization function\n", "    deapod = calculate_deapodization(matrix_size)\n", "    \n", "    # Apply deapodization to correct for kernel effects\n", "    reconstructed_image = image_domain / deapod\n", "    \n", "    return reconstructed_image"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Example Usage\n", "\n", "Here's how to use these functions to reconstruct an image from spiral k-space data:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example usage (assuming you have an image and want to simulate spiral acquisition)\n", "def example_usage(image_data):\n", "    # Get image dimensions\n", "    matrix_size = image_data.shape[0]\n", "    \n", "    # Convert image to k-space\n", "    cartesian_kspace = np.fft.fftshift(np.fft.fft2(image_data))\n", "    \n", "    # Generate spiral trajectory\n", "    kx, ky = generate_spiral_trajectory(matrix_size, interleaves=1)\n", "    \n", "    # Interpolate Cartesian k-space to spiral trajectory\n", "    spiral_kspace = cartesian_to_spiral(cartesian_kspace, kx, ky, matrix_size)\n", "    \n", "    # Reconstruct image from spiral k-space\n", "    reconstructed_image = reconstruct_from_spiral(spiral_kspace, kx, ky, matrix_size)\n", "    \n", "    # Display results\n", "    plt.figure(figsize=(15, 5))\n", "    \n", "    plt.subplot(1, 3, 1)\n", "    plt.imshow(np.abs(image_data), cmap='gray')\n", "    plt.title('Original Image')\n", "    plt.colorbar()\n", "    \n", "    # Visualize spiral trajectory\n", "    plt.subplot(1, 3, 2)\n", "    plt.plot(kx, ky, 'b.', markersize=1)\n", "    plt.title('Spiral Trajectory')\n", "    plt.axis('equal')\n", "    plt.grid(True)\n", "    \n", "    plt.subplot(1, 3, 3)\n", "    plt.imshow(np.abs(reconstructed_image), cmap='gray')\n", "    plt.title('Reconstructed Image')\n", "    plt.colorbar()\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    return reconstructed_image"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}