import vtk
import numpy as np
import os

if not hasattr(np, 'bool'):
    np.bool = bool

slice_path = 'mrxcat-2.0-main/outputData/test_male169/slice_624'
files = ['Model.vti', 'PD.vti', 'T1.vti', 'T2.vti', 'T2s.vti']

print('Final verification of slice 624 VTI files:')
print('='*50)

all_good = True
for vti_file in files:
    full_path = os.path.join(slice_path, vti_file)
    if os.path.exists(full_path):
        try:
            reader = vtk.vtkXMLImageDataReader()
            reader.SetFileName(full_path)
            reader.Update()
            img = reader.GetOutput()
            
            labels_array = img.GetPointData().GetArray('labels')
            if labels_array:
                print(f'✅ {vti_file}: Valid (labels found)')
            else:
                print(f'❌ {vti_file}: No labels array')
                all_good = False
        except Exception as e:
            print(f'❌ {vti_file}: Error - {e}')
            all_good = False
    else:
        print(f'❌ {vti_file}: File not found')
        all_good = False

if all_good:
    print('\n🎉 All VTI files are valid and ready for use!')
else:
    print('\n❌ Some VTI files have issues') 