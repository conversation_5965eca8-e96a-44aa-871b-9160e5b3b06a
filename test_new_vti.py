import vtk
from vtk.util.numpy_support import vtk_to_numpy
import numpy as np
import os

# Fix numpy compatibility
if not hasattr(np, 'bool'):
    np.bool = bool

def test_new_vti(filepath):
    print(f"\n🔍 Testing newly generated VTI: {os.path.basename(filepath)}")
    print(f"   File size: {os.path.getsize(filepath)} bytes")
    
    try:
        reader = vtk.vtkXMLImageDataReader()
        reader.SetFileName(filepath)
        reader.Update()
        
        imageData = reader.GetOutput()
        
        if imageData is None:
            print("   ❌ Failed to read imageData")
            return False
            
        dims = imageData.GetDimensions()
        print(f"   ✅ Successfully read VTI file!")
        print(f"   Dimensions: {dims}")
        print(f"   Spacing: {imageData.GetSpacing()}")
        print(f"   Origin: {imageData.GetOrigin()}")
        
        # Check if dimensions are valid
        if dims[0] == 0 or dims[1] == 0:
            print("   ❌ Invalid dimensions (0,0,0) - file still corrupted")
            return False
        
        # Check point data
        pointData = imageData.GetPointData()
        print(f"   Point Data Arrays: {pointData.GetNumberOfArrays()}")
        
        arrays_found = []
        for i in range(pointData.GetNumberOfArrays()):
            array = pointData.GetArray(i)
            if array:
                array_name = array.GetName() if array.GetName() else f"Array_{i}"
                print(f"     [{i}] '{array_name}': {array.GetNumberOfTuples()} elements")
                arrays_found.append(array_name)
                
                # Try to convert to numpy and show sample values
                try:
                    np_array = vtk_to_numpy(array)
                    print(f"         Range: {np_array.min():.3f} to {np_array.max():.3f}")
                    unique_vals = np.unique(np_array)
                    print(f"         Unique values (first 10): {unique_vals[:10]}")
                    
                    # Special check for labels array
                    if 'label' in array_name.lower():
                        print(f"         ✅ Found labels array! {len(unique_vals)} unique labels")
                        
                except Exception as e:
                    print(f"         ❌ Failed to convert to numpy: {e}")
        
        # Summary
        if 'labels' in arrays_found or any('label' in name.lower() for name in arrays_found):
            print("   ✅ SUCCESS: VTI file contains proper label data!")
            return True
        else:
            print("   ⚠️  WARNING: No 'labels' array found")
            return False
        
    except Exception as e:
        print(f"   ❌ Error reading file: {e}")
        return False

# Test the newly generated files
print("Testing newly generated VTI files:")
print("=" * 60)

# Test slice 624 Model.vti
test_new_vti("mrxcat-2.0-main/outputData/test_male169/slice_624/Model.vti")

# Also check if tissue property files exist for this slice
tissue_folder = "mrxcat-2.0-main/outputData/test_male169/Tissue_properties_initial_frame/slice_624"
if os.path.exists(tissue_folder):
    print(f"\n📁 Checking tissue properties for slice 624:")
    for prop_file in ['PD.vti', 'T1.vti', 'T2.vti', 'T2s.vti']:
        prop_path = os.path.join(tissue_folder, prop_file)
        if os.path.exists(prop_path):
            test_new_vti(prop_path)
        else:
            print(f"   ❌ {prop_file} not found")
else:
    print(f"\n⚠️  Tissue properties folder not found: {tissue_folder}") 