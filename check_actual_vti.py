import vtk
from vtk.util.numpy_support import vtk_to_numpy
import numpy as np
import os

# Fix numpy compatibility
if not hasattr(np, 'bool'):
    np.bool = bool

# Check VTI files in the correct locations
print("Checking VTI files in correct locations:")
print("=" * 60)

# Check Model.vti in test_male169 slice directories
model_path = 'mrxcat-2.0-main/outputData/test_male169/slice_574/Model.vti'
print(f"\n📁 Checking: {model_path}")

if os.path.exists(model_path):
    print(f"   ✅ File exists, Size: {os.path.getsize(model_path)} bytes")
    
    try:
        reader = vtk.vtkXMLImageDataReader()
        reader.SetFileName(model_path)
        reader.Update()
        
        imageData = reader.GetOutput()
        
        # Check point data arrays
        pointData = imageData.GetPointData()
        print(f"   Point Data Arrays: {pointData.GetNumberOfArrays()}")
        
        for i in range(pointData.GetNumberOfArrays()):
            array = pointData.GetArray(i)
            array_name = array.GetName() if array.GetName() else f"Array_{i}"
            print(f"     [{i}] Name: '{array_name}', Size: {array.GetNumberOfTuples()}")
            
            # If this looks like labels, show some sample values
            if 'label' in array_name.lower() or array_name in ['ImageScalars', 'scalars']:
                np_array = vtk_to_numpy(array)
                unique_vals = np.unique(np_array[:1000])  # Sample first 1000 values
                print(f"         Sample unique values: {unique_vals[:10]}")
        
        # Check cell data arrays  
        cellData = imageData.GetCellData()
        print(f"   Cell Data Arrays: {cellData.GetNumberOfArrays()}")
        
        for i in range(cellData.GetNumberOfArrays()):
            array = cellData.GetArray(i)
            array_name = array.GetName() if array.GetName() else f"Array_{i}"
            print(f"     [{i}] Name: '{array_name}', Size: {array.GetNumberOfTuples()}")
            
    except Exception as e:
        print(f"   ❌ Error reading Model.vti: {e}")
else:
    print("   ❌ Model.vti not found!")

# Check tissue property files
print(f"\n📁 Checking tissue property files:")
tissue_base = 'mrxcat-2.0-main/outputData/test_male169/Tissue_properties_initial_frame/slice_574/'

for prop_file in ['PD.vti', 'T1.vti', 'T2.vti', 'T2s.vti']:
    prop_path = os.path.join(tissue_base, prop_file)
    print(f"\n   📄 {prop_file}:")
    
    if os.path.exists(prop_path):
        print(f"     ✅ Exists, Size: {os.path.getsize(prop_path)} bytes")
        
        try:
            reader = vtk.vtkXMLImageDataReader()
            reader.SetFileName(prop_path)
            reader.Update()
            
            imageData = reader.GetOutput()
            pointData = imageData.GetPointData()
            
            if pointData.GetNumberOfArrays() > 0:
                array = pointData.GetArray(0)
                array_name = array.GetName() if array.GetName() else "Array_0"
                print(f"     Primary array: '{array_name}', Size: {array.GetNumberOfTuples()}")
                
                # Show sample values
                np_array = vtk_to_numpy(array)
                print(f"     Value range: {np_array.min():.3f} to {np_array.max():.3f}")
            else:
                print(f"     ❌ No arrays found!")
                
        except Exception as e:
            print(f"     ❌ Error reading: {e}")
    else:
        print(f"     ❌ File not found!") 