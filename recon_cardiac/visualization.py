#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MRXCAT Cardiac MRI Visualization

This script contains functions for visualizing MRI data and results.
"""

import numpy as np
import matplotlib.pyplot as plt

def visualize_parameter_maps(maps_dict, param_settings, z_slice=None, figsize=(15, 10)):
    """
    Visualize parameter maps
    
    Parameters:
    -----------
    maps_dict : dict
        Dictionary of parameter maps
    param_settings : dict
        Dictionary of visualization settings
    z_slice : int or None
        Slice index to visualize (if None, assumes 2D maps)
    figsize : tuple
        Figure size
    """
    # Count number of maps to display
    n_maps = len(maps_dict)
    n_cols = min(4, n_maps)
    n_rows = (n_maps + n_cols - 1) // n_cols
    
    plt.figure(figsize=figsize)
    
    for i, (key, map_data) in enumerate(maps_dict.items()):
        plt.subplot(n_rows, n_cols, i+1)
        
        # Extract slice if 3D
        if z_slice is not None and map_data.ndim == 3:
            map_slice = map_data[:, :, z_slice]
        else:
            map_slice = map_data
        
        # Get visualization settings
        settings = param_settings.get(key, {})
        cmap = settings.get('cmap', 'viridis')
        title = settings.get('title', key)
        vmin = settings.get('vmin', None)
        vmax = settings.get('vmax', None)
        
        # Display the map
        im = plt.imshow(map_slice, cmap=cmap, vmin=vmin, vmax=vmax)
        plt.colorbar(im)
        plt.title(title)
        plt.axis('off')
    
    plt.tight_layout()
    plt.show()

def visualize_spiral_trajectory(kx, ky, k_max, image_size, figsize=(12, 5)):
    """
    Visualize spiral trajectory
    
    Parameters:
    -----------
    kx, ky : 1D arrays
        k-space coordinates
    k_max : float
        Maximum k-space radius
    image_size : int
        Size of the image
    figsize : tuple
        Figure size
    """
    plt.figure(figsize=figsize)
    
    # Plot 1: k-space trajectory
    plt.subplot(1, 2, 1)
    plt.plot(kx, ky, 'b-', linewidth=0.5)
    plt.scatter(kx[::100], ky[::100], c='r', s=5, alpha=0.5)
    plt.title('Spiral Trajectory')
    plt.xlabel('kx')
    plt.ylabel('ky')
    plt.axis('equal')
    plt.grid(True)
    
    # Plot 2: Pixel coordinates
    kx_pixel = (kx / k_max) * (image_size/2) + image_size/2
    ky_pixel = (ky / k_max) * (image_size/2) + image_size/2
    
    plt.subplot(1, 2, 2)
    plt.plot(kx_pixel, ky_pixel, 'g-', linewidth=0.5)
    plt.scatter(kx_pixel[::100], ky_pixel[::100], c='r', s=5, alpha=0.5)
    plt.title('Pixel Coordinates')
    plt.xlabel('x')
    plt.ylabel('y')
    plt.xlim(0, image_size)
    plt.ylim(0, image_size)
    plt.axis('equal')
    plt.grid(True)
    
    plt.tight_layout()
    plt.show()

def visualize_reconstruction_results(recon_original, recon_t2star, recon_t2star_b0, 
                                    t2star_map, b0_map, cardiac_mask, labels=None, z_slice=None):
    """
    Visualize reconstruction results
    
    Parameters:
    -----------
    recon_original, recon_t2star, recon_t2star_b0 : 2D arrays
        Reconstructed images
    t2star_map, b0_map : 2D arrays
        Parameter maps
    cardiac_mask : 2D boolean array
        Mask of cardiac structures
    labels : 3D array or None
        Tissue label map
    z_slice : int or None
        Slice index
    """
    # Calculate global min and max for consistent window leveling
    global_min = min(np.min(recon_original), np.min(recon_t2star), np.min(recon_t2star_b0))
    global_max = max(np.max(recon_original), np.max(recon_t2star), np.max(recon_t2star_b0))
    print(f"Using global window level: vmin={global_min:.3f}, vmax={global_max:.3f}")
    
    # Calculate difference images
    diff_t2star = recon_original - recon_t2star
    diff_t2star_b0 = recon_original - recon_t2star_b0
    diff_b0 = recon_t2star - recon_t2star_b0
    
    plt.figure(figsize=(18, 12))
    
    # Row 1: Parameter maps
    plt.subplot(3, 3, 1)
    plt.imshow(t2star_map, cmap='hot', vmin=0, vmax=100)
    plt.colorbar()
    plt.title('T2* Map (ms)')
    plt.axis('off')
    
    plt.subplot(3, 3, 2)
    plt.imshow(b0_map, cmap='coolwarm', vmin=-50, vmax=50)
    plt.colorbar()
    plt.title('B0 Field Map (Hz)')
    plt.axis('off')
    
    plt.subplot(3, 3, 3)
    # Create a cardiac anatomy visualization
    if labels is not None and z_slice is not None:
        overlay = np.zeros((*cardiac_mask.shape, 3))
        for label, color in [(1, [1, 0, 0]),      # LV wall - red
                            (2, [0, 0, 1]),       # RV wall - blue
                            (5, [1, 0.7, 0.7]),   # LV blood - light red
                            (6, [0.7, 0.7, 1])]:  # RV blood - light blue
            overlay[labels[:,:,z_slice] == label] = color
        plt.imshow(overlay)
        plt.title('Cardiac Anatomy')
    else:
        plt.imshow(cardiac_mask, cmap='gray')
        plt.title('Cardiac Mask')
    plt.axis('off')
    
    # Row 2: Reconstructed images
    plt.subplot(3, 3, 4)
    plt.imshow(recon_original, cmap='gray', vmin=global_min, vmax=global_max)
    plt.title('Spiral Recon (No Decay)')
    plt.axis('off')
    
    plt.subplot(3, 3, 5)
    plt.imshow(recon_t2star, cmap='gray', vmin=global_min, vmax=global_max)
    plt.title('With T2* Decay Only')
    plt.axis('off')
    
    plt.subplot(3, 3, 6)
    plt.imshow(recon_t2star_b0, cmap='gray', vmin=global_min, vmax=global_max)
    plt.title('With T2* and B0 Effects')
    plt.axis('off')
    
    # Row 3: Difference maps
    plt.subplot(3, 3, 7)
    plt.imshow(diff_t2star, cmap='hot', vmin=0)
    plt.colorbar()
    plt.title('Signal Loss from T2* Only')
    plt.axis('off')
    
    plt.subplot(3, 3, 8)
    plt.imshow(diff_t2star_b0, cmap='hot', vmin=0)
    plt.colorbar()
    plt.title('Signal Loss from T2* + B0')
    plt.axis('off')
    
    plt.subplot(3, 3, 9)
    plt.imshow(diff_b0, cmap='coolwarm', vmin=-0.1, vmax=0.1)
    plt.colorbar()
    plt.title('Additional Loss from B0')
    plt.axis('off')
    
    plt.tight_layout()
    plt.show()

def visualize_decay_profiles(r, r_max, spiral_kspace_original, spiral_kspace_t2star, 
                            spiral_kspace_t2star_b0, t2star_map, sampling_times,
                            recon_original, recon_t2star_b0, cardiac_mask, image_size):
    """
    Visualize decay profiles
    
    Parameters:
    -----------
    r : 1D array
        k-space radius
    r_max : float
        Maximum k-space radius
    spiral_kspace_original, spiral_kspace_t2star, spiral_kspace_t2star_b0 : 1D complex arrays
        Spiral k-space data
    t2star_map : 2D array
        T2* map
    sampling_times : 1D array
        Sampling times
    recon_original, recon_t2star_b0 : 2D arrays
        Reconstructed images
    cardiac_mask : 2D boolean array
        Mask of cardiac structures
    image_size : int
        Size of the image
    """
    plt.figure(figsize=(15, 5))
    
    # Plot k-space signal magnitude vs. radius
    r_norm = r / r_max  # Normalized radius
    r_order = np.argsort(r_norm)  # Sort by distance from center
    
    # Calculate signal ratios
    signal_original = np.abs(spiral_kspace_original)
    signal_t2star = np.abs(spiral_kspace_t2star)
    signal_t2star_b0 = np.abs(spiral_kspace_t2star_b0)
    
    # Normalize to original signal
    ratio_t2star = signal_t2star / (signal_original + 1e-10)
    ratio_t2star_b0 = signal_t2star_b0 / (signal_original + 1e-10)
    
    # Plot decay rates
    plt.subplot(1, 3, 1)
    plt.scatter(r_norm[r_order], ratio_t2star[r_order], 
               c='blue', s=3, alpha=0.3, label='T2* Only')
    plt.scatter(r_norm[r_order], ratio_t2star_b0[r_order], 
               c='red', s=3, alpha=0.3, label='T2* + B0')
    
    # Add theoretical decay curve
    typical_t2star = np.median(t2star_map) / 1000.0  # Convert to seconds
    x_theory = np.linspace(0, 1, 100)
    expected_decay = np.exp(-x_theory * sampling_times[-1] / typical_t2star)
    plt.plot(x_theory, expected_decay, 'k--', linewidth=2, label='Theoretical')
    
    plt.xlabel('Normalized k-space Distance')
    plt.ylabel('Signal Ratio')
    plt.title('T2* Decay vs. k-space Distance')
    plt.grid(True, alpha=0.3)
    plt.legend()
    plt.ylim([0, 1.1])
    
    # Show zoomed cardiac regions
    plt.subplot(1, 3, 2)
    # Find cardiac center for zooming
    if np.any(cardiac_mask):
        y_indices, x_indices = np.where(cardiac_mask)
        center_y, center_x = int(np.mean(y_indices)), int(np.mean(x_indices))
    else:
        center_y, center_x = image_size // 2, image_size // 2
    
    # Define zoom region centered on cardiac area
    zoom_size = 60
    zoom_y_min = max(0, center_y - zoom_size)
    zoom_y_max = min(image_size, center_y + zoom_size)
    zoom_x_min = max(0, center_x - zoom_size)
    zoom_x_max = min(image_size, center_x + zoom_size)
    
    # Show zoomed region
    plt.imshow(recon_original[zoom_y_min:zoom_y_max, zoom_x_min:zoom_x_max], cmap='gray')
    plt.title('Original (No Decay)')
    plt.axis('off')
    
    plt.subplot(1, 3, 3)
    plt.imshow(recon_t2star_b0[zoom_y_min:zoom_y_max, zoom_x_min:zoom_x_max], cmap='gray')
    plt.title('With T2* and B0 Effects')
    plt.axis('off')
    
    plt.tight_layout()
    plt.show()

def visualize_phase_effects(orig_image, b0_map, sampling_times, recon_t2star, recon_t2star_b0):
    """
    Visualize phase effects from B0 field
    
    Parameters:
    -----------
    orig_image : 2D complex array
        Original image
    b0_map : 2D array
        B0 field map in Hz
    sampling_times : 1D array
        Sampling times
    recon_t2star, recon_t2star_b0 : 2D arrays
        Reconstructed images
    """
    plt.figure(figsize=(15, 5))
    
    # Generate images with only phase errors from B0 (no magnitude decay)
    phase_only_image = orig_image * np.exp(-1j * 2 * np.pi * b0_map * sampling_times[-1])
    
    # Calculate phase differences
    phase_orig = np.angle(orig_image)
    phase_b0 = np.angle(phase_only_image)
    phase_diff = phase_b0 - phase_orig
    
    # Show phase effects
    plt.subplot(1, 3, 1)
    plt.imshow(np.abs(orig_image), cmap='gray')
    plt.title('Magnitude Image')
    plt.axis('off')
    
    plt.subplot(1, 3, 2)
    plt.imshow(phase_diff, cmap='hsv', vmin=-np.pi, vmax=np.pi)
    plt.colorbar(label='Phase Shift (rad)')
    plt.title('B0-Induced Phase Errors')
    plt.axis('off')
    
    plt.subplot(1, 3, 3)
    # Show blurring from B0 alone (subtract T2* effects)
    blur_from_b0 = recon_t2star - recon_t2star_b0
    # Only show significant differences
    plt.imshow(blur_from_b0, cmap='coolwarm', vmin=-0.05, vmax=0.05)
    plt.colorbar(label='Signal Difference')
    plt.title('Image Distortion from B0 Only')
    plt.axis('off')
    
    plt.tight_layout()
    plt.show()

def print_simulation_summary(t2star_map, b0_map, recon_original, recon_t2star, recon_t2star_b0, phase_diff):
    """
    Print summary of simulation results
    
    Parameters:
    -----------
    t2star_map, b0_map : 2D arrays
        Parameter maps
    recon_original, recon_t2star, recon_t2star_b0 : 2D arrays
        Reconstructed images
    phase_diff : 2D array
        Phase difference map
    """
    # Calculate difference maps
    diff_t2star = recon_original - recon_t2star
    diff_t2star_b0 = recon_original - recon_t2star_b0
    diff_b0 = recon_t2star - recon_t2star_b0
    
    # Calculate metrics
    rmse_t2star = np.sqrt(np.mean((recon_original - recon_t2star)**2))
    rmse_b0 = np.sqrt(np.mean((recon_t2star - recon_t2star_b0)**2))
    
    print("\nSimulation Results Summary:")
    print(f"T2* map range: {np.min(t2star_map):.1f} - {np.max(t2star_map):.1f} ms")
    print(f"B0 field range: {np.min(b0_map):.1f} - {np.max(b0_map):.1f} Hz")
    print(f"Average signal loss from T2* decay: {100*np.mean(diff_t2star)/np.mean(recon_original):.1f}%")
    print(f"Average signal loss with B0 effects: {100*np.mean(diff_t2star_b0)/np.mean(recon_original):.1f}%")
    print(f"Additional signal loss from B0: {100*np.mean(np.abs(diff_b0))/np.mean(recon_original):.1f}%")
    print(f"Maximum phase error from B0: {np.max(np.abs(phase_diff)):.2f} radians ({np.max(np.abs(phase_diff))*180/np.pi:.1f} degrees)")
    print(f"T2* decay artifacts RMSE: {rmse_t2star:.5f}")
    print(f"B0 field effect artifacts RMSE: {rmse_b0:.5f}")
    
    # Plot the relative artifact contributions
    plt.figure(figsize=(8, 5))
    labels = ['T2* Decay', 'B0 Effects']
    values = [rmse_t2star, rmse_b0]
    plt.bar(labels, values)
    plt.title("Relative Artifact Contributions (RMSE)")
    plt.ylabel("RMSE Difference")
    plt.grid(axis='y')
    plt.tight_layout()
    plt.show()
