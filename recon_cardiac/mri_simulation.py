#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MRXCAT Cardiac MRI Signal Simulation

This script contains functions for simulating MRI signals from tissue property maps.
"""

import numpy as np
from scipy.ndimage import gaussian_filter

def simulate_mri_signal(pd_map, t1_map, t2_map, t2star_map, b0_map, TR, TE, flip_angle, sequence_type='spin_echo', use_shimmed_b0=False):
    """
    Generate synthetic MRI images from tissue property maps
    
    Parameters:
    -----------
    pd_map, t1_map, t2_map, t2star_map : 2D arrays
        Tissue property maps
    b0_map : 2D array
        B0 field map in Hz
    TR : float
        Repetition time in ms
    TE : float
        Echo time in ms
    flip_angle : float
        Flip angle in radians
    sequence_type : str
        'spin_echo', 'gradient_echo', or 'bssfp'
    use_shimmed_b0 : bool
        Whether to use shimmed B0 field
        
    Returns:
    --------
    signal : 2D complex array
        Simulated MR signal
    """
    # Initialize signal with zeros
    signal = np.zeros_like(pd_map, dtype=complex)
    
    if sequence_type.lower() == 'spin_echo':
        # Spin echo: S = PD * (1-exp(-TR/T1)) * exp(-TE/T2)
        t1_factor = 1 - np.exp(-TR / (t1_map + 1e-6))
        t2_factor = np.exp(-TE / (t2_map + 1e-6))
        signal = pd_map * t1_factor * t2_factor
        
    elif sequence_type.lower() == 'gradient_echo':
        # Gradient echo: S = PD * sin(α) * (1-exp(-TR/T1)) / (1-cos(α)*exp(-TR/T1)) * exp(-TE/T2*)
        t1_factor = np.sin(flip_angle) * (1 - np.exp(-TR / (t1_map + 1e-6)))
        denominator = 1 - np.cos(flip_angle) * np.exp(-TR / (t1_map + 1e-6))
        t2star_factor = np.exp(-TE / (t2star_map + 1e-6))
        signal = pd_map * (t1_factor / (denominator + 1e-6)) * t2star_factor
        
        # In the gradient_echo case:
        # Use shimmed B0 field if specified
        if use_shimmed_b0:
            # Using shimmed B0 field for more realistic simulation
            print("Using shimmed B0 field for signal simulation")
            b0_phase = 2 * np.pi * b0_map * TE / 1000.0  # B0 in Hz, TE in ms
        else:
            # Using original B0 field
            b0_phase = 2 * np.pi * b0_map * TE / 1000.0  # B0 in Hz, TE in ms
        signal = signal * np.exp(1j * b0_phase)  # Add B0-induced phase
        
    elif sequence_type.lower() == 'bssfp':
        # bSSFP: S = PD * sin(α) * (1-E1) / (1-(E1-E2)*cos(α)-E1*E2) * exp(i*φ)
        # where E1 = exp(-TR/T1), E2 = exp(-TR/T2)
        E1 = np.exp(-TR / (t1_map + 1e-6))
        E2 = np.exp(-TR / (t2_map + 1e-6))
        
        numerator = np.sin(flip_angle) * (1 - E1)
        denominator = 1 - (E1 - E2) * np.cos(flip_angle) - E1 * E2
        
        magnitude = pd_map * (numerator / (denominator + 1e-6))
        
        # bSSFP phase depends on off-resonance
        if use_shimmed_b0:
            off_resonance_phase = np.pi * b0_map * TR / 1000.0  # B0 in Hz, TR in ms
        else:
            off_resonance_phase = np.pi * b0_map * TR / 1000.0
            
        signal = magnitude * np.exp(1j * off_resonance_phase)
    
    else:
        raise ValueError(f"Unknown sequence type: {sequence_type}")
    
    return signal

def simulate_spiral_t2star_decay(kspace_original, ky_pixel, kx_pixel, orig_image, t2star_map, b0_map, sampling_times, weight_map):
    """
    Simulate T2* decay and B0 effects in spiral MRI acquisition
    
    Parameters:
    -----------
    kspace_original: Complex array of spiral k-space data
    ky_pixel, kx_pixel: Pixel coordinates for spiral trajectory
    orig_image: Original image used for simulation
    t2star_map: T2* values in milliseconds
    b0_map: B0 field inhomogeneity map in Hz
    sampling_times: Acquisition time for each k-space point
    weight_map: Weights for each spiral point
    
    Returns:
    --------
    kspace_with_decay: Complex array with T2* and B0 effects applied
    orig_image: Original image used for simulation
    """
    # Create sparse Cartesian k-space using weight map
    image_size = 90
    sparse_kspace = np.zeros((image_size, image_size), dtype=complex)
    weight_grid = np.zeros((image_size, image_size), dtype=float)
    
    for i, local_weights in enumerate(weight_map):
        for y, x, weight in local_weights:
            sparse_kspace[y, x] += weight * kspace_original[i]
            weight_grid[y, x] += weight

    mask = weight_grid > 0
    sparse_kspace[mask] /= weight_grid[mask]
    
    # Apply minimal smoothing for better visual quality
    real_part = gaussian_filter(sparse_kspace.real, sigma=0.3)
    imag_part = gaussian_filter(sparse_kspace.imag, sigma=0.3)
    smoothed_kspace = real_part + 1j * imag_part
    
    # Convert to image domain
    orig_image = np.fft.ifft2(np.fft.ifftshift(smoothed_kspace))
    
    # Convert T2* map from ms to seconds
    t2star_sec = t2star_map / 1000.0
    
    # Avoid too small T2* values
    min_t2s = 5.0 / 1000.0  # 5 ms minimum
    t2star_sec = np.maximum(t2star_sec, min_t2s)
    
    # Initialize output
    kspace_with_decay = np.zeros_like(kspace_original, dtype=complex)
    
    # Get readout duration
    readout_duration = sampling_times[-1]
    print(f"Readout duration: {readout_duration*1000:.2f} ms")
    
    # Use time binning for efficient processing
    time_bin = 0.001  # 1ms bins
    time_bins = np.arange(0, np.max(sampling_times) + time_bin, time_bin)
    
    print(f'kx_pixel size: {len(kx_pixel)}')
    print(f'ky_pixel size: {len(ky_pixel)}')
    print(f'size of kspace original: {kspace_original.shape}')
    
    # Process each time bin
    for i, t in enumerate(time_bins[:-1]):
        # Find points in this time bin
        time_indices = np.where((sampling_times >= t) & (sampling_times < time_bins[i+1]))[0]
        
        if len(time_indices) == 0:
            continue
        
        # Calculate T2* decay at this time
        t_mid = (t + time_bins[i+1]) / 2  # Use middle of time bin
        decay = np.exp(-t_mid / t2star_sec)
        
        # Calculate phase from B0 field
        b0_phase = np.exp(-1j * 2 * np.pi * b0_map * t_mid)
        
        # Apply both effects to the original image
        decayed_image = orig_image * decay * b0_phase
        
        # Convert back to k-space
        decayed_kspace = np.fft.fftshift(np.fft.fft2(decayed_image))
        
        # Sample at spiral trajectory points for this time bin using weight map
        for idx in time_indices:
            # Use weight_map[idx] for weighted sampling of decayed_kspace
            value = 0.0
            total_weight = 0.0
            for y, x, weight in weight_map[idx]:
                value += weight * decayed_kspace[y, x]
                total_weight += weight
            if total_weight > 0:
                kspace_with_decay[idx] = value / total_weight
            else:
                kspace_with_decay[idx] = 0.0
    
    return kspace_with_decay, orig_image
