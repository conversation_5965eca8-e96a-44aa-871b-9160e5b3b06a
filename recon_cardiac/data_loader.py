#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MRXCAT Cardiac MRI Data Loader

This script loads cardiac MRI parameter maps and prepares them for processing.
"""

import numpy as np
import os
import matplotlib.pyplot as plt

def load_cardiac_data(data_path):
    """
    Load cardiac parameter maps from a .npz file
    
    Parameters:
    -----------
    data_path : str
        Path to the cardiac data file
        
    Returns:
    --------
    data : dict
        Dictionary containing all parameter maps
    """
    print("[DEBUG] Starting to load cardiac parameter maps...")
    try:
        data = np.load(data_path)
        print("[DEBUG] Successfully loaded cardiac data")
    except Exception as e:
        print(f"[ERROR] Failed to load cardiac data: {e}")
        print(f"[DEBUG] Current working directory: {os.getcwd()}")
        print(f"[DEBUG] Checking if file exists: {os.path.exists(data_path)}")
        raise
    
    # Display available parameters
    print(f"[DEBUG] Available parameters: {list(data.keys())}")
    print(f"[DEBUG] Data shapes:")
    for key in data.keys():
        print(f"  - {key}: {data[key].shape}")
    
    return data

def create_cardiac_mask(labels, z_slice, label_values=[1, 2, 5, 6]):
    """
    Create a mask for cardiac structures
    
    Parameters:
    -----------
    labels : 3D array
        Tissue label map
    z_slice : int
        Slice index to use
    label_values : list
        List of label values to include in the mask
        
    Returns:
    --------
    cardiac_mask : 2D boolean array
        Mask of cardiac structures
    """
    cardiac_mask = np.zeros_like(labels[:,:,z_slice], dtype=bool)
    for label in label_values:  # LV wall, RV wall, LV blood, RV blood
        cardiac_mask |= (labels[:,:,z_slice] == label)
    return cardiac_mask

def create_anatomy_overlay(labels, z_slice):
    """
    Create a color-coded anatomy image
    
    Parameters:
    -----------
    labels : 3D array
        Tissue label map
    z_slice : int
        Slice index to use
        
    Returns:
    --------
    anatomy : 3D RGB array
        Color-coded anatomy image
    """
    anatomy = np.zeros((*labels[:,:,z_slice].shape, 3))
    anatomy[labels[:,:,z_slice] == 1] = [1, 0, 0]      # LV wall - red
    anatomy[labels[:,:,z_slice] == 5] = [1, 0.7, 0.7]  # LV blood - light red
    anatomy[labels[:,:,z_slice] == 2] = [0, 0, 1]      # RV wall - blue
    anatomy[labels[:,:,z_slice] == 6] = [0.7, 0.7, 1]  # RV blood - light blue
    return anatomy

def extract_axial_slice(data_input, slice_idx):
    """
    Extract an axial slice from a 3D volume
    
    Parameters:
    -----------
    data_input : 3D array
        Input volume
    slice_idx : int
        Slice index
        
    Returns:
    --------
    slice_data : 2D array
        Extracted slice
    """
    return data_input[slice_idx, :, :].copy()

def crop_maps(maps_dict, center_mask, crop_size=90):
    """
    Crop all maps to a fixed size centered on the region of interest
    
    Parameters:
    -----------
    maps_dict : dict
        Dictionary of parameter maps
    center_mask : 2D boolean array
        Mask defining the region of interest
    crop_size : int
        Size of the cropped region
        
    Returns:
    --------
    cropped_maps : dict
        Dictionary of cropped parameter maps
    crop_bounds : tuple
        (row_min, row_max, col_min, col_max) defining the crop region
    """
    # Get dimensions of one of the maps
    sample_map = next(iter(maps_dict.values()))
    h, w = sample_map.shape
    
    # Find the center of the non-zero region
    y_indices, x_indices = np.where(center_mask)
    center_y = int(np.mean(y_indices))
    center_x = int(np.mean(x_indices))
    
    # Calculate crop boundaries
    half_size = crop_size // 2
    row_min = max(0, center_y - half_size)
    row_max = min(h - 1, center_y + half_size - 1)
    col_min = max(0, center_x - half_size)
    col_max = min(w - 1, center_x + half_size - 1)
    
    # Adjust if crop region is smaller than desired size
    if row_max - row_min + 1 < crop_size:
        if row_min == 0:
            row_max = min(h - 1, crop_size - 1)
        elif row_max == h - 1:
            row_min = max(0, h - crop_size)
    
    if col_max - col_min + 1 < crop_size:
        if col_min == 0:
            col_max = min(w - 1, crop_size - 1)
        elif col_max == w - 1:
            col_min = max(0, w - crop_size)
    
    # Crop all maps
    cropped_maps = {}
    for key, map_data in maps_dict.items():
        cropped_maps[key] = map_data[row_min:row_min+crop_size, col_min:col_min+crop_size]
    
    crop_bounds = (row_min, row_max, col_min, col_max)
    return cropped_maps, crop_bounds

# Parameter visualization settings
param_settings = {
    'PD': {'cmap': 'gray', 'title': 'Proton Density', 'vmin': None, 'vmax': None},
    'T1': {'cmap': 'hot', 'title': 'T1 (ms)', 'vmin': 0, 'vmax': 2000},
    'T2': {'cmap': 'viridis', 'title': 'T2 (ms)', 'vmin': 0, 'vmax': 150},
    'T2star': {'cmap': 'inferno', 'title': 'T2* (ms)', 'vmin': 0, 'vmax': 100},
    'T2star_plus': {'cmap': 'inferno', 'title': 'T2*+ (ms)', 'vmin': 0, 'vmax': 100},
    'Labels': {'cmap': 'tab10', 'title': 'Tissue Labels', 'vmin': None, 'vmax': None},
    'B0': {'cmap': 'jet', 'title': 'B0 Field (Hz)', 'vmin': -100, 'vmax': 100},
    'ShimmedB0': {'cmap': 'jet', 'title': 'Shimmed B0 Field (Hz)', 'vmin': -100, 'vmax': 100}
}
