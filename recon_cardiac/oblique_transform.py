#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MRXCAT Cardiac MRI Oblique Transformations

This script contains functions for transforming volumes to oblique views.
"""

import numpy as np
from scipy import ndimage

def extract_oblique_slice(data_input, best_slice, offset=0, Lu=200, Lv=100, theta=-45):
    """
    Extract a 2D oblique slice from a 3D volume.
    
    Parameters:
    -----------
    data_input : 3D numpy array with shape (Z, Y, X)
    best_slice : int, the axial slice index to serve as center
    offset : float, displacement along new_z axis
    Lu : length (in pixels) along new_x (the oblique direction in the axial plane)
    Lv : length (in pixels) along new_y (the original z direction)
    theta : angle in degrees for the oblique direction in the axial plane
    
    Returns:
    --------
    oblique_slice : 2D numpy array, the extracted oblique slice
    """
    # Determine volume dimensions and center point
    Y, X = data_input.shape[1], data_input.shape[2]
    center = np.array([best_slice, Y/2, X/2])  # center = (z, y, x)

    # Define new coordinate directions
    theta_rad = np.deg2rad(theta)
    new_x = np.array([0, np.cos(theta_rad), np.sin(theta_rad)])  # e.g. (0, 0.7071, 0.7071)
    new_y = np.array([1, 0, 0])  # along the original z-axis
    new_z = np.cross(new_x, new_y)  # perpendicular to both

    # Create grid for new slice coordinates
    Lu_pixels = int(Lu)
    Lv_pixels = int(Lv)
    u = np.linspace(-Lu/2, Lu/2, Lu_pixels)
    v = np.linspace(-Lv/2, Lv/2, Lv_pixels)
    U, V = np.meshgrid(u, v)  # shape (Lv_pixels, Lu_pixels)

    # Compute original coordinates
    Z_coord = center[0] + V * new_y[0] + offset * new_z[0]  # new_y contributes to z; new_z[0] is 0
    Y_coord = center[1] + U * new_x[1] + offset * new_z[1]  # new_x[1] = cos(theta); new_z[1] = cos(theta)
    X_coord = center[2] + U * new_x[2] + offset * new_z[2]  # new_x[2] = sin(theta); new_z[2] = -sin(theta)
    coords = [Z_coord, Y_coord, X_coord]

    # Extract the oblique slice using interpolation
    oblique_slice = ndimage.map_coordinates(data_input, coords, order=1, mode='constant', cval=0)
    return oblique_slice

def transform_modality_to_oblique_view(volume):
    """
    Transform a 3D volume to the final oblique view using two sequential transformations.
    
    Parameters:
    -----------
    volume : 3D numpy array
        Input volume to transform (e.g., data['T2'], data['T1'])
    
    Returns:
    --------
    new_ref_volume : 3D numpy array
        Transformed volume in final oblique view
    """
    # ===== FIRST TRANSFORMATION =====
    # Parameters for first transformation
    best_slice = 60
    Lu = 150
    Lv = 150
    Lz = 101
    D = 100
    fixed_offset = -7
    theta = -45

    # Get volume dimensions
    Z_in, Y_in, X_in = volume.shape

    # Define center in original volume coordinates
    center = np.array([best_slice, Y_in/2, X_in/2])

    # Define new coordinate axes
    theta_rad = np.deg2rad(theta)
    new_x = np.array([0, np.cos(theta_rad), np.sin(theta_rad)])
    new_y = np.array([1, 0, 0])
    new_z = -np.cross(new_x, new_y)

    # Incorporate fixed offset
    shifted_center = center + fixed_offset * new_z

    # Set up dimensions
    Lu_pixels = int(Lu)
    Lv_pixels = int(Lv)
    Lz_pixels = int(Lz)

    # Create grids for new coordinates
    u = np.linspace(-Lu/2, Lu/2, Lu_pixels)
    v = np.linspace(-Lv/2, Lv/2, Lv_pixels)
    d = np.linspace(-D/2, D/2, Lz_pixels)

    # Create meshgrid
    D_grid, V_grid, U_grid = np.meshgrid(d, v, u, indexing='ij')

    # Map coordinates from new to original volume
    orig_Z = shifted_center[0] + D_grid * new_z[0] + V_grid * new_y[0] + U_grid * new_x[0]
    orig_Y = shifted_center[1] + D_grid * new_z[1] + V_grid * new_y[1] + U_grid * new_x[1]
    orig_X = shifted_center[2] + D_grid * new_z[2] + V_grid * new_y[2] + U_grid * new_x[2]

    coords = [orig_Z, orig_Y, orig_X]

    # Reslice the original volume (first transformation)
    ref_volume = ndimage.map_coordinates(volume, coords, order=1, mode='constant', cval=0)

    # ===== SECOND TRANSFORMATION =====
    # Parameters for second transformation
    new_best_slice = 51
    Lu_new = 150
    Lv_new = 150
    Lz_new = 101
    D_new = 100
    fixed_offset = 0
    new_theta = -18

    # Dimensions of the intermediate volume
    Lz_old, Lv_old, Lu_old = ref_volume.shape

    # Define center in intermediate volume coordinates
    center_new = np.array([new_best_slice, Lv_old/2, Lu_old/2])

    # Define new coordinate axes
    new_theta_rad = np.deg2rad(new_theta)
    new_x_re = np.array([0, np.cos(new_theta_rad), np.sin(new_theta_rad)])
    new_y_re = np.array([1, 0, 0])
    new_z_re = -np.cross(new_x_re, new_y_re)

    # Incorporate fixed offset
    shifted_center = center_new + fixed_offset * new_z_re

    # Set up dimensions
    Lu_pixels = int(Lu_new)
    Lv_pixels = int(Lv_new)
    Lz_pixels = int(Lz_new)

    # Create grids for local coordinates
    u = np.linspace(-Lu_new/2, Lu_new/2, Lu_pixels)
    v = np.linspace(-Lv_new/2, Lv_new/2, Lv_pixels)
    d = np.linspace(-D_new/2, D_new/2, Lz_pixels)

    # Meshgrid
    D_grid, V_grid, U_grid = np.meshgrid(d, v, u, indexing='ij')

    # Map coordinates
    orig_d = shifted_center[0] + D_grid * new_z_re[0] + V_grid * new_y_re[0] + U_grid * new_x_re[0]
    orig_v = shifted_center[1] + D_grid * new_z_re[1] + V_grid * new_y_re[1] + U_grid * new_x_re[1]
    orig_u = shifted_center[2] + D_grid * new_z_re[2] + V_grid * new_y_re[2] + U_grid * new_x_re[2]

    coords_new = [orig_d, orig_v, orig_u]

    # Resample the intermediate volume (second transformation)
    new_ref_volume = ndimage.map_coordinates(ref_volume, coords_new, order=1, mode='constant', cval=0)

    return new_ref_volume

def process_transformed_slice(transformed_data, modality, slice_idx=None):
    """
    Process a slice from transformed data by:
    1. Swapping x and y axes
    2. Flipping y-axis vertically (upside down)
    
    Parameters:
    -----------
    transformed_data : dict
        Dictionary containing all transformed modalities
    modality : str
        The modality to process (e.g., 'T2', 'T1')
    slice_idx : int or None
        The slice index to extract (if None, uses middle slice)
    
    Returns:
    --------
    processed_slice : 2D numpy array
        The processed slice with axes swapped and flipped
    """
    # If slice_idx is None, use the middle slice
    if slice_idx is None:
        slice_idx = transformed_data[modality].shape[0] // 2

    # Extract the slice
    central_slice = transformed_data[modality][slice_idx, :, :]

    # 1. Transpose the slice (swap x and y)
    # 2. Flip vertically (upside down)
    processed_slice = central_slice.T[::-1, :]

    return processed_slice

def reorganize_volume(volume):
    """
    Reorganize a 3D volume by:
    1. Keeping z-dimension (first dimension) unchanged
    2. Swapping x and y dimensions (transposing each slice)
    3. Flipping the new y-axis vertically
    
    Parameters:
    -----------
    volume : 3D numpy array with shape (z, y, x)
    
    Returns:
    --------
    reorganized : 3D numpy array with shape (z, x, y) and y-flipped
    """
    z_slices = volume.shape[0]
    reorganized = np.zeros((z_slices, volume.shape[2], volume.shape[1]))

    for z in range(z_slices):
        # Get the slice, transpose it, and flip y-axis
        reorganized[z, :, :] = volume[z, :, :].T[::-1, :]

    return reorganized
