#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MRXCAT Cardiac MRI Reconstruction

This script contains functions for reconstructing MR images from k-space data.
"""

import numpy as np
from scipy.ndimage import gaussian_filter
from scipy.interpolate import griddata, NearestNDInterpolator

def grid_spiral_to_cartesian(spiral_kspace, weight_map, image_size):
    """
    Grid spiral k-space data to Cartesian coordinates using weight map
    
    Parameters:
    -----------
    spiral_kspace : 1D complex array
        Spiral k-space data
    weight_map : list
        List of weights for each spiral point
    image_size : int
        Size of the output image
        
    Returns:
    --------
    sparse_kspace : 2D complex array
        Gridded k-space data
    """
    sparse_kspace = np.zeros((image_size, image_size), dtype=complex)
    weight_grid = np.zeros((image_size, image_size), dtype=float)
    
    for i, local_weights in enumerate(weight_map):
        for y, x, weight in local_weights:
            sparse_kspace[y, x] += weight * spiral_kspace[i]
            weight_grid[y, x] += weight
    
    mask = weight_grid > 0
    sparse_kspace[mask] /= weight_grid[mask]
    
    return sparse_kspace

def apply_gridding(sparse_kspace, weight_grid=None, sigma=0.3):
    """
    Apply gridding reconstruction with Gaussian smoothing
    
    Parameters:
    -----------
    sparse_kspace : 2D complex array
        Sparse k-space data
    weight_grid : 2D array or None
        Weight grid for normalization
    sigma : float
        Sigma for Gaussian smoothing
        
    Returns:
    --------
    recon : 2D array
        Reconstructed image
    """
    real_part = gaussian_filter(sparse_kspace.real, sigma=sigma)
    imag_part = gaussian_filter(sparse_kspace.imag, sigma=sigma)
    smoothed_kspace = real_part + 1j * imag_part
    
    recon = np.fft.ifft2(np.fft.ifftshift(smoothed_kspace))
    return np.abs(recon)

def calculate_density_compensation(kx_rel, ky_rel, max_radius, N):
    """
    Calculate density compensation factors for non-uniform sampling
    
    Parameters:
    -----------
    kx_rel, ky_rel : 1D arrays
        k-space coordinates relative to center
    max_radius : float
        Maximum k-space radius
    N : int
        Number of interleaves
        
    Returns:
    --------
    density_comp : 1D array
        Density compensation factors
    """
    # Calculate radius and normalize
    radius = np.sqrt(kx_rel**2 + ky_rel**2)
    radius_norm = radius / max_radius
    
    # Calculate angular density based on interleave pattern
    angles = np.arctan2(ky_rel, kx_rel)
    # Normalize angles to [0, 2π]
    angles = np.mod(angles, 2*np.pi)
    
    # Calculate distance to nearest interleave
    angle_per_interleave = 2*np.pi / N
    angle_to_nearest = np.minimum(np.mod(angles, angle_per_interleave),
                                angle_per_interleave - np.mod(angles, angle_per_interleave))
    
    # Use a simpler, more uniform angular factor
    angle_factor = np.ones_like(angles)  # Start with uniform weighting
    
    # Apply a more uniform radial weighting
    # Use a simple ramp function that increases linearly with radius
    radial_weight = 0.5 + 0.5 * radius_norm
    
    # Ensure the center of k-space has lower weight (since it's more densely sampled)
    center_mask = radius_norm <= 0.1
    radial_weight[center_mask] = 0.5
    
    # Final density compensation is just the radial weight
    density_comp = radial_weight
    
    return density_comp

def prepare_kernel_weights(kx_pixel_float, ky_pixel_float, image_size, kernel_width=1.0, kernel_beta=13.0):
    """
    Prepare kernel weights for spiral trajectory points
    
    Parameters:
    -----------
    kx_pixel_float, ky_pixel_float : 1D arrays
        Floating-point pixel coordinates for spiral trajectory
    image_size : int
        Size of the image
    kernel_width : float
        Width of the kernel in pixels
    kernel_beta : float
        Shape parameter for Kaiser-Bessel kernel
        
    Returns:
    --------
    weight_map : list
        List of weights for each spiral point
    """
    weight_map = []
    
    print("Calculating kernel weights for spiral trajectory points...")
    for i in range(len(kx_pixel_float)):
        x_center = kx_pixel_float[i]
        y_center = ky_pixel_float[i]
        local_weights = []
        
        x_min = max(0, int(x_center - kernel_width))
        x_max = min(image_size-1, int(x_center + kernel_width))
        y_min = max(0, int(y_center - kernel_width))
        y_max = min(image_size-1, int(y_center + kernel_width))
        
        total_weight = 0.0
        for y in range(y_min, y_max+1):
            for x in range(x_min, x_max+1):
                # Calculate distance from center
                dx = x - x_center
                dy = y - y_center
                distance = np.sqrt(dx*dx + dy*dy)
                
                if distance <= kernel_width:
                    # Apply Kaiser-Bessel kernel
                    weight = np.i0(kernel_beta * np.sqrt(1 - (distance/kernel_width)**2)) / np.i0(kernel_beta)
                    local_weights.append((y, x, weight))
                    total_weight += weight
        
        # Normalize weights
        if total_weight > 0:
            local_weights = [(y, x, w/total_weight) for y, x, w in local_weights]
        
        weight_map.append(local_weights)
        
        if i % 5000 == 0 and i > 0:
            print(f"  Processed {i}/{len(kx_pixel_float)} points")
    
    return weight_map

def convert_spiral_to_cartesian(kx_spiral, ky_spiral, k_max, image_size):
    """
    Convert spiral trajectory from k-space units to pixel coordinates
    
    Parameters:
    -----------
    kx_spiral, ky_spiral : 1D arrays
        k-space coordinates in k-space units
    k_max : float
        Maximum k-space radius
    image_size : int
        Size of the image
        
    Returns:
    --------
    kx_pixel_float, ky_pixel_float : 1D arrays
        Floating-point pixel coordinates
    kx_pixel_int, ky_pixel_int : 1D arrays
        Integer pixel coordinates
    """
    # Convert from k-space units to pixel coordinates
    kx_pixel_float = (kx_spiral / k_max) * (image_size/2) + image_size/2
    ky_pixel_float = (ky_spiral / k_max) * (image_size/2) + image_size/2
    
    # Clip to valid pixel range
    kx_pixel_float = np.clip(kx_pixel_float, 0, image_size-1)
    ky_pixel_float = np.clip(ky_pixel_float, 0, image_size-1)
    
    # Integer pixel coordinates for indexing
    kx_pixel_int = np.clip(np.round(kx_pixel_float).astype(int), 0, image_size-1)
    ky_pixel_int = np.clip(np.round(ky_pixel_float).astype(int), 0, image_size-1)
    
    return kx_pixel_float, ky_pixel_float, kx_pixel_int, ky_pixel_int
