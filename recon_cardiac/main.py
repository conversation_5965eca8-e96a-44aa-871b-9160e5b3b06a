#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MRXCAT Cardiac MRI Reconstruction - Main Script

This is the main script for the MRXCAT cardiac MRI reconstruction pipeline.
It orchestrates the entire process from data loading to visualization.
"""

import os
import numpy as np
import matplotlib.pyplot as plt
from scipy.ndimage import gaussian_filter

# Import modules from the package
from recon_cardiac.data_loader import (
    load_cardiac_data, create_cardiac_mask, create_anatomy_overlay,
    extract_axial_slice, crop_maps, param_settings
)
from recon_cardiac.oblique_transform import (
    transform_modality_to_oblique_view, process_transformed_slice, reorganize_volume
)
from recon_cardiac.spiral_trajectory import (
    generate_spiral_trajectory, plot_vds_results
)
from recon_cardiac.mri_simulation import (
    simulate_mri_signal, simulate_spiral_t2star_decay
)
from recon_cardiac.reconstruction import (
    grid_spiral_to_cartesian, apply_gridding, calculate_density_compensation,
    prepare_kernel_weights, convert_spiral_to_cartesian
)
from recon_cardiac.visualization import (
    visualize_parameter_maps, visualize_spiral_trajectory,
    visualize_reconstruction_results, visualize_decay_profiles,
    visualize_phase_effects, print_simulation_summary
)

def main():
    """
    Main function to run the cardiac MRI reconstruction pipeline
    """
    print("=" * 80)
    print("MRXCAT Cardiac MRI Reconstruction Pipeline")
    print("=" * 80)

    # ===== STEP 1: LOAD DATA =====
    print("\n1. Loading cardiac parameter maps...")
    data_path = "./mrxcat-2.0-main/female50/cardiac_region_new/cardiac_volume.npz"
    data = load_cardiac_data(data_path)

    # ===== STEP 2: TRANSFORM TO OBLIQUE VIEW =====
    print("\n2. Transforming volumes to oblique view...")
    transformed_data = {}
    for key in ['T1', 'T2', 'PD', 'B0', 'ShimmedB0', 'T2star', 'T2star_plus', 'Labels']:
        if key in data:
            print(f"  Transforming {key}...")
            transformed_data[key] = transform_modality_to_oblique_view(data[key])

    # ===== STEP 3: REORGANIZE DATA =====
    print("\n3. Reorganizing data...")
    reorganized_data = {}
    for key, volume in transformed_data.items():
        reorganized_data[key] = reorganize_volume(volume)

    # ===== STEP 4: EXTRACT SLICE AND CROP =====
    print("\n4. Extracting slice and cropping...")
    z_slice = 50  # Choose a good slice

    # Create cardiac mask for the chosen slice
    cardiac_mask = create_cardiac_mask(reorganized_data['Labels'], z_slice)

    # Extract maps from reorganized data
    maps_dict = {}
    for key in ['PD', 'T1', 'T2', 'T2star', 'T2star_plus', 'B0', 'ShimmedB0']:
        maps_dict[key] = reorganized_data[key][z_slice,:,:]

    # Crop maps to focus on the heart
    print("  Cropping maps to focus on the heart...")
    cropped_maps, crop_bounds = crop_maps(maps_dict, cardiac_mask, crop_size=90)

    # ===== STEP 5: VISUALIZE PARAMETER MAPS =====
    print("\n5. Visualizing parameter maps...")
    visualize_parameter_maps(cropped_maps, param_settings)

    # ===== STEP 6: SIMULATE MRI SIGNALS =====
    print("\n6. Simulating MRI signals...")
    # Sequence parameters
    tr_gre = 10.0  # ms
    te_gre = 5.0   # ms
    fa_gre = np.deg2rad(15)  # radians

    # Generate MR images
    gre_t2star_complex = simulate_mri_signal(
        cropped_maps['PD'], cropped_maps['T1'], cropped_maps['T2'],
        cropped_maps['T2star'], cropped_maps['ShimmedB0'],
        tr_gre, te_gre, fa_gre, 'gradient_echo', use_shimmed_b0=True
    )

    # Convert to image
    gre_t2star_image = np.abs(gre_t2star_complex)

    # ===== STEP 7: GENERATE SPIRAL TRAJECTORY =====
    print("\n7. Generating spiral trajectory...")
    # Parameters for spiral trajectory
    smax = 20000       # Maximum slew rate (G/cm/s)
    gmax = 4           # Maximum gradient amplitude (G/cm)
    T = 4e-6           # Sampling period (s)
    N = 1              # Number of interleaves
    Fcoeff = [18, 0]   # FOV coefficients
    sf = 1.3           # Scale factor
    resolution = 0.2   # Resolution in mm

    # Generate the spiral trajectory
    k, g, s, time, r, theta = generate_spiral_trajectory(
        smax, gmax, T, N, Fcoeff, sf, resolution
    )

    # Extract kx and ky components
    kx_spiral = np.real(k)
    ky_spiral = np.imag(k)

    # Visualize the spiral trajectory
    visualize_spiral_trajectory(kx_spiral, ky_spiral, np.max(r), 90)

    # ===== STEP 8: CONVERT TO PIXEL COORDINATES =====
    print("\n8. Converting to pixel coordinates...")
    image_size = 90
    k_max = np.max(r)

    # Convert from k-space units to pixel coordinates
    kx_pixel_float, ky_pixel_float, kx_pixel_int, ky_pixel_int = convert_spiral_to_cartesian(
        kx_spiral, ky_spiral, k_max, image_size
    )

    # ===== STEP 9: PREPARE KERNEL WEIGHTS =====
    print("\n9. Preparing kernel weights...")
    weight_map = prepare_kernel_weights(kx_pixel_float, ky_pixel_float, image_size)

    # ===== STEP 10: GENERATE K-SPACE DATA =====
    print("\n10. Generating k-space data...")
    # Convert image to k-space
    kspace = np.fft.fftshift(np.fft.fft2(gre_t2star_image))

    # Sample k-space at spiral trajectory points
    spiral_kspace = np.zeros(len(kx_pixel_float), dtype=complex)
    for i, local_weights in enumerate(weight_map):
        value = 0.0
        total_weight = 0.0
        for y, x, weight in local_weights:
            value += weight * kspace[y, x]
            total_weight += weight
        if total_weight > 0:
            spiral_kspace[i] = value / total_weight

    # ===== STEP 11: SIMULATE T2* DECAY AND B0 EFFECTS =====
    print("\n11. Simulating T2* decay and B0 effects...")
    # Make a copy of the original spiral k-space data
    spiral_raw_kspace = spiral_kspace.copy()

    # Simulate three versions for comparison
    spiral_kspace_original = spiral_raw_kspace.copy()

    # With T2* decay only (no B0)
    spiral_kspace_t2star, orig_image = simulate_spiral_t2star_decay(
        spiral_raw_kspace, ky_pixel_int, kx_pixel_int, gre_t2star_image,
        cropped_maps['T2star_plus'], np.zeros_like(cropped_maps['B0']),
        time, weight_map
    )

    # With both T2* and B0 effects
    spiral_kspace_t2star_b0, _ = simulate_spiral_t2star_decay(
        spiral_raw_kspace, ky_pixel_int, kx_pixel_int, gre_t2star_image,
        cropped_maps['T2star_plus'], cropped_maps['ShimmedB0'],
        time, weight_map
    )

    # ===== STEP 12: RECONSTRUCT IMAGES =====
    print("\n12. Reconstructing images...")
    # Create sparse k-space representations for each version
    sparse_kspace_original = grid_spiral_to_cartesian(spiral_kspace_original, weight_map, image_size)
    sparse_kspace_t2star = grid_spiral_to_cartesian(spiral_kspace_t2star, weight_map, image_size)
    sparse_kspace_t2star_b0 = grid_spiral_to_cartesian(spiral_kspace_t2star_b0, weight_map, image_size)

    # Apply Hamming window to reduce Gibbs ringing
    hamming_window = np.outer(
        np.hamming(image_size),
        np.hamming(image_size)
    )
    sparse_kspace_original *= hamming_window
    sparse_kspace_t2star *= hamming_window
    sparse_kspace_t2star_b0 *= hamming_window

    # Reconstruct images
    recon_original = apply_gridding(sparse_kspace_original)
    recon_t2star = apply_gridding(sparse_kspace_t2star)
    recon_t2star_b0 = apply_gridding(sparse_kspace_t2star_b0)

    # ===== STEP 13: VISUALIZE RESULTS =====
    print("\n13. Visualizing results...")
    # Visualize reconstruction results
    visualize_reconstruction_results(
        recon_original, recon_t2star, recon_t2star_b0,
        cropped_maps['T2star_plus'], cropped_maps['ShimmedB0'],
        cardiac_mask, reorganized_data['Labels'], z_slice
    )

    # Visualize decay profiles
    visualize_decay_profiles(
        r, k_max, spiral_kspace_original, spiral_kspace_t2star,
        spiral_kspace_t2star_b0, cropped_maps['T2star_plus'], time,
        recon_original, recon_t2star_b0, cardiac_mask, image_size
    )

    # Visualize phase effects
    phase_only_image = orig_image * np.exp(-1j * 2 * np.pi * cropped_maps['ShimmedB0'] * time[-1])
    phase_orig = np.angle(orig_image)
    phase_b0 = np.angle(phase_only_image)
    phase_diff = phase_b0 - phase_orig

    visualize_phase_effects(
        orig_image, cropped_maps['ShimmedB0'], time,
        recon_t2star, recon_t2star_b0
    )

    # Print simulation summary
    print_simulation_summary(
        cropped_maps['T2star_plus'], cropped_maps['ShimmedB0'],
        recon_original, recon_t2star, recon_t2star_b0, phase_diff
    )

    print("\nReconstruction pipeline completed successfully!")

if __name__ == "__main__":
    main()
