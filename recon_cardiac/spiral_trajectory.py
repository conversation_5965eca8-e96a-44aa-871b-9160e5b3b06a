#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MRXCAT Cardiac MRI Spiral Trajectory Generation

This script contains functions for generating spiral k-space trajectories.
"""

import numpy as np
import matplotlib.pyplot as plt

def qdf(a, b, c):
    """
    Solves quadratic equation ax^2 + bx + c = 0

    Returns:
        Roots of the quadratic equation
    """
    d = b**2 - 4*a*c
    if d < 0:
        # Handle negative discriminant case
        d = complex(d, 0)
    root1 = (-b + np.sqrt(d)) / (2*a)
    root2 = (-b - np.sqrt(d)) / (2*a)
    return np.array([root1, root2])

def findq2r2(smax, gmax, r, r1, T, Ts, N, Fcoeff, rmax, z=0):
    """
    Calculates the second derivatives of r and theta (q) that satisfy hardware and FOV constraints

    Parameters:
        smax: Maximum slew rate in G/cm/s
        gmax: Maximum gradient amplitude in G/cm
        r: Current value of the k-space radius
        r1: Current derivative of r
        T: Gradient sample period
        Ts: Data sampling period
        N: Number of spiral interleaves
        Fcoeff: FOV coefficients for variable density
        rmax: Maximum k-space radius
        z: R/L for gradient coil (voltage model parameter)

    Returns:
        q2: Second derivative of angle theta
        r2: Second derivative of radius r
    """
    gamma = 4258  # Hz/G

    smax = smax + z*gmax

    # Calculate FOV and its derivative for current r
    F = 0
    dFdr = 0
    for rind in range(len(Fcoeff)):
        F += Fcoeff[rind] * (r/rmax)**(rind)
        if rind > 0:
            dFdr += rind * Fcoeff[rind] * (r/rmax)**(rind-1) / rmax

    # FOV limit on gradient
    GmaxFOV = N/gamma / F / Ts
    if not hasattr(findq2r2, "printed"):
        print(f'Required GmaxFOV: {GmaxFOV}')
        findq2r2.printed = True
    Gmax = min(GmaxFOV, gmax)

    # Maximum allowed r1 based on gradient amplitude limit
    maxr1 = np.sqrt((gamma*Gmax)**2 / (1 + (2*np.pi*F*r/N)**2))

    if r1 > maxr1:
        # Gradient amplitude limited case
        r2 = (maxr1 - r1) / T
    else:
        # Slew rate limited case
        twopiFoN = 2*np.pi*F/N
        twopiFoN2 = twopiFoN**2

        # Coefficients for the quadratic equation in r2
        A = 1 + twopiFoN2*r*r
        B = 2*twopiFoN2*r*r1*r1 + 2*twopiFoN2/F*dFdr*r*r*r1*r1 + 2*z*r1 + 2*twopiFoN2*r1*r
        C1 = twopiFoN2**2*r*r*r1**4 + 4*twopiFoN2*r1**4 + (2*np.pi/N*dFdr)**2*r*r*r1**4 + 4*twopiFoN2/F*dFdr*r*r1**4 - (gamma)**2*smax**2
        C2 = z*(z*r1**2 + z*twopiFoN2*r1**2 + 2*twopiFoN2*r1**3*r + 2*twopiFoN2/F*dFdr*r1**3*r)
        C = C1 + C2

        # Solve quadratic equation
        rts = qdf(A, B, C)
        r2 = np.real(rts[0])  # Use first root

        # Calculate and check resulting slew rate
        slew = 1/gamma * (r2 - twopiFoN2*r*r1**2 + 1j*twopiFoN*(2*r1**2 + r*r2 + dFdr/F*r*r1**2))
        sr = np.abs(slew)/smax

        if sr > 1.01:
            print(f"Slew violation, slew = {round(np.abs(slew))}, smax = {round(smax)}, sr={sr:.3f}, r={r:.3f}, r1={r1:.3f}")

    # Calculate q2 from other parameters
    q2 = 2*np.pi/N*dFdr*r1**2 + 2*np.pi*F/N*r2

    return q2, r2

def vds(smax, gmax, T, N, Fcoeff, rmax, z=0):
    """
    Variable Density Spiral trajectory generation

    Parameters:
        smax: Maximum slew rate G/cm/s
        gmax: Maximum gradient G/cm
        T: Sampling period (s)
        N: Number of interleaves
        Fcoeff: FOV coefficients - FOV(r) = Sum_k Fcoeff[k]*(r/rmax)^k
        rmax: Maximum k-space radius (cm^-1)
        z: R/L for gradient coil model

    Returns:
        k: k-space trajectory (kx+iky) in cm^-1
        g: gradient waveform (Gx+iGy) in G/cm
        s: derivative of g (Sx+iSy) in G/cm/s
        time: time points corresponding to trajectory (s)
        r: k-space radius vs time
        theta: angle vs time
    """
    print('Variable Density Spiral Generation')
    gamma = 4258  # Hz/G

    # Oversampling for trajectory calculation
    oversamp = 8  # Keep this even
    To = T / oversamp  # Oversampled period

    # Initialize variables
    q0 = 0
    q1 = 0
    r0 = 0
    r1 = 0
    t = 0
    count = 0

    # Pre-allocate arrays (can extend later if needed)
    max_points = 10000000
    theta = np.zeros(max_points)
    r = np.zeros(max_points)
    time = np.zeros(max_points)

    # Main loop to generate trajectory
    while r0 < rmax:
        # Get the next point on the trajectory
        q2, r2 = findq2r2(smax, gmax, r0, r1, To, T, N, Fcoeff, rmax, z)

        # Integrate for θ, θ', r, and r'
        q1 = q1 + q2 * To
        q0 = q0 + q1 * To
        t = t + To

        r1 = r1 + r2 * To
        r0 = r0 + r1 * To

        # Store values
        count += 1
        theta[count] = q0
        r[count] = r0
        time[count] = t

        if count % 10000 == 0:
            print(f'{count} points, |k|={r0:.6f}')

        # Break if we've reached array limit
        if count >= max_points - 1:
            print("Warning: reached maximum array size")
            break

    # Trim arrays to used size
    theta = theta[:count+1]
    r = r[:count+1]
    time = time[:count+1]

    # Downsample to original sampling rate
    theta_ds = theta[oversamp//2::oversamp]
    r_ds = r[oversamp//2::oversamp]
    time_ds = time[oversamp//2::oversamp]

    # Keep the length a multiple of 4 (to match original code)
    length = 4 * (len(theta_ds) // 4)
    theta_ds = theta_ds[:length]
    r_ds = r_ds[:length]
    time_ds = time_ds[:length]

    # Calculate k-space trajectory, gradients, and slew rates
    k = r_ds * np.exp(1j * theta_ds)

    # Calculate gradients
    g = np.zeros_like(k, dtype=complex)
    g[:-1] = (k[1:] - k[:-1]) / T / gamma
    g[-1] = g[-2]  # Extrapolate last point

    # Calculate slew rates
    s = np.zeros_like(g, dtype=complex)
    s[:-1] = (g[1:] - g[:-1]) / T
    s[-1] = s[-2]  # Extrapolate last point

    # Debug information about trajectory
    print(f"Trajectory summary:")
    print(f"  k-space points: {len(k)}")
    print(f"  Max gradient: {np.max(np.abs(g)):.3f} G/cm")
    print(f"  Max slew rate: {np.max(np.abs(s)):.3f} G/cm/s")
    print(f"  Readout time: {time_ds[-1]*1000:.2f} ms")

    return k, g, s, time_ds, r_ds, theta_ds

def plot_vds_results(time, k, g, s):
    """
    Plot the results of the VDS trajectory generation
    """
    # Undersample for plotting
    tp = time[::10]
    kp = k[::10]
    gp = g[::10]
    sp = s[::10]
    
    plt.figure(figsize=(12, 10))
    
    # Plot 1: k-space trajectory
    plt.subplot(2, 2, 1)
    plt.plot(np.real(kp), np.imag(kp))
    plt.title('ky vs kx')
    plt.xlabel('kx (cm$^{-1}$)')
    plt.ylabel('ky (cm$^{-1}$)')
    plt.axis('square')
    plt.grid(True)
    
    # Plot 2: k-space vs time
    plt.subplot(2, 2, 2)
    plt.plot(tp, np.real(kp), 'c-', label='kx')
    plt.plot(tp, np.imag(kp), 'g-', label='ky')
    plt.title('k-space vs Time')
    plt.xlabel('Time (s)')
    plt.ylabel('k (cm$^{-1}$)')
    plt.legend()
    plt.grid(True)
    
    # Plot 3: Gradient vs time
    plt.subplot(2, 2, 3)
    plt.plot(tp, np.real(gp), 'c-', label='Gx')
    plt.plot(tp, np.imag(gp), 'g-', label='Gy')
    plt.title('Gradient vs Time')
    plt.xlabel('Time (s)')
    plt.ylabel('G (G/cm)')
    plt.legend()
    plt.grid(True)
    
    # Plot 4: Slew rate vs time
    plt.subplot(2, 2, 4)
    plt.plot(tp, np.real(sp), 'c-', label='Sx')
    plt.plot(tp, np.imag(sp), 'g-', label='Sy')
    plt.title('Slew Rate vs Time')
    plt.xlabel('Time (s)')
    plt.ylabel('S (G/cm/s)')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.show()

def generate_spiral_trajectory(smax=20000, gmax=4, T=4e-6, N=1, Fcoeff=[18, 0], sf=1.3, resolution=0.2):
    """
    Generate a spiral trajectory with the given parameters
    
    Parameters:
    -----------
    smax : float
        Maximum slew rate in G/cm/s
    gmax : float
        Maximum gradient amplitude in G/cm
    T : float
        Sampling period in seconds
    N : int
        Number of interleaves
    Fcoeff : list
        FOV coefficients
    sf : float
        Scale factor
    resolution : float
        Resolution in mm
        
    Returns:
    --------
    k : complex array
        k-space trajectory (kx+iky)
    g : complex array
        Gradient waveform (Gx+iGy)
    s : complex array
        Slew rate (Sx+iSy)
    time : array
        Time points
    r : array
        k-space radius
    theta : array
        Angle
    """
    # Maximum k-space radius for given resolution
    rmax = sf * 1/(2*resolution)
    
    # Print spiral trajectory parameters
    print(f"Spiral trajectory parameters:")
    print(f"  Number of interleaves: {N}")
    print(f"  Maximum slew rate: {smax} G/cm/s")
    print(f"  Maximum gradient: {gmax} G/cm")
    print(f"  Sampling period: {T*1e6} μs")
    print(f"  Maximum k-space radius: {rmax}")
    
    # Generate the spiral trajectory
    k, g, s, time, r, theta = vds(smax, gmax, T, N, Fcoeff, rmax)
    
    return k, g, s, time, r, theta
