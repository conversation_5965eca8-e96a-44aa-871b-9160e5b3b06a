import numpy as np
import matplotlib.pyplot as plt
from scipy.interpolate import RegularGridInterpolator
from scipy.special import i0  # Modified Bessel function of the first kind, order 0

def generate_spiral_trajectory(matrix_size, interleaves=1, field_of_view=1.0):
    """
    Generate spiral trajectory for k-space sampling.
    
    Parameters:
    -----------
    matrix_size : int
        Size of the image matrix (assumed square)
    interleaves : int
        Number of spiral interleaves
    field_of_view : float
        Field of view in meters
        
    Returns:
    --------
    kx, ky : ndarray
        k-space coordinates of the spiral trajectory
    """
    N = matrix_size
    num_points = N * N
    k = np.zeros((num_points, 2))
    
    # Spiral parameters
    max_k = N / 2
    turns = 10
    
    # Generate single spiral
    t = np.linspace(0, 2 * np.pi * turns, num_points)
    for i in range(interleaves):
        phi = 2 * np.pi * i / interleaves
        k[:, 0] = max_k * t / (2 * np.pi * turns) * np.cos(t + phi)
        k[:, 1] = max_k * t / (2 * np.pi * turns) * np.sin(t + phi)
    
    return k[:, 0], k[:, 1]

def k_to_pixel(kx, ky, matrix_size):
    """Convert k-space coordinates to pixel coordinates"""
    kx_pixel = kx + matrix_size / 2
    ky_pixel = ky + matrix_size / 2
    return kx_pixel, ky_pixel

def calculate_density_compensation(kx, ky):
    """
    Calculate density compensation function for spiral trajectory.
    
    For spiral trajectories, DCF is approximately proportional to radius.
    """
    # Calculate distance from k-space center
    k_radius = np.sqrt(kx**2 + ky**2)
    
    # For spiral trajectories, DCF is approximately proportional to radius
    dcf = k_radius
    
    # Normalize DCF
    dcf = dcf / np.max(dcf)
    
    # Avoid division by zero at k-space center
    dcf = np.maximum(dcf, 0.01)
    
    return dcf

def kaiser_bessel_kernel(distance, kernel_width=2.5, beta=2.34):
    """
    Kaiser-Bessel kernel function for gridding.
    
    Parameters:
    -----------
    distance : float or ndarray
        Distance from grid point
    kernel_width : float
        Width of the kernel
    beta : float
        Shape parameter of the Kaiser-Bessel function
        
    Returns:
    --------
    kernel_value : float or ndarray
        Value of the kernel at the given distance
    """
    if np.isscalar(distance):
        if distance > kernel_width:
            return 0.0
        x = distance / kernel_width * np.pi
        return i0(beta * np.sqrt(1 - (x/np.pi)**2)) / i0(beta)
    else:
        result = np.zeros_like(distance)
        mask = distance <= kernel_width
        x = distance[mask] / kernel_width * np.pi
        result[mask] = i0(beta * np.sqrt(1 - (x/np.pi)**2)) / i0(beta)
        return result

def calculate_deapodization(matrix_size, kernel_width=2.5, beta=2.34):
    """
    Calculate deapodization function to correct for kernel effects.
    
    Parameters:
    -----------
    matrix_size : int
        Size of the image matrix (assumed square)
    kernel_width : float
        Width of the kernel
    beta : float
        Shape parameter of the Kaiser-Bessel function
        
    Returns:
    --------
    deapod : ndarray
        Deapodization function
    """
    x = np.linspace(-1, 1, matrix_size)
    y = np.linspace(-1, 1, matrix_size)
    X, Y = np.meshgrid(x, y)
    R = np.sqrt(X**2 + Y**2)
    
    # Calculate deapodization function
    deapod = np.zeros((matrix_size, matrix_size))
    for i in range(matrix_size):
        for j in range(matrix_size):
            r = R[i, j]
            if r <= 1.0:
                # Fourier transform of Kaiser-Bessel is another Kaiser-Bessel
                x = r * kernel_width
                if x < 1e-6:
                    deapod[i, j] = 1.0
                else:
                    deapod[i, j] = np.abs(np.sin(np.pi * x) / (np.pi * x))
            else:
                deapod[i, j] = 1.0
    
    # Avoid division by zero
    deapod = np.maximum(deapod, 0.01)
    
    return deapod

def cartesian_to_spiral(cartesian_kspace, kx, ky, matrix_size):
    """
    Interpolate Cartesian k-space to spiral trajectory with improved method.
    
    Parameters:
    -----------
    cartesian_kspace : ndarray
        Cartesian k-space data
    kx, ky : ndarray
        k-space coordinates of the spiral trajectory
    matrix_size : int
        Size of the image matrix (assumed square)
        
    Returns:
    --------
    spiral_kspace : ndarray
        k-space data along the spiral trajectory
    """
    # Convert k-space coordinates to pixel coordinates
    kx_pixel, ky_pixel = k_to_pixel(kx, ky, matrix_size)
    
    # Create interpolator using linear interpolation
    x = np.arange(matrix_size)
    y = np.arange(matrix_size)
    interpolator = RegularGridInterpolator((y, x), cartesian_kspace, 
                                         method='linear', bounds_error=False, fill_value=0)
    
    # Interpolate at spiral trajectory points
    spiral_points = np.column_stack((ky_pixel, kx_pixel))
    spiral_kspace = interpolator(spiral_points)
    
    return spiral_kspace

def spiral_to_cartesian(spiral_kspace, kx, ky, matrix_size):
    """
    Improved gridding method to interpolate spiral k-space to Cartesian grid.
    
    Parameters:
    -----------
    spiral_kspace : ndarray
        k-space data along the spiral trajectory
    kx, ky : ndarray
        k-space coordinates of the spiral trajectory
    matrix_size : int
        Size of the image matrix (assumed square)
        
    Returns:
    --------
    gridded_kspace : ndarray
        Gridded k-space data on Cartesian grid
    """
    # Convert k-space coordinates to pixel coordinates
    kx_pixel, ky_pixel = k_to_pixel(kx, ky, matrix_size)
    
    # Calculate density compensation function
    dcf = calculate_density_compensation(kx, ky)
    
    # Apply density compensation
    spiral_kspace_compensated = spiral_kspace * dcf
    
    # Create empty Cartesian k-space
    gridded_kspace = np.zeros((matrix_size, matrix_size), dtype=complex)
    
    # Define gridding parameters
    kernel_width = 2.5  # Width of Kaiser-Bessel kernel
    kernel_radius = int(np.ceil(kernel_width))
    
    # Perform gridding with Kaiser-Bessel kernel
    for i in range(len(spiral_kspace)):
        # Find grid points within kernel radius
        x_center = kx_pixel[i]
        y_center = ky_pixel[i]
        x_min = max(0, int(x_center - kernel_radius))
        x_max = min(matrix_size-1, int(x_center + kernel_radius))
        y_min = max(0, int(y_center - kernel_radius))
        y_max = min(matrix_size-1, int(y_center + kernel_radius))
        
        # Apply kernel to nearby grid points
        for x in range(x_min, x_max + 1):
            for y in range(y_min, y_max + 1):
                distance = np.sqrt((x - x_center)**2 + (y - y_center)**2)
                weight = kaiser_bessel_kernel(distance, kernel_width)
                gridded_kspace[y, x] += spiral_kspace_compensated[i] * weight
    
    return gridded_kspace

def reconstruct_from_spiral(spiral_kspace, kx, ky, matrix_size):
    """
    Complete reconstruction from spiral k-space.
    
    Parameters:
    -----------
    spiral_kspace : ndarray
        k-space data along the spiral trajectory
    kx, ky : ndarray
        k-space coordinates of the spiral trajectory
    matrix_size : int
        Size of the image matrix (assumed square)
        
    Returns:
    --------
    reconstructed_image : ndarray
        Reconstructed image
    """
    # Grid the spiral data to Cartesian coordinates
    gridded_kspace = spiral_to_cartesian(spiral_kspace, kx, ky, matrix_size)
    
    # Perform inverse FFT
    image_domain = np.fft.ifft2(np.fft.ifftshift(gridded_kspace))
    
    # Calculate deapodization function
    deapod = calculate_deapodization(matrix_size)
    
    # Apply deapodization to correct for kernel effects
    reconstructed_image = image_domain / deapod
    
    return reconstructed_image
