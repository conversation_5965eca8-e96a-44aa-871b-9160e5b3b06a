import vtk
from vtk.util.numpy_support import vtk_to_numpy
import numpy as np
import os

# Fix numpy compatibility
if not hasattr(np, 'bool'):
    np.bool = bool

# Check VTI file contents
test_files = [
    'mrxcat-2.0-main/outputData/test_male169/Tissue_properties_initial_frame/slice_730/Model.vti',
    'mrxcat-2.0-main/outputData/test_male169/Tissue_properties_initial_frame/slice_730/PD.vti',
    'mrxcat-2.0-main/outputData/test_male169/Tissue_properties_initial_frame/slice_730/T1.vti',
    'mrxcat-2.0-main/outputData/test_male169/Tissue_properties_initial_frame/slice_730/T2.vti'
]

for file_path in test_files:
    if os.path.exists(file_path):
        try:
            reader = vtk.vtkXMLImageDataReader()
            reader.SetFileName(file_path)
            reader.Update()
            
            data = reader.GetOutput()
            point_data = data.GetPointData()
            
            print(f'\n=== {os.path.basename(file_path)} ===')
            print(f'Number of arrays: {point_data.GetNumberOfArrays()}')
            
            for i in range(point_data.GetNumberOfArrays()):
                array_name = point_data.GetArrayName(i)
                array = point_data.GetArray(i)
                print(f'  Array {i}: "{array_name}" (size: {array.GetNumberOfTuples()})')
                
        except Exception as e:
            print(f'Error reading {file_path}: {e}')
    else:
        print(f'File not found: {file_path}') 